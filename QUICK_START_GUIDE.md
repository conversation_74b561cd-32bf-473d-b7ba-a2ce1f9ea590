# DVP智能生成系统快速启动指南

## 🚀 快速开始（5分钟启动）

### 前置条件检查

```bash
# 检查Python版本（需要3.8+）
python --version

# 检查当前目录
pwd
# 应该在: /path/to/02.DVP-AI 算法生成 POC/
```

### 一键启动脚本

```bash
# 1. 安装依赖
pip install flask neo4j openai requests

# 2. 初始化Neo4j知识图谱（仅首次运行）
python setup_neo4j_knowledge_graph.py

# 3. 启动API服务器
python dvp_api_server.py &

# 4. 打开用户界面
open enhanced_dvp_interface.html
```

## 📝 第一次使用流程

### 步骤1: 环境验证

1. **检查Neo4j连接**
   - 启动API服务器后，查看控制台输出
   - 看到 "Neo4j连接成功!" 或 "Neo4j模拟连接成功!" 即可

2. **检查LLM API**
   - 观察是否有API调用错误
   - 如有超时，这是正常的，系统有fallback机制

### 步骤2: 界面操作

1. **选择配件**
   - 打开界面后，在配件选择区域勾选需要的配件
   - 配件列表动态从Neo4j加载（如失败会显示默认列表）

2. **配置参数**
   - 设置测试复杂度、测试环境、项目优先级
   - 这些参数基于PDF B列业务逻辑设计

3. **生成DVP方案**
   - 点击"生成DVP方案"按钮
   - 等待系统完成：Neo4j查询 → LLM推理 → 方案生成

### 步骤3: 结果查看

1. **甘特图时间轴**
   - 查看项目排期和时间安排
   - 时间轴支持动态日期处理

2. **项目匹配详情**
   - 查看匹配的试验项目列表
   - 了解每个项目的配件需求和周期

3. **车辆数量分析**
   - 查看LLM推理的车辆计算结果
   - 所有数量都是透明AI计算，无硬编码

## 🔧 常见问题和解决方案

### Q1: Neo4j连接失败怎么办？

**现象**: 看到 "Neo4j驱动未安装，使用模拟连接..."

**解决**:
```bash
# 安装Neo4j驱动
pip install neo4j

# 重新启动API服务器
python dvp_api_server.py
```

**注意**: 即使Neo4j连接失败，系统也会使用模拟模式正常工作。

### Q2: LLM API一直超时？

**现象**: 看到 "LLM API调用错误: The read operation timed out"

**解决**: 这是正常现象，系统有以下fallback机制:
- 使用备用计算方法
- 基于匹配项目数量的智能估算
- 确保系统正常运行

**优化**:
```python
# 在dvp_api_server.py中调整超时时间
timeout=60  # 增加到60秒
```

### Q3: 界面显示空白或加载失败？

**解决**:
```bash
# 1. 确认API服务器运行
curl http://localhost:5000/api/test-connection

# 2. 检查浏览器控制台错误
# 按F12打开开发者工具查看错误信息

# 3. 使用简单HTTP服务器
python -m http.server 8000
# 然后访问 http://localhost:8000/enhanced_dvp_interface.html
```

### Q4: 报告保存失败？

**现象**: "保存报告失败: [Errno 2] No such file or directory: 'reports/...'"

**解决**:
```bash
# 创建reports目录
mkdir -p reports

# 重新运行系统
```

## 📊 系统验证清单

### ✅ 基础功能验证

- [ ] API服务器启动成功（端口5000）
- [ ] 界面可以正常打开
- [ ] 配件列表可以加载
- [ ] 可以选择配件和设置参数
- [ ] 点击生成按钮有响应

### ✅ 数据流验证

- [ ] Neo4j连接状态（成功或模拟模式）
- [ ] LLM推理调用（成功或fallback）
- [ ] 项目匹配结果显示
- [ ] 甘特图正确渲染
- [ ] 车辆数量计算完成

### ✅ 输出验证

- [ ] DVP方案生成完成
- [ ] 报告文件保存成功
- [ ] 数据可追溯到真实源头
- [ ] 没有使用硬编码基线数据

## 🎯 演示场景推荐

### 场景1: ADAS基础配置

**配件选择**: 前摄像头
**预期结果**: 匹配7个相关项目，计算约22台车辆

### 场景2: ADAS L2+配置

**配件选择**: 前摄像头、后摄像头、前毫米波雷达、后毫米波雷达、激光雷达、ADAS控制器ECU、显示屏HMI

**预期结果**: 匹配10个项目，计算约47台车辆

### 场景3: 自定义配置

**配件选择**: 根据具体需求自由组合
**参数调整**: 体验不同参数对结果的影响

## 📈 性能期望

### 正常响应时间

- **配件列表加载**: < 1秒
- **项目匹配查询**: < 2秒  
- **LLM推理计算**: 10-30秒（取决于网络）
- **甘特图渲染**: < 1秒
- **报告生成**: < 1秒

### 系统资源使用

- **内存使用**: 约50-100MB
- **CPU使用**: 正常运行时很低
- **网络流量**: 主要是LLM API调用

## 🛠️ 高级配置

### 修改Neo4j连接

```python
# 在dvp_api_server.py中修改
NEO4J_URI = "bolt://your-neo4j-host:7687"
NEO4J_USERNAME = "your-username"
NEO4J_PASSWORD = "your-password"
```

### 修改LLM配置

```python
# 在dvp_api_server.py中修改
LLM_API_KEY = "your-api-key"
LLM_BASE_URL = "your-llm-endpoint"
```

### 启用调试模式

```python
# 在dvp_api_server.py末尾修改
app.run(debug=True, host='0.0.0.0', port=5000)
```

## 📞 技术支持

### 错误日志收集

```bash
# 查看详细日志
tail -f api_server.log

# 捕获完整错误信息
python dvp_api_server.py 2>&1 | tee error.log
```

### 反馈信息收集

遇到问题时，请提供：
1. 具体错误信息
2. 操作步骤
3. 系统环境信息
4. api_server.log相关内容

---

*本指南帮助您快速体验基于透明数据链路的DVP智能生成系统*