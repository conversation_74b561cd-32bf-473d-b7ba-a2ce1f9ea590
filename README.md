# DVP智能生成系统 POC

## 项目概述
汽车研发制造领域DVP（Design Verification Plan）智慧全自动生成系统。该系统采用**透明数据链路架构**，确保所有决策均可追溯至真实业务数据源：

### 核心能力
- **真实数据驱动**: 基于PDF业务逻辑矩阵的实体关系映射
- **Neo4j知识图谱**: 零件配置与试验项目的精确关系建模
- **LLM智能推理**: 基于图数据库查询结果的AI车辆数量计算
- **完全透明化**: 从PDF → Neo4j → LLM → DVP方案的全链路可追溯

### 数据链路架构
```
PDF业务逻辑矩阵 → Neo4j知识图谱 → LLM推理引擎 → DVP智能方案
      ↓               ↓              ↓            ↓
   真实业务数据    实体关系存储    基于查询推理   透明决策输出
```

## 技术架构
- **后端**: Python Flask API + Neo4j数据库
- **前端**: HTML5 + JavaScript (Linear风格设计)  
- **数据库**: Neo4j知识图谱 (实体关系存储)
- **AI服务**: SiliconCloud API (DeepSeek-R1模型)
- **数据源**: PDF业务逻辑矩阵 (唯一真实数据源)

## 项目文件结构

```
02.DVP-AI 算法生成 POC/
├── README.md                           # 项目说明文档
├── PROJECT_SUMMARY.md                  # 项目总结
├── requirements.txt                    # Python依赖包
├── DVP生成—ADAS检测项目逻辑关系.pdf      # 业务逻辑矩阵文件（真实数据源）
│
├── 核心系统模块/
│   ├── dvp_api_server.py              # 主API服务器（Flask + Neo4j + LLM）
│   ├── enhanced_dvp_interface.html     # 用户界面（Linear风格）
│   └── setup_neo4j_knowledge_graph.py # Neo4j知识图谱初始化脚本
│
├── data/                              # 数据文件目录
│   └── parsed_matrix_data.json        # 解析后的矩阵数据（真实业务数据）
│
├── reports/                           # 报告文件目录
│   └── dvp_report_*.json              # 生成的DVP报告
│
├── archived_legacy_code/              # 归档目录（旧代码）
│   ├── ARCHIVED_README.md             # 归档说明文档
│   ├── deprecated_scripts/            # 已弃用脚本
│   │   ├── dvp_final_demo.py          # 包含硬编码基线数据
│   │   ├── enhanced_dvp_generator.py  # 包含虚假成本逻辑
│   │   ├── final_complete_demo.py     # 包含模拟数据
│   │   └── dvp_generator.py           # 早期版本
│   ├── old_interfaces/                # 旧界面文件
│   │   ├── dvp_interface.html         # 静态配件选择界面
│   │   └── test_components_fix.html   # 测试修复版本
│   ├── demo_data/                     # 演示数据文件
│   │   ├── adas_sample_data.json      # ADAS演示数据
│   │   └── knowledge_graph_data.json  # 模拟知识图谱数据
│   └── hardcoded_logic/               # 硬编码业务逻辑
│
└── claude-code/                       # 开发工具目录
```

## 🏗️ 新架构设计原则

### 1. 数据真实性
- **唯一数据源**: PDF业务逻辑矩阵是系统唯一真实数据来源
- **零硬编码**: 系统不包含任何硬编码的车辆基线数据
- **完全透明**: 所有计算结果均可追溯到数据源

### 2. 透明决策链路
```
用户配件选择 → Neo4j图谱查询 → LLM推理分析 → 车辆数量计算 → DVP方案生成
```

### 3. 架构清晰分层
- **表示层**: `enhanced_dvp_interface.html` (用户交互)
- **业务层**: `dvp_api_server.py` (API服务 + 业务逻辑)
- **数据层**: `Neo4j知识图谱` (实体关系存储)
- **智能层**: `LLM推理引擎` (决策计算)

## 主要功能模块

### 1. Neo4j知识图谱模块
- **文件**: `setup_neo4j_knowledge_graph.py`
- **功能**: 将PDF业务逻辑矩阵转换为Neo4j实体关系图
- **数据结构**: 
  - **节点**: Component (零件配置), TestProject (试验项目)
  - **关系**: REQUIRES (零件需求关系)
  - **属性**: testCycle (试验周期), 但**不包含**基线车辆数据
- **特性**: 完全基于真实业务数据，无虚构信息

### 2. LLM智能推理模块
- **文件**: `dvp_api_server.py` 中的 `llm_dvp_reasoning()` 方法
- **功能**: 基于Neo4j查询结果进行车辆数量智能计算
- **输入**: 用户配件选择 + Neo4j匹配项目 + 三参数业务逻辑
- **输出**: 透明可追溯的车辆数量计算结果
- **特性**: 纯AI推理，无硬编码基线数据

### 3. 用户界面模块
- **文件**: `enhanced_dvp_interface.html`
- **特性**: Linear风格设计，支持暗黑/明亮模式切换
- **动态加载**: 配件信息从Neo4j动态查询，非静态硬编码
- **实时反馈**: 甘特图时间轴、项目匹配可视化
- **三参数逻辑**: 测试复杂度、测试环境、项目优先级基于PDF B列业务逻辑

### 4. API服务模块
- **文件**: `dvp_api_server.py`
- **架构**: Flask + Neo4j + LLM集成
- **端点**: 
  - `/api/get-components` (动态配件查询)
  - `/api/get-test-projects` (动态试验项目查询)
  - `/api/generate-dvp` (DVP方案生成)
- **特性**: 完全透明的数据链路，支持故障回退机制

## 使用方法

### 1. 环境安装
```bash
# 安装Python依赖
pip install -r requirements.txt

# 安装Neo4j Python驱动
pip install neo4j
```

### 2. 初始化Neo4j知识图谱
```bash
# 第一次运行需要初始化Neo4j数据库
python setup_neo4j_knowledge_graph.py
```

### 3. 启动API服务器
```bash
# 启动Flask API服务
python dvp_api_server.py

# 服务器将在 http://localhost:5000 启动
```

### 4. 打开用户界面
```bash
# 在浏览器中打开界面文件
open enhanced_dvp_interface.html

# 或使用Python简单服务器
python -m http.server 8000
# 然后访问 http://localhost:8000/enhanced_dvp_interface.html
```

### 5. 系统使用流程
1. **配件选择**: 在界面中选择车辆配件组合
2. **参数设置**: 配置测试复杂度、测试环境、项目优先级
3. **DVP生成**: 点击生成按钮，系统将：
   - 查询Neo4j匹配相关试验项目
   - 调用LLM进行智能车辆数量计算
   - 生成包含甘特图的完整DVP方案
4. **结果查看**: 查看生成的DVP方案和详细报告

## 核心算法流程

### 新架构透明数据链路

1. **用户配件选择**: 用户在界面中选择车辆配件组合
   ```
   输入: ["前摄像头", "毫米波雷达", "ADAS控制器ECU"]
   ```

2. **Neo4j图谱查询**: 基于用户选择查询相关试验项目
   ```cypher
   MATCH (c:Component)-[:REQUIRES]-(t:TestProject) 
   WHERE c.name IN $user_components 
   RETURN t.name, t.testCycle, collect(c.name) as matched_components
   ```

3. **LLM智能推理**: 将Neo4j查询结果输入LLM进行推理
   ```python
   # 输入: Neo4j查询结果 + 用户三参数逻辑
   # 输出: 透明的车辆数量计算过程和结果
   llm_response = llm_dvp_reasoning(neo4j_data, user_params)
   ```

4. **DVP方案生成**: 基于LLM推理结果生成完整方案
   ```
   输出: 包含甘特图、项目匹配、车辆分配的完整DVP方案
   ```

### 透明性保证
- **数据源追溯**: 每个结果都可以追溯到PDF业务逻辑矩阵
- **计算过程公开**: LLM推理过程完全透明，无黑盒操作
- **无硬编码数据**: 系统不包含任何预设的车辆基线数据

## 系统特性

### 🎨 用户界面
- **现代设计**: 采用Linear应用风格，简洁美观
- **主题切换**: 支持暗黑模式和明亮模式
- **动态加载**: 配件信息从Neo4j动态查询，非静态硬编码
- **实时反馈**: 甘特图、项目匹配、车辆分配的实时可视化

### 🧠 AI能力
- **透明推理**: 基于DeepSeek-R1模型的完全透明逻辑推理
- **智能计算**: 纯AI车辆数量计算，无硬编码基线数据
- **知识图谱**: 基于Neo4j的复杂关系网络深度分析
- **可追溯性**: 每个决策都可以追溯到原始业务数据

### 📊 数据可视化
- **甘特图**: 项目时间轴可视化，支持动态日期处理
- **项目匹配**: 实时显示配件与试验项目的匹配关系
- **透明计算**: 显示LLM推理过程和车辆数量计算逻辑

## 演示结果示例

基于新架构的系统运行后的典型输出：
- **配置类型**: ADAS L2+级自动驾驶
- **匹配项目**: 基于Neo4j查询的真实匹配项目
- **所需车辆**: 基于LLM推理的透明计算结果
- **测试周期**: 基于真实test_cycle数据的智能排期
- **数据源**: 100%来自PDF业务逻辑矩阵，无虚构数据

### 新架构 vs 旧架构对比
| 特性 | 旧架构（已归档） | 新架构（当前） |
|------|----------------|---------------|
| 数据源 | 硬编码JSON + 虚构数据 | PDF业务逻辑矩阵 |
| 车辆计算 | 硬编码基线数据 | LLM纯AI推理 |
| 配件加载 | 静态硬编码列表 | Neo4j动态查询 |
| 透明性 | 无法追溯数据源 | 完全透明可追溯 |
| 可信度 | 包含虚构信息 | 100%真实业务数据 |

## 技术配置

### API服务
- **Neo4j**: bolt://115.227.22.242:27687 (知识图谱数据库)
- **SiliconCloud**: https://api.siliconflow.cn/v1 (LLM推理服务)
- **AI模型**: deepseek-ai/DeepSeek-R1 (智能推理模型)

### 开发环境
- **Python**: 3.8+ (Flask + Neo4j驱动)
- **Neo4j**: 图数据库 (实体关系存储)
- **浏览器**: 支持ES6+的现代浏览器

## 项目状态

✅ **架构重构**: 完成从硬编码到透明数据链路的重构  
✅ **Neo4j集成**: 完成知识图谱数据建模和查询  
✅ **LLM推理**: 完成基于Neo4j数据的智能推理  
✅ **用户界面**: 完成Linear风格界面和动态数据加载  
✅ **代码归档**: 完成所有不合规代码的归档和说明  

## 重要提醒

### ⚠️ 数据完整性保证
- 系统现在**完全基于真实业务数据**，不包含任何虚构信息
- 所有车辆数量计算均通过**LLM透明推理**，无预设基线数据
- 配件信息从**Neo4j动态查询**，确保数据一致性

### 🔄 下一步操作建议
1. **安装Neo4j驱动**: `pip install neo4j`
2. **初始化知识图谱**: `python setup_neo4j_knowledge_graph.py`
3. **启动API服务**: `python dvp_api_server.py`
4. **测试完整流程**: 验证PDF → Neo4j → LLM → DVP的完整数据链路

### 📋 待优化项目
- [ ] 解决LLM API超时问题
- [ ] 创建reports目录确保报告保存
- [ ] 优化Neo4j连接稳定性
- [ ] 添加更多错误处理和重试机制

## 技术支持

### 文档参考
- 📁 `archived_legacy_code/ARCHIVED_README.md` - 归档代码说明
- 📊 `reports/` 目录 - 生成的DVP报告示例
- 🗃️ `data/parsed_matrix_data.json` - 真实业务数据结构

### 故障排除
1. **Neo4j连接问题**: 检查网络连接和凭据配置
2. **LLM API超时**: 调整timeout参数或检查API服务状态
3. **报告保存失败**: 确保reports目录存在
4. **界面数据加载问题**: 确认API服务器正常运行

### 联系方式
如遇技术问题，请提供具体错误信息和复现步骤。

---
*本项目为POC概念验证版本，基于透明数据链路架构，展示DVP智能生成的核心技术能力*