 * Tip: There are .env files present. Install python-dotenv to use them.
矩阵数据加载成功: data/parsed_matrix_data.json
知识图谱数据加载成功: data/knowledge_graph_data.json
✅ 矩阵数据加载成功: data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5001
 * Serving Flask app 'dvp_api_server'
 * Debug mode: on
[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://**********:5001
[33mPress CTRL+C to quit[0m
 * Restarting with stat
 * Tip: There are .env files present. Install python-dotenv to use them.
 * Debugger is active!
 * Debugger PIN: 281-256-029
127.0.0.1 - - [08/Jul/2025 15:12:37] "GET /api/test-connection HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 15:21:36] "GET /api/get-components HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 15:22:01] "GET /api/get-test-projects HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 15:22:41] "[35m[1mPOST /api/generate-dvp HTTP/1.1[0m" 500 -
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载成功: data/parsed_matrix_data.json
知识图谱数据加载成功: data/knowledge_graph_data.json
✅ 矩阵数据加载成功: data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5001
LLM API调用错误: The read operation timed out
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
🔍 从Neo4j数据库查询配件信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 10 个配件
🔍 从Neo4j数据库查询测试项目信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 13 个测试项目
🎯 收到DVP生成请求: ADAS L2+配置
🚀 开始生成DVP方案: ADAS L2+配置
📅 DVP执行时间窗口: 2025-01-08 → 2025-07-08
🔍 查询Neo4j数据库匹配试验项目...
🎯 用户选择的配件组合: ['AD摄像头前视/后视/侧视', '毫米波雷达', '激光雷达']
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 9 个匹配项目
🎯 过滤后保留 9 个高质量匹配项目 (阈值: 0.3)
📊 从Neo4j数据库匹配到 9 个试验项目
🤖 开始LLM DVP推理生成...
🤖 发送推理请求给LLM...
LLM API调用错误: The read operation timed out
🤖 LLM推理完成，响应长度: 37 字符
⚠️ LLM响应格式不正确，使用备用计算
📊 使用备用车辆计算方法...
🚗 LLM推理生成车辆数量: 12 台
⏰ 排期时间窗口: 2025-01-08 至 2025-07-08 (181天)
⚠️ AI排期计算失败: 'complexity'
❌ DVP生成失败: 'complexity'
 * Restarting with stat
 * Tip: There are .env files present. Install python-dotenv to use them.
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载成功: data/parsed_matrix_data.json
知识图谱数据加载成功: data/knowledge_graph_data.json
✅ 矩阵数据加载成功: data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5001
 * Restarting with stat
 * Tip: There are .env files present. Install python-dotenv to use them.
 * Debugger is active!
 * Debugger PIN: 281-256-029
127.0.0.1 - - [08/Jul/2025 15:47:09] "GET /api/get-test-projects HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 15:47:37] "GET /api/get-test-projects HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 15:49:00] "GET /api/test-connection HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 15:49:19] "GET /api/get-components HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 15:49:22] "GET /api/get-test-projects HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 66} for query: '\n                        MATCH (n)\n                        RETURN id(n) as id, labels(n) as labels, properties(n) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 75} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 52, offset: 95} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
127.0.0.1 - - [08/Jul/2025 15:49:32] "GET /api/get-knowledge-graph HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 66} for query: '\n                        MATCH (n)\n                        RETURN id(n) as id, labels(n) as labels, properties(n) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 75} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 52, offset: 95} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
127.0.0.1 - - [08/Jul/2025 15:50:44] "GET /api/get-knowledge-graph HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 15:52:21] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 15:52:38] "[35m[1mPOST /api/generate-dvp HTTP/1.1[0m" 500 -
127.0.0.1 - - [08/Jul/2025 15:52:50] "GET /api/get-test-projects HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 15:52:51] "GET /api/get-components HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 15:52:59] "GET /api/test-connection HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 66} for query: '\n                        MATCH (n)\n                        RETURN id(n) as id, labels(n) as labels, properties(n) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 75} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 52, offset: 95} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
127.0.0.1 - - [08/Jul/2025 15:54:07] "GET /api/get-knowledge-graph HTTP/1.1" 200 -
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载成功: data/parsed_matrix_data.json
知识图谱数据加载成功: data/knowledge_graph_data.json
✅ 矩阵数据加载成功: data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5001
🔍 从Neo4j数据库查询测试项目信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 13 个测试项目
🔍 从Neo4j数据库查询测试项目信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 13 个测试项目
LLM API调用错误: The read operation timed out
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
🔍 从Neo4j数据库查询配件信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
🔍 从Neo4j数据库查询测试项目信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
Neo4j连接失败: Couldn't connect to **************:27687 (resolved to ('**************:27687',)):
Connection to **************:27687 closed with incomplete handshake response
📋 使用备用数据源...
✅ 从备用数据源查询到 10 个配件
✅ 从Neo4j查询到 13 个测试项目
🔍 从Neo4j数据库查询知识图谱数据...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 23 个节点, 55 个关系
🔍 从Neo4j数据库查询知识图谱数据...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 23 个节点, 55 个关系
🎯 收到DVP生成请求: ADAS L2级配置
🚀 开始生成DVP方案: ADAS L2级配置
📅 DVP执行时间窗口: 2025-07-08 → 2026-01-08
🔍 查询Neo4j数据库匹配试验项目...
🎯 用户选择的配件组合: ['AD摄像头前视/后视/侧视', 'DPB+ESP/IPB', 'EPS', '毫米波雷达']
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 8 个匹配项目
🎯 过滤后保留 7 个高质量匹配项目 (阈值: 0.3)
📊 从Neo4j数据库匹配到 7 个试验项目
🤖 开始LLM DVP推理生成...
🤖 发送推理请求给LLM...
LLM API调用错误: The read operation timed out
🤖 LLM推理完成，响应长度: 37 字符
⚠️ LLM响应格式不正确，使用备用计算
📊 使用备用车辆计算方法...
🚗 LLM推理生成车辆数量: 12 台
⏰ 排期时间窗口: 2025-07-08 至 2026-01-08 (184天)
⚠️ AI排期计算失败: 'complexity'
❌ DVP生成失败: 'complexity'
🔍 从Neo4j数据库查询配件信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
🔍 从Neo4j数据库查询测试项目信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
Neo4j连接失败: Couldn't connect to **************:27687 (resolved to ('**************:27687',)):
Connection to **************:27687 closed with incomplete handshake response
📋 使用备用数据源...
✅ 从备用数据源查询到 13 个测试项目
✅ 从Neo4j查询到 10 个配件
LLM API调用错误: The read operation timed out
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
🔍 从Neo4j数据库查询知识图谱数据...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 23 个节点, 55 个关系
 * Restarting with stat
 * Tip: There are .env files present. Install python-dotenv to use them.
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载成功: data/parsed_matrix_data.json
知识图谱数据加载成功: data/knowledge_graph_data.json
✅ 矩阵数据加载成功: data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5001
 * Restarting with stat
 * Tip: There are .env files present. Install python-dotenv to use them.
 * Debugger is active!
 * Debugger PIN: 281-256-029
127.0.0.1 - - [08/Jul/2025 16:12:13] "GET /api/get-knowledge-graph HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 16:37:28] "GET /api/get-components HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 16:37:28] "GET /api/get-test-projects HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 16:37:34] "GET /api/get-knowledge-graph HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 16:39:00] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 16:39:16] "[35m[1mPOST /api/generate-dvp HTTP/1.1[0m" 500 -
