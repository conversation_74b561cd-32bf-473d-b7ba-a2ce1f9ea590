# 归档代码说明文档

## 📁 归档原因

本目录包含了在Neo4j知识图谱架构重构过程中被替换的旧代码，这些代码包含硬编码逻辑、虚假数据源或不符合新的透明数据链路要求。

## 🗂️ 归档结构

### `/deprecated_scripts/` - 已弃用的脚本文件
- **`dvp_demo.py`** - 早期演示脚本，包含硬编码的车辆数量
- **`dvp_final_demo.py`** - 包含虚假成本数据的最终演示版本
- **`final_complete_demo.py`** - 完整演示版本，但依赖硬编码逻辑
- **`dvp_generator.py`** - 原始DVP生成器，使用静态数据
- **`enhanced_dvp_generator.py`** - 增强版生成器，但仍包含基线车辆数据

**弃用原因**: 这些脚本包含硬编码的车辆基线数据，存在"作弊"嫌疑，不符合透明性要求。

### `/old_interfaces/` - 旧的用户界面
- **`dvp_interface.html`** - 原始HTML界面，包含静态配件选择
- **`test_components_fix.html`** - 测试修复版本，仍使用硬编码组件

**弃用原因**: 界面使用硬编码的配件列表，不支持从Neo4j动态加载。

### `/demo_data/` - 演示数据文件
- **`adas_sample_data.json`** - ADAS演示数据，包含虚构的项目信息
- **`knowledge_graph_data.json`** - 模拟知识图谱数据，包含虚假关系

**弃用原因**: 包含虚构的业务数据，不反映真实的PDF业务逻辑。

### `/hardcoded_logic/` - 硬编码业务逻辑
*（将在进一步代码审查后添加）*

## ⚠️ 重要警告

### 不要使用归档代码的原因：

1. **数据真实性问题**
   - 包含硬编码的车辆基线数量
   - 使用虚构的成本估算数据
   - 依赖模拟的业务逻辑关系

2. **透明性缺失**
   - 结果无法追溯到真实数据源
   - 存在"作弊"嫌疑
   - 不符合业务规范要求

3. **架构不兼容**
   - 不支持Neo4j知识图谱
   - 缺乏LLM推理能力
   - 无法适应动态数据变化

## 🔄 迁移到新架构

如果需要参考旧代码的某些逻辑，请注意：

### ✅ 可以参考的部分：
- 用户界面布局设计
- 甘特图渲染逻辑
- 错误处理模式
- API接口设计思路

### ❌ 不能使用的部分：
- 任何硬编码的数值数据
- 静态的配件和项目列表
- 虚构的成本计算逻辑
- 基线车辆数量配置

## 📋 新架构对比

| 功能模块 | 旧架构（已归档） | 新架构（当前使用） |
|---------|----------------|------------------|
| 数据源 | 硬编码JSON文件 | Neo4j知识图谱 |
| 配件信息 | 静态列表 | 动态查询 |
| 车辆计算 | 硬编码基线 | LLM推理生成 |
| 项目匹配 | 模糊匹配 | 精确图谱查询 |
| 透明性 | 无法追溯 | 完全透明 |
| 扩展性 | 有限 | 高度可扩展 |

## 🔍 代码审计记录

### 已识别的问题代码模式：

1. **硬编码车辆数量**
   ```python
   # ❌ 旧代码模式
   vehicle_baselines = {
       "感知开发": 2,
       "产线标定": 1,
       # ...
   }
   ```

2. **虚构成本数据**
   ```python
   # ❌ 旧代码模式
   cost_per_vehicle = 180000  # 虚构数据
   facility_cost = 900000     # 虚构数据
   ```

3. **静态配件列表**
   ```javascript
   // ❌ 旧代码模式
   const components = ["前摄像头", "ADAS控制器ECU"]; // 硬编码
   ```

### 新架构的改进：

1. **动态数据查询**
   ```python
   # ✅ 新代码模式
   components = query_neo4j_components()
   matched_projects = query_neo4j_projects(user_components)
   ```

2. **LLM推理计算**
   ```python
   # ✅ 新代码模式
   vehicle_count = llm_reasoning(neo4j_data, user_params)
   ```

3. **透明数据链路**
   ```
   PDF业务逻辑 → Neo4j图谱 → LLM推理 → DVP方案
   ```

## 📝 归档日志

| 日期 | 归档内容 | 原因 | 负责人 |
|------|---------|------|--------|
| 2025-07-07 | deprecated_scripts/ | 包含硬编码基线数据 | 系统重构 |
| 2025-07-07 | old_interfaces/ | 静态配件选择界面 | 系统重构 |
| 2025-07-07 | demo_data/ | 虚构演示数据 | 系统重构 |

## 🚀 未来计划

1. **完全移除归档代码**：在新架构稳定运行3个月后
2. **代码审计报告**：生成完整的代码质量审计报告
3. **最佳实践文档**：基于重构经验编写最佳实践指南

---

**注意**: 此归档目录仅供参考和审计使用，严禁在生产环境中使用任何归档代码。所有新的开发工作都应基于Neo4j知识图谱架构进行。