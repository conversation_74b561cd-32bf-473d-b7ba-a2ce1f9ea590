# -*- coding: utf-8 -*-
"""
DVP智能生成系统演示
整合所有功能模块的完整演示
"""

import json
import os
from datetime import datetime, timedelta

# 模拟导入我们的模块
class DVPDemo:
    """DVP演示类"""
    
    def __init__(self):
        self.sample_data = None
        self.load_sample_data()
    
    def load_sample_data(self):
        """加载示例数据"""
        try:
            with open("adas_sample_data.json", "r") as f:
                self.sample_data = json.load(f)
            print("示例数据加载成功")
        except:
            print("示例数据加载失败，使用默认数据")
            self.sample_data = {
                "adas_components": ["前摄像头", "毫米波雷达", "激光雷达"],
                "test_projects": [
                    {
                        "project_name": "ACC自适应巡航",
                        "required_components": ["前摄像头", "毫米波雷达"],
                        "test_duration": 15,
                        "vehicle_count": 2
                    }
                ]
            }
    
    def simulate_configuration_matching(self, vehicle_config):
        """模拟配置匹配试验项目"""
        print("\n=== 配置匹配试验项目 ===")
        print("车辆配置: {}".format(vehicle_config["config_name"]))
        print("配置组件: {}".format(", ".join(vehicle_config["components"])))
        
        matched_projects = []
        
        # 模拟匹配逻辑
        for project in self.sample_data["test_projects"]:
            project_components = project.get("required_components", [])
            
            # 检查是否有匹配的组件
            match_found = False
            for comp in project_components:
                if comp in vehicle_config["components"]:
                    match_found = True
                    break
            
            if match_found:
                matched_projects.append(project)
                print("匹配项目: {} (需要: {})".format(
                    project["project_name"],
                    ", ".join(project_components)
                ))
        
        return matched_projects
    
    def simulate_vehicle_count_calculation(self, test_projects):
        """模拟车辆数量计算"""
        print("\n=== 车辆数量计算 ===")
        
        # 简单的计算逻辑
        base_count = len(test_projects)  # 基础车辆数
        
        # 根据测试复杂度调整
        complexity_factor = 1
        for project in test_projects:
            if len(project.get("required_components", [])) > 2:
                complexity_factor += 0.5
        
        # 考虑测试周期重叠
        total_duration = sum([p.get("test_duration", 0) for p in test_projects])
        if total_duration > 30:  # 如果总测试时间超过30天
            complexity_factor += 1
        
        # 风险缓解备用车辆
        backup_vehicles = 1 if len(test_projects) > 2 else 0
        
        total_vehicles = int(base_count * complexity_factor) + backup_vehicles
        
        print("基础车辆数: {}".format(base_count))
        print("复杂度系数: {}".format(complexity_factor))
        print("备用车辆: {}".format(backup_vehicles))
        print("总计车辆数: {}".format(total_vehicles))
        
        return total_vehicles
    
    def simulate_test_scheduling(self, test_projects, sop_date, total_vehicles):
        """模拟测试排期"""
        print("\n=== 测试排期生成 ===")
        print("SOP日期: {}".format(sop_date))
        print("可用车辆: {}台".format(total_vehicles))
        
        # 计算总测试时间
        total_test_days = sum([p.get("test_duration", 0) for p in test_projects])
        
        # 考虑并行测试
        parallel_efficiency = 0.7 if total_vehicles > 1 else 1.0
        actual_duration = int(total_test_days * parallel_efficiency)
        
        # 从SOP日期反推开始时间
        sop_datetime = datetime.strptime(sop_date, "%Y-%m-%d")
        start_date = sop_datetime - timedelta(days=actual_duration + 30)  # 加30天缓冲
        
        print("总测试时间: {}天".format(total_test_days))
        print("并行效率: {}".format(parallel_efficiency))
        print("实际测试周期: {}天".format(actual_duration))
        print("建议开始时间: {}".format(start_date.strftime("%Y-%m-%d")))
        
        # 生成详细排期
        schedule = {
            "start_date": start_date.strftime("%Y-%m-%d"),
            "end_date": (start_date + timedelta(days=actual_duration)).strftime("%Y-%m-%d"),
            "sop_date": sop_date,
            "total_duration": actual_duration,
            "buffer_days": 30,
            "vehicle_allocation": total_vehicles,
            "projects": []
        }
        
        # 为每个项目分配时间段
        current_date = start_date
        for i, project in enumerate(test_projects):
            project_duration = project.get("test_duration", 0)
            project_end = current_date + timedelta(days=project_duration)
            
            project_schedule = {
                "project_name": project["project_name"],
                "start_date": current_date.strftime("%Y-%m-%d"),
                "end_date": project_end.strftime("%Y-%m-%d"),
                "duration": project_duration,
                "allocated_vehicles": 1 if total_vehicles >= len(test_projects) else total_vehicles // len(test_projects)
            }
            
            schedule["projects"].append(project_schedule)
            print("项目 {}: {} ~ {} ({}天)".format(
                project["project_name"],
                project_schedule["start_date"],
                project_schedule["end_date"],
                project_duration
            ))
            
            # 下一个项目开始时间（考虑一些重叠）
            current_date = current_date + timedelta(days=max(1, project_duration // 2))
        
        return schedule
    
    def generate_dvp_report(self, vehicle_config, matched_projects, total_vehicles, schedule):
        """生成DVP报告"""
        print("\n=== DVP方案生成报告 ===")
        
        report = {
            "dvp_id": "DVP_{}".format(datetime.now().strftime("%Y%m%d_%H%M%S")),
            "generated_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "vehicle_config": vehicle_config,
            "matched_projects": matched_projects,
            "total_vehicles": total_vehicles,
            "test_schedule": schedule,
            "summary": {
                "total_projects": len(matched_projects),
                "estimated_cost": total_vehicles * 100000,  # 假设每台车10万成本
                "total_test_duration": schedule["total_duration"],
                "risk_level": "中等" if total_vehicles > 3 else "低"
            }
        }
        
        # 保存报告
        report_filename = "dvp_report_{}.json".format(report["dvp_id"])
        with open(report_filename, "w") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("DVP方案ID: {}".format(report["dvp_id"]))
        print("项目数量: {}".format(report["summary"]["total_projects"]))
        print("车辆数量: {}台".format(total_vehicles))
        print("预估成本: {} 万元".format(report["summary"]["estimated_cost"] / 10000))
        print("测试周期: {}天".format(report["summary"]["total_test_duration"]))
        print("风险等级: {}".format(report["summary"]["risk_level"]))
        print("报告已保存: {}".format(report_filename))
        
        return report
    
    def run_demo(self):
        """运行完整演示"""
        print("=== DVP智能生成系统演示 ===")
        print("汽车研发制造领域DVP智慧全自动生成")
        print("=" * 50)
        
        # 示例车辆配置
        vehicle_config = {
            "config_id": "CONFIG_ADAS_L2",
            "config_name": "ADAS L2级自动驾驶配置",
            "components": ["前摄像头", "毫米波雷达", "激光雷达", "控制器ECU"],
            "level": "L2+",
            "target_market": "高端车型"
        }
        
        # 示例SOP日期
        sop_date = "2024-12-31"
        
        # 1. 配置匹配试验项目
        matched_projects = self.simulate_configuration_matching(vehicle_config)
        
        # 2. 计算车辆数量
        total_vehicles = self.simulate_vehicle_count_calculation(matched_projects)
        
        # 3. 生成测试排期
        schedule = self.simulate_test_scheduling(matched_projects, sop_date, total_vehicles)
        
        # 4. 生成DVP报告
        report = self.generate_dvp_report(vehicle_config, matched_projects, total_vehicles, schedule)
        
        print("\n=== 演示完成 ===")
        print("完整的DVP方案已生成！")
        
        return report

def main():
    """主函数"""
    demo = DVPDemo()
    demo.run_demo()

if __name__ == "__main__":
    main()