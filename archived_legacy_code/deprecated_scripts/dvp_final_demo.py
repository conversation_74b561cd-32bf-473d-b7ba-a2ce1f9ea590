# -*- coding: utf-8 -*-
"""
DVP最终演示版本
"""

import json
import os
from datetime import datetime, timed<PERSON><PERSON>

def create_sample_data():
    """创建示例数据"""
    sample_data = {
        "adas_components": [
            "前摄像头",
            "后摄像头", 
            "毫米波雷达",
            "激光雷达",
            "超声波传感器",
            "控制器ECU"
        ],
        "test_projects": [
            {
                "project_name": "ACC自适应巡航",
                "required_components": ["前摄像头", "毫米波雷达"],
                "test_duration": 15,
                "vehicle_count": 2
            },
            {
                "project_name": "AEB自动紧急制动",
                "required_components": ["前摄像头", "毫米波雷达"],
                "test_duration": 10,
                "vehicle_count": 1
            },
            {
                "project_name": "BSD盲点监测",
                "required_components": ["后摄像头", "毫米波雷达"],
                "test_duration": 8,
                "vehicle_count": 1
            },
            {
                "project_name": "LDW车道偏离警告",
                "required_components": ["前摄像头"],
                "test_duration": 5,
                "vehicle_count": 1
            }
        ],
        "vehicle_count_rules": [
            "单一功能测试：1台车",
            "组合功能测试：2台车",
            "长期耐久测试：3台车"
        ]
    }
    return sample_data

def match_test_projects(vehicle_config, sample_data):
    """匹配测试项目"""
    print("\n=== 配置匹配试验项目 ===")
    print("车辆配置: {}".format(vehicle_config["config_name"]))
    print("配置组件: {}".format(", ".join(vehicle_config["components"])))
    
    matched_projects = []
    
    # 简化匹配逻辑
    config_components = vehicle_config["components"]
    
    for project in sample_data["test_projects"]:
        project_components = project.get("required_components", [])
        
        # 检查匹配
        has_match = False
        for req_comp in project_components:
            for config_comp in config_components:
                if req_comp == config_comp:
                    has_match = True
                    break
            if has_match:
                break
        
        if has_match:
            matched_projects.append(project)
            print("匹配项目: {} (需要: {})".format(
                project["project_name"],
                ", ".join(project_components)
            ))
    
    return matched_projects

def calculate_vehicle_count(test_projects):
    """计算车辆数量"""
    print("\n=== 车辆数量计算 ===")
    
    if not test_projects:
        print("没有匹配的测试项目")
        return 0
    
    # 基础车辆数
    base_count = len(test_projects)
    print("基础车辆数: {}".format(base_count))
    
    # 复杂度调整
    complexity_bonus = 0
    for project in test_projects:
        if len(project.get("required_components", [])) > 1:
            complexity_bonus += 1
    
    print("复杂度加成: {}".format(complexity_bonus))
    
    # 备用车辆
    backup = 1 if len(test_projects) > 2 else 0
    print("备用车辆: {}".format(backup))
    
    total = base_count + complexity_bonus + backup
    print("总计车辆数: {}".format(total))
    
    return total

def generate_test_schedule(test_projects, sop_date, total_vehicles):
    """生成测试排期"""
    print("\n=== 测试排期生成 ===")
    print("SOP日期: {}".format(sop_date))
    print("可用车辆: {}台".format(total_vehicles))
    
    if not test_projects:
        return {"message": "没有测试项目"}
    
    # 计算总测试时间
    total_test_days = sum([p.get("test_duration", 0) for p in test_projects])
    print("总测试时间: {}天".format(total_test_days))
    
    # 并行效率
    parallel_efficiency = 0.6 if total_vehicles > 1 else 1.0
    actual_duration = int(total_test_days * parallel_efficiency)
    print("考虑并行后实际周期: {}天".format(actual_duration))
    
    # 反推开始时间
    sop_datetime = datetime.strptime(sop_date, "%Y-%m-%d")
    start_date = sop_datetime - timedelta(days=actual_duration + 30)
    
    print("建议开始时间: {}".format(start_date.strftime("%Y-%m-%d")))
    
    schedule = {
        "start_date": start_date.strftime("%Y-%m-%d"),
        "end_date": (start_date + timedelta(days=actual_duration)).strftime("%Y-%m-%d"),
        "sop_date": sop_date,
        "total_duration": actual_duration,
        "projects": test_projects
    }
    
    return schedule

def generate_dvp_report(vehicle_config, matched_projects, total_vehicles, schedule):
    """生成DVP报告"""
    print("\n=== DVP方案生成 ===")
    
    dvp_id = "DVP_{}".format(datetime.now().strftime("%Y%m%d_%H%M%S"))
    
    report = {
        "dvp_id": dvp_id,
        "generated_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "vehicle_config": vehicle_config,
        "matched_projects": matched_projects,
        "total_vehicles": total_vehicles,
        "test_schedule": schedule,
        "summary": {
            "total_projects": len(matched_projects),
            "estimated_cost": total_vehicles * 10,  # 假设每台车10万元
            "total_test_duration": schedule.get("total_duration", 0),
            "risk_level": "中等" if total_vehicles > 3 else "低"
        }
    }
    
    # 保存报告
    report_filename = "dvp_report_{}.json".format(dvp_id)
    with open(report_filename, "w") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print("DVP方案ID: {}".format(dvp_id))
    print("项目数量: {}".format(len(matched_projects)))
    print("车辆数量: {}台".format(total_vehicles))
    print("预估成本: {}万元".format(total_vehicles * 10))
    print("测试周期: {}天".format(schedule.get("total_duration", 0)))
    print("风险等级: {}".format(report["summary"]["risk_level"]))
    print("报告已保存: {}".format(report_filename))
    
    return report

def main():
    """主演示函数"""
    print("=== DVP智能生成系统演示 ===")
    print("汽车研发制造领域DVP智慧全自动生成")
    print("=" * 50)
    
    # 创建示例数据
    sample_data = create_sample_data()
    
    # 示例车辆配置
    vehicle_config = {
        "config_id": "CONFIG_ADAS_L2",
        "config_name": "ADAS L2级自动驾驶配置",
        "components": ["前摄像头", "毫米波雷达", "激光雷达", "控制器ECU"],
        "level": "L2+",
        "target_market": "高端车型"
    }
    
    # SOP日期
    sop_date = "2024-12-31"
    
    # 1. 配置匹配试验项目
    matched_projects = match_test_projects(vehicle_config, sample_data)
    
    # 2. 计算车辆数量
    total_vehicles = calculate_vehicle_count(matched_projects)
    
    # 3. 生成测试排期
    schedule = generate_test_schedule(matched_projects, sop_date, total_vehicles)
    
    # 4. 生成DVP报告
    report = generate_dvp_report(vehicle_config, matched_projects, total_vehicles, schedule)
    
    print("\n=== 演示完成 ===")
    print("完整的DVP方案已生成！")
    print("您可以查看生成的JSON报告文件获取详细信息。")
    
    return report

if __name__ == "__main__":
    main()