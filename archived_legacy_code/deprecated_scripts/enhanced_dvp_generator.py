# -*- coding: utf-8 -*-
"""
增强版DVP生成器
基于PDF矩阵解析结果的智能DVP生成
"""

import json
import os
from datetime import datetime, timedelta
from collections import defaultdict

class EnhancedDVPGenerator:
    """增强版DVP生成器"""
    
    def __init__(self):
        self.matrix_data = None
        self.kg_data = None
        self.load_matrix_data()
    
    def load_matrix_data(self):
        """加载矩阵数据"""
        try:
            # 尝试不同路径加载数据文件
            data_paths = [
                "data/parsed_matrix_data.json",
                "parsed_matrix_data.json",
                "../parsed_matrix_data.json"
            ]
            
            kg_paths = [
                "data/knowledge_graph_data.json", 
                "knowledge_graph_data.json",
                "../knowledge_graph_data.json"
            ]
            
            # 加载矩阵数据
            for path in data_paths:
                try:
                    with open(path, "r", encoding='utf-8') as f:
                        self.matrix_data = json.load(f)
                    print(f"矩阵数据加载成功: {path}")
                    break
                except FileNotFoundError:
                    continue
            else:
                raise FileNotFoundError("找不到parsed_matrix_data.json文件")
            
            # 加载知识图谱数据
            for path in kg_paths:
                try:
                    with open(path, "r", encoding='utf-8') as f:
                        self.kg_data = json.load(f)
                    print(f"知识图谱数据加载成功: {path}")
                    break
                except FileNotFoundError:
                    continue
            else:
                print("知识图谱数据文件未找到，将使用内置数据")
                self.kg_data = {}
                
        except Exception as e:
            print("矩阵数据加载失败：{}".format(str(e)))
            return False
        return True
    
    def match_test_projects_by_components(self, vehicle_components):
        """根据组件匹配试验项目（基于矩阵关系）"""
        print("\n=== 基于矩阵关系匹配试验项目 ===")
        print("输入组件：{}".format(", ".join(vehicle_components)))
        
        matched_projects = []
        
        # 统计每个试验项目的匹配度
        project_matches = defaultdict(list)
        
        for relationship in self.matrix_data["relationships"]:
            if relationship["relationship"] == "✅":
                test_project = relationship["test_project"]
                component = relationship["component"]
                
                if component in vehicle_components:
                    project_matches[test_project].append(component)
        
        # 筛选出有匹配的项目
        for project, matched_components in project_matches.items():
            if matched_components:  # 至少有一个组件匹配
                project_info = {
                    "project_name": project,
                    "matched_components": matched_components,
                    "test_cycle": self.matrix_data["test_cycles"].get(project, 0),
                    "vehicle_baseline": self.matrix_data["vehicle_baselines"].get(project, 1),
                    "logic_description": self.matrix_data["logic_descriptions"].get(project, ""),
                    "match_score": len(matched_components)
                }
                matched_projects.append(project_info)
                
                print("匹配项目：{} (匹配组件：{}, 基线车辆：{}, 周期：{}天)".format(
                    project, 
                    ", ".join(matched_components),
                    project_info["vehicle_baseline"],
                    project_info["test_cycle"]
                ))
        
        # 按匹配度排序
        matched_projects.sort(key=lambda x: x["match_score"], reverse=True)
        
        print("共匹配{}个试验项目".format(len(matched_projects)))
        return matched_projects
    
    def calculate_vehicles_with_matrix_logic(self, matched_projects, variation_factors=None):
        """基于矩阵逻辑计算车辆数量"""
        print("\n=== 基于矩阵逻辑计算车辆数量 ===")
        
        if not matched_projects:
            print("没有匹配的项目")
            return 0
        
        # 1. 基础车辆数量（来自G列）
        base_vehicles = {}
        total_baseline = 0
        
        for project in matched_projects:
            baseline = project["vehicle_baseline"]
            base_vehicles[project["project_name"]] = baseline
            total_baseline += baseline
            print("项目 {} 基线车辆：{}台".format(project["project_name"], baseline))
        
        print("基线车辆总计：{}台".format(total_baseline))
        
        # 2. 变化因素调整
        variation_multiplier = 1.0
        if variation_factors:
            # 根据变化因素数量调整
            factor_count = len(variation_factors)
            if factor_count > 4:
                variation_multiplier = 1.5
            elif factor_count > 2:
                variation_multiplier = 1.3
            else:
                variation_multiplier = 1.1
            
            print("变化因素：{}个，调整系数：{}".format(factor_count, variation_multiplier))
        
        # 3. 项目复杂度调整
        complexity_bonus = 0
        high_complexity_projects = []
        
        for project in matched_projects:
            # 基于逻辑描述判断复杂度
            logic = project["logic_description"]
            if any(keyword in logic for keyword in ["复杂", "多场景", "高风险", "多种"]):
                complexity_bonus += 1
                high_complexity_projects.append(project["project_name"])
        
        print("高复杂度项目：{}个，复杂度加成：{}台".format(len(high_complexity_projects), complexity_bonus))
        
        # 4. 并行测试优化
        # 检查是否有可以并行的项目
        parallel_groups = self._analyze_parallel_possibility(matched_projects)
        parallel_efficiency = 0.8 if len(parallel_groups) > 1 else 1.0
        
        print("并行测试组：{}个，效率：{}".format(len(parallel_groups), parallel_efficiency))
        
        # 5. 计算最终车辆数量
        adjusted_vehicles = int(total_baseline * variation_multiplier * parallel_efficiency) + complexity_bonus
        
        # 6. 风险缓解备用车辆
        risk_vehicles = max(1, adjusted_vehicles // 5)  # 20%的备用车辆
        
        final_vehicles = adjusted_vehicles + risk_vehicles
        
        print("调整后车辆：{}台".format(adjusted_vehicles))
        print("风险缓解备用：{}台".format(risk_vehicles))
        print("最终车辆数量：{}台".format(final_vehicles))
        
        return final_vehicles
    
    def _analyze_parallel_possibility(self, matched_projects):
        """分析并行测试可能性"""
        # 基于组件需求分析哪些测试可以并行
        component_usage = defaultdict(list)
        
        for project in matched_projects:
            for component in project["matched_components"]:
                component_usage[component].append(project["project_name"])
        
        # 简单分组：相同组件的测试不能完全并行
        parallel_groups = []
        used_projects = set()
        
        for project in matched_projects:
            if project["project_name"] not in used_projects:
                group = [project["project_name"]]
                used_projects.add(project["project_name"])
                
                # 找到可以并行的项目
                for other_project in matched_projects:
                    if other_project["project_name"] not in used_projects:
                        # 检查是否有组件冲突
                        if not set(project["matched_components"]) & set(other_project["matched_components"]):
                            group.append(other_project["project_name"])
                            used_projects.add(other_project["project_name"])
                
                parallel_groups.append(group)
        
        return parallel_groups
    
    def generate_enhanced_schedule(self, matched_projects, sop_date, total_vehicles):
        """生成增强版测试排期"""
        print("\n=== 生成增强版测试排期 ===")
        print("SOP日期：{}".format(sop_date))
        print("总车辆数：{}台".format(total_vehicles))
        
        # 分析并行测试可能性
        parallel_groups = self._analyze_parallel_possibility(matched_projects)
        
        # 按优先级排序项目
        sorted_projects = sorted(matched_projects, key=lambda x: (
            -x["match_score"],  # 匹配度高的优先
            -x["test_cycle"],   # 周期长的优先
            -x["vehicle_baseline"]  # 需要车辆多的优先
        ))
        
        # 计算总测试时间
        total_duration = 0
        for group in parallel_groups:
            group_duration = 0
            for project_name in group:
                project = next(p for p in matched_projects if p["project_name"] == project_name)
                group_duration = max(group_duration, project["test_cycle"])
            total_duration += group_duration
        
        # 反推开始时间
        sop_datetime = datetime.strptime(sop_date, "%Y-%m-%d")
        start_date = sop_datetime - timedelta(days=total_duration + 45)  # 45天缓冲
        
        # 生成详细排期
        schedule = {
            "start_date": start_date.strftime("%Y-%m-%d"),
            "sop_date": sop_date,
            "total_duration": total_duration,
            "buffer_days": 45,
            "total_vehicles": total_vehicles,
            "parallel_groups": parallel_groups,
            "project_details": []
        }
        
        current_date = start_date
        
        for i, group in enumerate(parallel_groups):
            print("\n并行组 {}：".format(i + 1))
            group_duration = 0
            group_projects = []
            
            for project_name in group:
                project = next(p for p in matched_projects if p["project_name"] == project_name)
                
                project_detail = {
                    "project_name": project_name,
                    "start_date": current_date.strftime("%Y-%m-%d"),
                    "end_date": (current_date + timedelta(days=project["test_cycle"])).strftime("%Y-%m-%d"),
                    "duration": project["test_cycle"],
                    "allocated_vehicles": min(project["vehicle_baseline"], total_vehicles // len(matched_projects)),
                    "components": project["matched_components"],
                    "parallel_group": i + 1
                }
                
                group_projects.append(project_detail)
                group_duration = max(group_duration, project["test_cycle"])
                
                print("  项目：{} ({} ~ {}, {}天, {}台车)".format(
                    project_name,
                    project_detail["start_date"],
                    project_detail["end_date"],
                    project["test_cycle"],
                    project_detail["allocated_vehicles"]
                ))
            
            schedule["project_details"].extend(group_projects)
            current_date += timedelta(days=group_duration + 5)  # 5天间隔
        
        print("\n总测试周期：{}天".format(total_duration))
        print("建议开始时间：{}".format(start_date.strftime("%Y-%m-%d")))
        
        return schedule
    
    def generate_enhanced_report(self, vehicle_config, matched_projects, total_vehicles, schedule):
        """生成增强版DVP报告"""
        print("\n=== 生成增强版DVP报告 ===")
        
        dvp_id = "DVP_Enhanced_{}".format(datetime.now().strftime("%Y%m%d_%H%M%S"))
        
        # 计算成本估算
        base_cost_per_vehicle = 15  # 15万元/台
        test_operation_cost = len(matched_projects) * 5  # 每个项目5万元
        total_cost = total_vehicles * base_cost_per_vehicle + test_operation_cost
        
        # 风险评估
        risk_factors = []
        if total_vehicles > 10:
            risk_factors.append("车辆数量较多，资源协调风险")
        if schedule["total_duration"] > 60:
            risk_factors.append("测试周期较长，时间风险")
        if len(matched_projects) > 8:
            risk_factors.append("测试项目较多，复杂度风险")
        
        risk_level = "高" if len(risk_factors) > 2 else "中" if len(risk_factors) > 0 else "低"
        
        report = {
            "dvp_id": dvp_id,
            "generated_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "vehicle_config": vehicle_config,
            "matched_projects": matched_projects,
            "total_vehicles": total_vehicles,
            "test_schedule": schedule,
            "cost_analysis": {
                "vehicle_cost": total_vehicles * base_cost_per_vehicle,
                "operation_cost": test_operation_cost,
                "total_cost": total_cost,
                "cost_per_project": total_cost / len(matched_projects) if matched_projects else 0
            },
            "risk_analysis": {
                "risk_level": risk_level,
                "risk_factors": risk_factors,
                "mitigation_suggestions": [
                    "建立车辆资源池统一管理",
                    "制定详细的测试计划和里程碑",
                    "建立风险监控和应急预案",
                    "定期进行进度评估和调整"
                ]
            },
            "summary": {
                "total_projects": len(matched_projects),
                "total_test_duration": schedule["total_duration"],
                "parallel_efficiency": len(schedule["parallel_groups"]),
                "components_coverage": len(set(comp for proj in matched_projects for comp in proj["matched_components"]))
            }
        }
        
        # 保存报告
        report_filename = "enhanced_dvp_report_{}.json".format(dvp_id)
        with open(report_filename, "w") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("DVP方案ID：{}".format(dvp_id))
        print("匹配项目：{}个".format(len(matched_projects)))
        print("所需车辆：{}台".format(total_vehicles))
        print("测试周期：{}天".format(schedule["total_duration"]))
        print("预估成本：{}万元".format(total_cost))
        print("风险等级：{}".format(risk_level))
        print("并行测试组：{}个".format(len(schedule["parallel_groups"])))
        print("组件覆盖：{}个".format(report["summary"]["components_coverage"]))
        print("报告已保存：{}".format(report_filename))
        
        return report
    
    def run_enhanced_demo(self, vehicle_config, sop_date, variation_factors=None):
        """运行增强版演示"""
        print("=== 增强版DVP智能生成系统 ===")
        print("基于PDF矩阵解析的智能DVP生成")
        print("=" * 50)
        
        # 1. 基于矩阵关系匹配试验项目
        matched_projects = self.match_test_projects_by_components(vehicle_config["components"])
        
        # 2. 基于矩阵逻辑计算车辆数量
        total_vehicles = self.calculate_vehicles_with_matrix_logic(matched_projects, variation_factors)
        
        # 3. 生成增强版测试排期
        schedule = self.generate_enhanced_schedule(matched_projects, sop_date, total_vehicles)
        
        # 4. 生成增强版DVP报告
        report = self.generate_enhanced_report(vehicle_config, matched_projects, total_vehicles, schedule)
        
        print("\n=== 增强版演示完成 ===")
        print("基于PDF矩阵数据的DVP方案生成完成！")
        
        return report

def main():
    """主函数"""
    generator = EnhancedDVPGenerator()
    
    # 示例车辆配置
    vehicle_config = {
        "config_id": "CONFIG_ADAS_L2_PLUS",
        "config_name": "ADAS L2+级智能驾驶配置",
        "components": [
            "前摄像头",
            "后摄像头", 
            "前毫米波雷达",
            "后毫米波雷达",
            "激光雷达",
            "ADAS控制器ECU",
            "显示屏HMI"
        ],
        "level": "L2+",
        "target_market": "高端智能汽车"
    }
    
    # 示例变化因素
    variation_factors = [
        "天气条件",
        "路面状况", 
        "光照条件",
        "车速范围",
        "目标车型"
    ]
    
    # SOP日期
    sop_date = "2024-12-31"
    
    # 运行增强版演示
    report = generator.run_enhanced_demo(vehicle_config, sop_date, variation_factors)
    
    return report

if __name__ == "__main__":
    main()