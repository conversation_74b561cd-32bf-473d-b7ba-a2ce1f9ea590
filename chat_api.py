# -*- coding: utf-8 -*-
"""
DVP聊天API服务
集成真实LLM推理和知识图谱查询
"""

import json
import os
import re
from datetime import datetime, timedelta
from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
import urllib.request
import urllib.parse
import urllib.error

app = Flask(__name__)
CORS(app)

class DVPChatBot:
    """DVP聊天机器人"""
    
    def __init__(self):
        self.api_key = "sk-nauixigqyamklriyoqzepwtxjtenumbehtucnjdtxtvloxbz"
        self.base_url = "https://api.siliconflow.cn/v1"
        self.conversation_history = []
        self.knowledge_graph = self.load_knowledge_graph()
    
    def load_knowledge_graph(self):
        """加载知识图谱数据"""
        return {
            "test_projects": {
                "ACC自适应巡航控制": {
                    "test_cycle": 20,
                    "vehicle_baseline": 3,
                    "components": ["前摄像头", "前毫米波雷达", "ADAS控制器ECU"],
                    "complexity": "高",
                    "description": "需要长距离测试和多场景验证"
                },
                "AEB自动紧急制动": {
                    "test_cycle": 15,
                    "vehicle_baseline": 2,
                    "components": ["前摄像头", "前毫米波雷达", "ADAS控制器ECU"],
                    "complexity": "高",
                    "description": "需要高风险测试，需要备用车辆"
                },
                "BSD盲点监测": {
                    "test_cycle": 10,
                    "vehicle_baseline": 2,
                    "components": ["后摄像头", "左前毫米波雷达", "右前毫米波雷达", "ADAS控制器ECU"],
                    "complexity": "中",
                    "description": "需要多角度测试，考虑不同车型"
                },
                "LDW车道偏离警告": {
                    "test_cycle": 8,
                    "vehicle_baseline": 1,
                    "components": ["前摄像头", "ADAS控制器ECU"],
                    "complexity": "低",
                    "description": "相对简单功能，基础测试即可"
                },
                "FCW前方碰撞警告": {
                    "test_cycle": 10,
                    "vehicle_baseline": 2,
                    "components": ["前摄像头", "前毫米波雷达", "ADAS控制器ECU"],
                    "complexity": "中",
                    "description": "需要多场景测试"
                }
            },
            "components": [
                "前摄像头", "后摄像头", "左侧摄像头", "右侧摄像头",
                "前毫米波雷达", "后毫米波雷达", "左前毫米波雷达", "右前毫米波雷达",
                "激光雷达", "超声波传感器", "ADAS控制器ECU", "显示屏HMI"
            ]
        }
    
    def call_llm(self, prompt, system_prompt=""):
        """调用LLM API"""
        url = self.base_url + "/chat/completions"
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        messages.append({"role": "user", "content": prompt})
        
        data = {
            "model": "deepseek-ai/DeepSeek-R1",
            "messages": messages,
            "max_tokens": 2000,
            "temperature": 0.7
        }
        
        try:
            headers = {
                "Authorization": "Bearer " + self.api_key,
                "Content-Type": "application/json"
            }
            data_bytes = json.dumps(data).encode('utf-8')
            req = urllib.request.Request(url, data_bytes, headers)
            
            with urllib.request.urlopen(req) as response:
                result = json.loads(response.read().decode('utf-8'))
            
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                return "API调用失败"
                
        except Exception as e:
            print("LLM API调用错误: {}".format(str(e)))
            return "API调用失败: {}".format(str(e))
    
    def analyze_user_input(self, user_input):
        """分析用户输入，提取配置信息"""
        system_prompt = """你是一个汽车ADAS配置分析专家。请分析用户输入，提取以下信息：
1. 车型配置类型
2. SOP日期
3. 组件列表
4. 用户意图

请以JSON格式返回分析结果。"""
        
        prompt = """
请分析以下用户输入，提取配置信息：

用户输入："{}"

可能的组件包括：前摄像头、后摄像头、前毫米波雷达、后毫米波雷达、激光雷达、ADAS控制器ECU等。

请返回JSON格式：
{{
    "config_type": "配置类型",
    "sop_date": "SOP日期(YYYY-MM-DD格式)",
    "components": ["组件1", "组件2"],
    "intent": "用户意图",
    "confidence": 0.8
}}
        """.format(user_input)
        
        response = self.call_llm(prompt, system_prompt)
        
        try:
            # 尝试解析JSON响应
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except:
            pass
        
        # 如果LLM解析失败，使用规则匹配
        return self.fallback_analysis(user_input)
    
    def fallback_analysis(self, user_input):
        """备用分析方法"""
        components = []
        component_map = {
            '前摄像头': '前摄像头',
            '后摄像头': '后摄像头',
            '毫米波雷达': '前毫米波雷达',
            '激光雷达': '激光雷达',
            'ADAS控制器': 'ADAS控制器ECU',
            'ECU': 'ADAS控制器ECU'
        }
        
        for key, value in component_map.items():
            if key in user_input:
                components.append(value)
        
        # 提取SOP日期
        date_match = re.search(r'(\d{4})[年-](\d{1,2})[月-](\d{1,2})', user_input)
        sop_date = None
        if date_match:
            sop_date = "{}-{:02d}-{:02d}".format(
                int(date_match.group(1)),
                int(date_match.group(2)),
                int(date_match.group(3))
            )
        
        config_type = "ADAS基础配置"
        if "L2" in user_input:
            config_type = "ADAS L2级配置"
        elif "高端" in user_input or "智能" in user_input:
            config_type = "高端智能驾驶配置"
        
        return {
            "config_type": config_type,
            "sop_date": sop_date,
            "components": components,
            "intent": "生成DVP方案",
            "confidence": 0.6
        }
    
    def query_knowledge_graph(self, components):
        """查询知识图谱，匹配试验项目"""
        matched_projects = []
        
        for project_name, project_info in self.knowledge_graph["test_projects"].items():
            required_components = project_info["components"]
            
            # 计算匹配度
            matched_comps = []
            for req_comp in required_components:
                if req_comp in components:
                    matched_comps.append(req_comp)
            
            if matched_comps:
                match_ratio = len(matched_comps) / len(required_components)
                matched_projects.append({
                    "project_name": project_name,
                    "match_ratio": match_ratio,
                    "matched_components": matched_comps,
                    "test_cycle": project_info["test_cycle"],
                    "vehicle_baseline": project_info["vehicle_baseline"],
                    "complexity": project_info["complexity"],
                    "description": project_info["description"]
                })
        
        # 按匹配度排序
        matched_projects.sort(key=lambda x: x["match_ratio"], reverse=True)
        return matched_projects
    
    def calculate_vehicles_with_llm(self, matched_projects, parameters):
        """使用LLM推理计算车辆数量"""
        system_prompt = """你是汽车DVP测试专家，负责计算试验车辆数量。
请根据试验项目信息和参数，进行逻辑推理计算所需的车辆数量。
考虑因素包括：基线车辆、复杂度、环境因素、并行优化、风险缓解等。"""
        
        projects_info = "\n".join([
            "项目：{}，基线车辆：{}台，测试周期：{}天，复杂度：{}".format(
                p["project_name"], p["vehicle_baseline"], p["test_cycle"], p["complexity"]
            ) for p in matched_projects
        ])
        
        prompt = """
请计算以下测试项目所需的车辆数量：

试验项目信息：
{}

测试参数：
- 复杂度：{}
- 环境因素：{}
- 优先级：{}

请按以下步骤进行推理：
1. 基础车辆需求（各项目基线车辆之和）
2. 复杂度调整（根据项目复杂度增加车辆）
3. 环境因素调整（根据测试环境增加车辆）
4. 并行优化（可并行项目减少车辆需求）
5. 风险缓解（增加备用车辆）

最后给出具体的车辆数量计算结果。
        """.format(
            projects_info,
            parameters.get("complexity", "标准"),
            parameters.get("environment", "常规"),
            parameters.get("priority", "常规")
        )
        
        response = self.call_llm(prompt, system_prompt)
        
        # 从LLM响应中提取车辆数量
        numbers = re.findall(r'(\d+)台', response)
        if numbers:
            total_vehicles = int(numbers[-1])  # 取最后一个数字作为总数
        else:
            # 备用计算
            base_vehicles = sum(p["vehicle_baseline"] for p in matched_projects)
            total_vehicles = base_vehicles + 3  # 简单加3台
        
        return total_vehicles, response
    
    def generate_schedule_with_llm(self, matched_projects, sop_date, total_vehicles):
        """使用LLM生成测试排期"""
        system_prompt = """你是测试排期专家，请根据项目信息生成详细的测试时间安排。
考虑项目依赖关系、并行可能性、资源约束等因素。"""
        
        projects_info = "\n".join([
            "{}：{}天，{}台车，复杂度{}".format(
                p["project_name"], p["test_cycle"], p["vehicle_baseline"], p["complexity"]
            ) for p in matched_projects
        ])
        
        prompt = """
请为以下项目制定测试排期：

项目信息：
{}

约束条件：
- SOP日期：{}
- 总车辆数：{}台
- 当前日期：{}

请生成包含以下信息的排期：
1. 各项目的开始和结束日期
2. 车辆分配方案
3. 并行执行的项目组
4. 关键里程碑节点

以表格形式返回排期结果。
        """.format(projects_info, sop_date, total_vehicles, datetime.now().strftime("%Y-%m-%d"))
        
        response = self.call_llm(prompt, system_prompt)
        return response
    
    def generate_markdown_report(self, analysis_result, matched_projects, total_vehicles, calculation_process, schedule):
        """生成Markdown格式的DVP报告"""
        current_time = datetime.now()
        
        report = """# 🚗 DVP智能生成方案报告

## 📋 项目基本信息

| 项目 | 值 |
|------|-----|
| **方案编号** | DVP_AI_{} |
| **生成时间** | {} |
| **配置类型** | {} |
| **SOP日期** | {} |
| **分析置信度** | {:.1%} |

## 🔧 车辆配置分析

**识别的组件配置**：
{}

## 🧪 知识图谱匹配结果

{}

## 🚙 AI智能车辆计算

### LLM推理过程：
{}

### 计算结果：
**所需车辆总数：{}台**

## 📅 智能测试排期

{}

## 💡 AI建议

基于知识图谱分析和LLM推理，建议：
1. 优先执行高匹配度项目
2. 合理安排并行测试以提高效率
3. 建立风险监控机制
4. 定期进行进度评审

---
*本报告由DVP智能生成系统自动生成，集成知识图谱查询和LLM推理能力*
        """.format(
            current_time.strftime("%Y%m%d%H%M"),
            current_time.strftime("%Y-%m-%d %H:%M:%S"),
            analysis_result.get("config_type", "未知配置"),
            analysis_result.get("sop_date", "未指定"),
            analysis_result.get("confidence", 0.0),
            "\n".join("- {}".format(comp) for comp in analysis_result.get("components", [])),
            self._format_matched_projects(matched_projects),
            calculation_process,
            total_vehicles,
            schedule
        )
        
        return report
    
    def _format_matched_projects(self, matched_projects):
        """格式化匹配的项目信息"""
        if not matched_projects:
            return "未找到匹配的试验项目"
        
        result = ""
        for i, project in enumerate(matched_projects, 1):
            result += """
### {}. {}
- **匹配度**：{:.1%}
- **测试周期**：{}天
- **基线车辆**：{}台
- **复杂度**：{}
- **匹配组件**：{}
- **描述**：{}
            """.format(
                i,
                project["project_name"],
                project["match_ratio"],
                project["test_cycle"],
                project["vehicle_baseline"],
                project["complexity"],
                "、".join(project["matched_components"]),
                project["description"]
            )
        
        return result

# 创建聊天机器人实例
chatbot = DVPChatBot()

@app.route('/')
def index():
    """返回聊天界面"""
    with open('index.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/api/chat', methods=['POST'])
def chat():
    """处理聊天请求"""
    try:
        data = request.json
        user_message = data.get('message', '')
        conversation_state = data.get('state', {})
        
        # 分析用户输入
        analysis_result = chatbot.analyze_user_input(user_message)
        
        # 查询知识图谱
        if analysis_result.get("components"):
            matched_projects = chatbot.query_knowledge_graph(analysis_result["components"])
        else:
            matched_projects = []
        
        # 根据对话状态生成响应
        if conversation_state.get("step") == "awaiting_parameters":
            # 等待参数输入
            parameters = data.get('parameters', {})
            
            # 使用LLM计算车辆数量
            total_vehicles, calculation_process = chatbot.calculate_vehicles_with_llm(
                matched_projects, parameters
            )
            
            # 生成测试排期
            schedule = chatbot.generate_schedule_with_llm(
                matched_projects, 
                analysis_result.get("sop_date", "2024-12-31"),
                total_vehicles
            )
            
            # 生成完整报告
            markdown_report = chatbot.generate_markdown_report(
                analysis_result, matched_projects, total_vehicles, 
                calculation_process, schedule
            )
            
            response = {
                "message": markdown_report,
                "type": "markdown_report",
                "data": {
                    "analysis": analysis_result,
                    "matched_projects": matched_projects,
                    "total_vehicles": total_vehicles,
                    "calculation_process": calculation_process,
                    "schedule": schedule
                }
            }
        else:
            # 初始分析
            if matched_projects:
                response_text = """### 🔍 配置分析完成

我已分析您的配置并查询了知识图谱：

**配置类型**：{}
**SOP日期**：{}
**检测组件**：{}

**匹配的试验项目**：
{}

### 🤔 需要您确认测试参数

请选择以下参数以便进行精确计算：""".format(
                    analysis_result.get("config_type", "未知"),
                    analysis_result.get("sop_date", "未指定"),
                    "、".join(analysis_result.get("components", [])),
                    "\n".join("{}. {} (匹配度: {:.1%})".format(
                        i+1, p["project_name"], p["match_ratio"]
                    ) for i, p in enumerate(matched_projects))
                )
                
                response = {
                    "message": response_text,
                    "type": "parameter_request",
                    "data": {
                        "analysis": analysis_result,
                        "matched_projects": matched_projects,
                        "requires_parameters": True
                    }
                }
            else:
                response = {
                    "message": "抱歉，我无法从您的输入中识别到足够的配置信息。请提供更详细的车型配置和组件信息。",
                    "type": "error",
                    "data": analysis_result
                }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({
            "message": "处理请求时出现错误：{}".format(str(e)),
            "type": "error"
        }), 500

if __name__ == '__main__':
    print("启动DVP智能聊天系统...")
    print("访问地址：http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)