矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Serving Flask app 'dvp_api_server'
 * Debug mode: on
[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**********:5000
[33mPress CTRL+C to quit[0m
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
127.0.0.1 - - [07/Jul/2025 10:55:15] "HEAD /api/test-connection HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 10:55:50] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 10:56:27] "POST /api/generate-dvp HTTP/1.1" 200 -
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
LLM API调用错误: The read operation timed out
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j驱动未安装，使用模拟连接...
Neo4j模拟连接成功!
🎯 收到DVP生成请求: ADAS基础配置
🚀 开始生成DVP方案: ADAS基础配置
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件1个, 匹配度0.33
  - AEB自动紧急制动: 匹配组件1个, 匹配度0.33
  - LDW车道偏离警告: 匹配组件1个, 匹配度0.33
  - LKA车道保持辅助: 匹配组件1个, 匹配度0.33
  - FCW前方碰撞警告: 匹配组件1个, 匹配度0.33
  - TSR交通标志识别: 匹配组件1个, 匹配度0.33
  - HMA远光灯辅助: 匹配组件1个, 匹配度0.33
  - APA自动泊车辅助: 匹配组件1个, 匹配度0.17
🎯 过滤后保留 7 个高质量匹配项目 (阈值: 0.3)
📊 匹配到 7 个试验项目
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 22 台
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_105550.json'
🎯 收到DVP生成请求: ADAS L2+配置
🚀 开始生成DVP方案: ADAS L2+配置
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头', '后摄像头', '前毫米波雷达', '后毫米波雷达', '激光雷达', 'ADAS控制器ECU', '显示屏HMI']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件3个, 匹配度1.00
  - AEB自动紧急制动: 匹配组件3个, 匹配度1.00
  - BSD盲点监测: 匹配组件4个, 匹配度1.00
  - FCW前方碰撞警告: 匹配组件3个, 匹配度1.00
  - RCW后方交叉警告: 匹配组件3个, 匹配度1.00
  - LDW车道偏离警告: 匹配组件2个, 匹配度0.67
  - LKA车道保持辅助: 匹配组件2个, 匹配度0.67
  - TSR交通标志识别: 匹配组件2个, 匹配度0.67
  - HMA远光灯辅助: 匹配组件2个, 匹配度0.67
  - APA自动泊车辅助: 匹配组件3个, 匹配度0.50
🎯 过滤后保留 10 个高质量匹配项目 (阈值: 0.5)
📊 匹配到 10 个试验项目
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 47 台
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_105627.json'
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
127.0.0.1 - - [07/Jul/2025 11:00:57] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:02:10] "GET /api/test-connection HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:02:20] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:02:40] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:07:01] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:07:21] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:07:44] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:08:04] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:09:16] "POST /api/generate-dvp HTTP/1.1" 200 -
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
🎯 收到DVP生成请求: ADAS基础配置
🚀 开始生成DVP方案: ADAS基础配置
📅 DVP执行时间窗口: 2025-01-07 → 2025-07-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件1个, 匹配度0.33
  - AEB自动紧急制动: 匹配组件1个, 匹配度0.33
  - LDW车道偏离警告: 匹配组件1个, 匹配度0.33
  - LKA车道保持辅助: 匹配组件1个, 匹配度0.33
  - FCW前方碰撞警告: 匹配组件1个, 匹配度0.33
  - TSR交通标志识别: 匹配组件1个, 匹配度0.33
  - HMA远光灯辅助: 匹配组件1个, 匹配度0.33
  - APA自动泊车辅助: 匹配组件1个, 匹配度0.17
🎯 过滤后保留 7 个高质量匹配项目 (阈值: 0.3)
📊 匹配到 7 个试验项目
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 22 台
⏰ 排期时间窗口: 2025-01-07 至 2025-07-07 (181天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_110057.json'
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j驱动未安装，使用模拟连接...
Neo4j模拟连接成功!
🎯 收到DVP生成请求: 高端智能驾驶配置
🚀 开始生成DVP方案: 高端智能驾驶配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头', '后摄像头', '左侧摄像头', '右侧摄像头', '前毫米波雷达', '后毫米波雷达', '左前毫米波雷达', '右前毫米波雷达', '激光雷达', '超声波传感器', 'ADAS控制器ECU', '显示屏HMI']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件3个, 匹配度1.00
  - AEB自动紧急制动: 匹配组件3个, 匹配度1.00
  - BSD盲点监测: 匹配组件4个, 匹配度1.00
  - FCW前方碰撞警告: 匹配组件3个, 匹配度1.00
  - APA自动泊车辅助: 匹配组件6个, 匹配度1.00
  - RCW后方交叉警告: 匹配组件3个, 匹配度1.00
  - LDW车道偏离警告: 匹配组件2个, 匹配度0.67
  - LKA车道保持辅助: 匹配组件2个, 匹配度0.67
  - TSR交通标志识别: 匹配组件2个, 匹配度0.67
  - HMA远光灯辅助: 匹配组件2个, 匹配度0.67
🎯 过滤后保留 10 个高质量匹配项目 (阈值: 0.5)
📊 匹配到 10 个试验项目
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 50 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_110240.json'
🎯 收到DVP生成请求: ADAS L2+配置
🚀 开始生成DVP方案: ADAS L2+配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头', '后摄像头', '前毫米波雷达', '后毫米波雷达', '激光雷达', 'ADAS控制器ECU', '显示屏HMI']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件3个, 匹配度1.00
  - AEB自动紧急制动: 匹配组件3个, 匹配度1.00
  - BSD盲点监测: 匹配组件4个, 匹配度1.00
  - FCW前方碰撞警告: 匹配组件3个, 匹配度1.00
  - RCW后方交叉警告: 匹配组件3个, 匹配度1.00
  - LDW车道偏离警告: 匹配组件2个, 匹配度0.67
  - LKA车道保持辅助: 匹配组件2个, 匹配度0.67
  - TSR交通标志识别: 匹配组件2个, 匹配度0.67
  - HMA远光灯辅助: 匹配组件2个, 匹配度0.67
  - APA自动泊车辅助: 匹配组件3个, 匹配度0.50
🎯 过滤后保留 10 个高质量匹配项目 (阈值: 0.5)
📊 匹配到 10 个试验项目
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 47 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_110721.json'
🎯 收到DVP生成请求: ADAS基础配置
🚀 开始生成DVP方案: ADAS基础配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头', 'ADAS控制器ECU']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件2个, 匹配度0.67
  - AEB自动紧急制动: 匹配组件2个, 匹配度0.67
  - LDW车道偏离警告: 匹配组件2个, 匹配度0.67
  - LKA车道保持辅助: 匹配组件2个, 匹配度0.67
  - FCW前方碰撞警告: 匹配组件2个, 匹配度0.67
  - TSR交通标志识别: 匹配组件2个, 匹配度0.67
  - HMA远光灯辅助: 匹配组件2个, 匹配度0.67
  - APA自动泊车辅助: 匹配组件2个, 匹配度0.33
  - RCW后方交叉警告: 匹配组件1个, 匹配度0.33
  - BSD盲点监测: 匹配组件1个, 匹配度0.25
🎯 过滤后保留 7 个高质量匹配项目 (阈值: 0.5)
📊 匹配到 7 个试验项目
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 31 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_110804.json'
🎯 收到DVP生成请求: ADAS L2级配置
🚀 开始生成DVP方案: ADAS L2级配置
📅 DVP执行时间窗口: 2025-01-07 → 2025-07-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头', '前毫米波雷达', 'ADAS控制器ECU', '显示屏HMI']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件3个, 匹配度1.00
  - AEB自动紧急制动: 匹配组件3个, 匹配度1.00
  - FCW前方碰撞警告: 匹配组件3个, 匹配度1.00
  - BSD盲点监测: 匹配组件3个, 匹配度0.75
  - LDW车道偏离警告: 匹配组件2个, 匹配度0.67
  - LKA车道保持辅助: 匹配组件2个, 匹配度0.67
  - TSR交通标志识别: 匹配组件2个, 匹配度0.67
  - HMA远光灯辅助: 匹配组件2个, 匹配度0.67
  - APA自动泊车辅助: 匹配组件2个, 匹配度0.33
  - RCW后方交叉警告: 匹配组件1个, 匹配度0.33
🎯 过滤后保留 8 个高质量匹配项目 (阈值: 0.5)
📊 匹配到 8 个试验项目
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 36 台
⏰ 排期时间窗口: 2025-01-07 至 2025-07-07 (181天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_110916.json'
🎯 收到DVP生成请求: ADAS L2级配置127.0.0.1 - - [07/Jul/2025 11:09:49] "POST /api/generate-dvp HTTP/1.1" 200 -
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading

🚀 开始生成DVP方案: ADAS L2级配置
📅 DVP执行时间窗口: 2025-01-07 → 2025-07-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头', '前毫米波雷达', 'ADAS控制器ECU', '显示屏HMI']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件3个, 匹配度1.00
  - AEB自动紧急制动: 匹配组件3个, 匹配度1.00
  - FCW前方碰撞警告: 匹配组件3个, 匹配度1.00
  - BSD盲点监测: 匹配组件3个, 匹配度0.75
  - LDW车道偏离警告: 匹配组件2个, 匹配度0.67
  - LKA车道保持辅助: 匹配组件2个, 匹配度0.67
  - TSR交通标志识别: 匹配组件2个, 匹配度0.67
  - HMA远光灯辅助: 匹配组件2个, 匹配度0.67
  - APA自动泊车辅助: 匹配组件2个, 匹配度0.33
  - RCW后方交叉警告: 匹配组件1个, 匹配度0.33
🎯 过滤后保留 8 个高质量匹配项目 (阈值: 0.5)
📊 匹配到 8 个试验项目
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 36 台
⏰ 排期时间窗口: 2025-01-07 至 2025-07-07 (181天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_110949.json'
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
127.0.0.1 - - [07/Jul/2025 11:13:33] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:13:53] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:18:17] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:18:48] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:19:07] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:19:27] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:19:57] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:20:18] "POST /api/generate-dvp HTTP/1.1" 200 -
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
🎯 收到DVP生成请求: ADAS L2级配置
🚀 开始生成DVP方案: ADAS L2级配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头', '前毫米波雷达', 'ADAS控制器ECU', '显示屏HMI']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件3个, 匹配度1.00
  - AEB自动紧急制动: 匹配组件3个, 匹配度1.00
  - FCW前方碰撞警告: 匹配组件3个, 匹配度1.00
  - BSD盲点监测: 匹配组件3个, 匹配度0.75
  - LDW车道偏离警告: 匹配组件2个, 匹配度0.67
  - LKA车道保持辅助: 匹配组件2个, 匹配度0.67
  - TSR交通标志识别: 匹配组件2个, 匹配度0.67
  - HMA远光灯辅助: 匹配组件2个, 匹配度0.67
  - APA自动泊车辅助: 匹配组件2个, 匹配度0.33
  - RCW后方交叉警告: 匹配组件1个, 匹配度0.33
🎯 过滤后保留 8 个高质量匹配项目 (阈值: 0.5)
📊 匹配到 8 个试验项目
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 36 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_111353.json'
🎯 收到DVP生成请求: ADAS基础配置
🚀 开始生成DVP方案: ADAS基础配置
📅 DVP执行时间窗口: 2025-01-07 → 2025-07-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件1个, 匹配度0.33
  - AEB自动紧急制动: 匹配组件1个, 匹配度0.33
  - LDW车道偏离警告: 匹配组件1个, 匹配度0.33
  - LKA车道保持辅助: 匹配组件1个, 匹配度0.33
  - FCW前方碰撞警告: 匹配组件1个, 匹配度0.33
  - TSR交通标志识别: 匹配组件1个, 匹配度0.33
  - HMA远光灯辅助: 匹配组件1个, 匹配度0.33
  - APA自动泊车辅助: 匹配组件1个, 匹配度0.17
🎯 过滤后保留 7 个高质量匹配项目 (阈值: 0.3)
📊 匹配到 7 个试验项目
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 22 台
⏰ 排期时间窗口: 2025-01-07 至 2025-07-07 (181天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_111817.json'
🎯 收到DVP生成请求: ADAS基础配置
🚀 开始生成DVP方案: ADAS基础配置
📅 DVP执行时间窗口: 2025-01-07 → 2025-07-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件1个, 匹配度0.33
  - AEB自动紧急制动: 匹配组件1个, 匹配度0.33
  - LDW车道偏离警告: 匹配组件1个, 匹配度0.33
  - LKA车道保持辅助: 匹配组件1个, 匹配度0.33
  - FCW前方碰撞警告: 匹配组件1个, 匹配度0.33
  - TSR交通标志识别: 匹配组件1个, 匹配度0.33
  - HMA远光灯辅助: 匹配组件1个, 匹配度0.33
  - APA自动泊车辅助: 匹配组件1个, 匹配度0.17
🎯 过滤后保留 7 个高质量匹配项目 (阈值: 0.3)
📊 匹配到 7 个试验项目
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 22 台
⏰ 排期时间窗口: 2025-01-07 至 2025-07-07 (181天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_111848.json'
🎯 收到DVP生成请求: ADAS L2级配置
🚀 开始生成DVP方案: ADAS L2级配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头', '前毫米波雷达', 'ADAS控制器ECU', '显示屏HMI']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件3个, 匹配度1.00
  - AEB自动紧急制动: 匹配组件3个, 匹配度1.00
  - FCW前方碰撞警告: 匹配组件3个, 匹配度1.00
  - BSD盲点监测: 匹配组件3个, 匹配度0.75
  - LDW车道偏离警告: 匹配组件2个, 匹配度0.67
  - LKA车道保持辅助: 匹配组件2个, 匹配度0.67
  - TSR交通标志识别: 匹配组件2个, 匹配度0.67
  - HMA远光灯辅助: 匹配组件2个, 匹配度0.67
  - APA自动泊车辅助: 匹配组件2个, 匹配度0.33
  - RCW后方交叉警告: 匹配组件1个, 匹配度0.33
🎯 过滤后保留 8 个高质量匹配项目 (阈值: 0.5)
📊 匹配到 8 个试验项目
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 36 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_111927.json'
🎯 收到DVP生成请求: ADAS L2级配置
🚀 开始生成DVP方案: ADAS L2级配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头', '前毫米波雷达', 'ADAS控制器ECU', '显示屏HMI']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件3个, 匹配度1.00
  - AEB自动紧急制动: 匹配组件3个, 匹配度1.00
  - FCW前方碰撞警告: 匹配组件3个, 匹配度1.00
  - BSD盲点监测: 匹配组件3个, 匹配度0.75
  - LDW车道偏离警告: 匹配组件2个, 匹配度0.67
  - LKA车道保持辅助: 匹配组件2个, 匹配度0.67
  - TSR交通标志识别: 匹配组件2个, 匹配度0.67
  - HMA远光灯辅助: 匹配组件2个, 匹配度0.67
  - APA自动泊车辅助: 匹配组件2个, 匹配度0.33
  - RCW后方交叉警告: 匹配组件1个, 匹配度0.33
🎯 过滤后保留 8 个高质量匹配项目 (阈值: 0.5)
📊 匹配到 8 个试验项目
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 36 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_112018.json'
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
127.0.0.1 - - [07/Jul/2025 11:32:36] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:33:15] "POST /api/generate-dvp HTTP/1.1" 200 -
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
🎯 收到DVP生成请求: ADAS基础配置
🚀 开始生成DVP方案: ADAS基础配置
📅 DVP执行时间窗口: 2025-01-07 → 2025-07-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件1个, 匹配度0.33
  - AEB自动紧急制动: 匹配组件1个, 匹配度0.33
  - LDW车道偏离警告: 匹配组件1个, 匹配度0.33
  - LKA车道保持辅助: 匹配组件1个, 匹配度0.33
  - FCW前方碰撞警告: 匹配组件1个, 匹配度0.33
  - TSR交通标志识别: 匹配组件1个, 匹配度0.33
  - HMA远光灯辅助: 匹配组件1个, 匹配度0.33
  - APA自动泊车辅助: 匹配组件1个, 匹配度0.17
🎯 过滤后保留 7 个高质量匹配项目 (阈值: 0.3)
📊 匹配到 7 个试验项目
🎯 车辆计算参数: 复杂度=standard, 环境=normal, 优先级=normal
📊 应用PDF B列业务逻辑计算...
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
⚠️ AI推理失败，使用备用计算: name 'fallback_result' is not defined
🚗 AI计算车辆数量: 12 台
⏰ 排期时间窗口: 2025-01-07 至 2025-07-07 (181天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_113236.json'
🎯 收到DVP生成请求: ADAS基础配置
🚀 开始生成DVP方案: ADAS基础配置
📅 DVP执行时间窗口: 2025-01-07 → 2025-07-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件1个, 匹配度0.33
  - AEB自动紧急制动: 匹配组件1个, 匹配度0.33
  - LDW车道偏离警告: 匹配组件1个, 匹配度0.33
  - LKA车道保持辅助: 匹配组件1个, 匹配度0.33
  - FCW前方碰撞警告: 匹配组件1个, 匹配度0.33
  - TSR交通标志识别: 匹配组件1个, 匹配度0.33
  - HMA远光灯辅助: 匹配组件1个, 匹配度0.33
  - APA自动泊车辅助: 匹配组件1个, 匹配度0.17
🎯 过滤后保留 7 个高质量匹配项目 (阈值: 0.3)
📊 匹配到 7 个试验项目
🎯 车辆计算参数: 复杂度=comprehensive, 环境=extreme, 优先级=urgent
📊 应用PDF B列业务逻辑计算...
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
⚠️ AI推理失败，使用备用计算: name 'fallback_result' is not defined
🚗 AI计算车辆数量: 27 台
⏰ 排期时间窗口: 2025-01-07 至 2025-07-07 (181天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_113315.json'
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
127.0.0.1 - - [07/Jul/2025 11:43:46] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 11:44:06] "POST /api/generate-dvp HTTP/1.1" 200 -
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
🎯 收到DVP生成请求: ADAS基础配置
🚀 开始生成DVP方案: ADAS基础配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['前摄像头', 'ADAS控制器ECU']
🔍 项目匹配结果:
  - ACC自适应巡航控制: 匹配组件2个, 匹配度0.67
  - AEB自动紧急制动: 匹配组件2个, 匹配度0.67
  - LDW车道偏离警告: 匹配组件2个, 匹配度0.67
  - LKA车道保持辅助: 匹配组件2个, 匹配度0.67
  - FCW前方碰撞警告: 匹配组件2个, 匹配度0.67
  - TSR交通标志识别: 匹配组件2个, 匹配度0.67
  - HMA远光灯辅助: 匹配组件2个, 匹配度0.67
  - APA自动泊车辅助: 匹配组件2个, 匹配度0.33
  - RCW后方交叉警告: 匹配组件1个, 匹配度0.33
  - BSD盲点监测: 匹配组件1个, 匹配度0.25
🎯 过滤后保留 7 个高质量匹配项目 (阈值: 0.5)
📊 匹配到 7 个试验项目
🎯 DVP计算参数: 项目类型=首发, 变化程度=单一变化, 资源优先级=normal
📊 应用PDF B列业务逻辑计算...
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 20 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_114406.json'
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
127.0.0.1 - - [07/Jul/2025 12:04:24] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 12:04:44] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 12:05:23] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 12:05:43] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 12:06:19] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 12:06:40] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 12:15:37] "POST /api/generate-dvp HTTP/1.1" 200 -
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
🎯 收到DVP生成请求: ADAS L2+配置
🚀 开始生成DVP方案: ADAS L2+配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['激光雷达', '毫米波雷达', 'AD摄像头前视', 'AD摄像头后视', '环视摄像头', 'DPB+ESP/IPB', 'EPS']
🔍 项目匹配结果:
  - 感知数采: 匹配组件3个, 匹配度0.75
  - 感知开发: 匹配组件4个, 匹配度0.67
  - 产线标定: 匹配组件4个, 匹配度0.67
  - 数据回传: 匹配组件2个, 匹配度0.67
  - 行泊主功能开发: 匹配组件6个, 匹配度0.60
  - 功能集成测试: 匹配组件6个, 匹配度0.60
  - 泛化路试: 匹配组件6个, 匹配度0.60
  - 毫米波雷达标定匹配: 匹配组件1个, 匹配度0.33
  - 激光雷达标定匹配: 匹配组件1个, 匹配度0.33
🎯 过滤后保留 7 个高质量匹配项目 (阈值: 0.5)
📊 匹配到 7 个试验项目
🎯 DVP计算参数: 项目类型=次发, 变化程度=单一变化, 资源优先级=normal
📊 应用PDF B列业务逻辑计算...
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 20 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_120444.json'
🎯 收到DVP生成请求: ADAS基础配置
🚀 开始生成DVP方案: ADAS基础配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['AD摄像头前视', 'EPS']
🔍 项目匹配结果:
  - 数据回传: 匹配组件1个, 匹配度0.33
  - 感知数采: 匹配组件1个, 匹配度0.25
  - 行泊主功能开发: 匹配组件2个, 匹配度0.20
  - 功能集成测试: 匹配组件2个, 匹配度0.20
  - 泛化路试: 匹配组件2个, 匹配度0.20
  - 感知开发: 匹配组件1个, 匹配度0.17
  - 产线标定: 匹配组件1个, 匹配度0.17
🎯 过滤后保留 1 个高质量匹配项目 (阈值: 0.3)
📊 匹配到 1 个试验项目
🎯 DVP计算参数: 项目类型=次发, 变化程度=单一变化, 资源优先级=normal
📊 应用PDF B列业务逻辑计算...
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 1 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 6 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_120543.json'
🎯 收到DVP生成请求: 高端智能驾驶配置
🚀 开始生成DVP方案: 高端智能驾驶配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['FSD变更', '激光雷达', '毫米波雷达', '超声波雷达', 'AD摄像头前视', 'AD摄像头后视', 'AD摄像头侧视', '环视摄像头', 'DPB+ESP/IPB', 'EPS', '后轮转向', '悬架']
🔍 项目匹配结果:
  - 感知数采: 匹配组件4个, 匹配度1.00
  - 感知开发: 匹配组件6个, 匹配度1.00
  - 产线标定: 匹配组件6个, 匹配度1.00
  - 数据回传: 匹配组件3个, 匹配度1.00
  - 行泊主功能开发: 匹配组件10个, 匹配度1.00
  - 功能集成测试: 匹配组件10个, 匹配度1.00
  - 泛化路试: 匹配组件10个, 匹配度1.00
  - 超声波雷达标定匹配: 匹配组件1个, 匹配度0.33
  - 毫米波雷达标定匹配: 匹配组件1个, 匹配度0.33
  - 激光雷达标定匹配: 匹配组件1个, 匹配度0.33
  - 定位IMU模块标定匹配: 匹配组件1个, 匹配度0.33
  - 底软开发: 匹配组件1个, 匹配度0.33
  - 地图定位: 匹配组件1个, 匹配度0.33
🎯 过滤后保留 7 个高质量匹配项目 (阈值: 0.5)
📊 匹配到 7 个试验项目
🎯 DVP计算参数: 项目类型=次发, 变化程度=单一变化, 资源优先级=normal
📊 应用PDF B列业务逻辑计算...
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 20 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_120640.json'
🎯 收到DVP生成请求: ADAS基础配置
🚀 开始生成DVP方案: ADAS基础配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['AD摄像头前视/后视/侧视', 'EPS']
🔍 项目匹配结果:
  - 数据回传: 匹配组件1个, 匹配度0.33
  - 感知数采: 匹配组件1个, 匹配度0.25
  - 行泊主功能开发: 匹配组件2个, 匹配度0.20
  - 功能集成测试: 匹配组件2个, 匹配度0.20
  - 泛化路试: 匹配组件2个, 匹配度0.20
  - 感知开发: 匹配组件1个, 匹配度0.17
  - 产线标定: 匹配组件1个, 匹配度0.17
🎯 过滤后保留 1 个高质量匹配项目 (阈值: 0.3)
📊 匹配到 1 个试验项目
🎯 DVP计算参数: 项目类型=次发, 变化程度=单一变化, 资源优先级=normal
📊 应用PDF B列业务逻辑计算...
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 1 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 6 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_121537.json'
🎯 收到DVP生成请求: ADAS L2+配置
🚀 开始生成DVP方案: ADAS L2+配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['FSD变更', '激光雷达', '毫米波雷达', 'AD摄像头前视/后视/侧视', '环视摄像头', 'EPS', 'DPB+ESP/IPB']
🔍 项目匹配结果:
  - 感知数采: 匹配组件4个, 匹配度1.00
  - 数据回传: 匹配组件3个, 匹配度1.00
  - 感知开发: 匹配组件5个, 匹配度0.83
  - 产线标定: 匹配组件5个, 匹配度0.83
  - 行泊主功能开发: 匹配组件7个, 匹配度0.70
  - 功能集成测试: 匹配组件7个, 匹配度0.70
  - 泛化路试: 匹配组件7个, 匹配度0.70
  - 毫米波雷达标定匹配: 匹配组件1个, 匹配度0.33
  - 激光雷达标定匹配: 匹配组件1个, 匹配度0.33
  - 定位IMU模块标定匹配: 匹配组件1个, 匹配度0.33
  - 底软开发: 匹配组件1个, 匹配度0.33
  - 地图定位: 匹配组件1个, 匹配度0.33
🎯 过滤后保留 7 个高质量匹配项目 (阈值: 0.5)
📊 匹配到 7 个试验项目
🎯 DVP计算参数: 项目类型=首发, 变化程度=多重变化, 资源优先级=urgent
📊 应用PDF B列业务逻辑计算...
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 38 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)127.0.0.1 - - [07/Jul/2025 12:16:16] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 12:16:49] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 12:17:23] "POST /api/generate-dvp HTTP/1.1" 200 -
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading

🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_121616.json'
🎯 收到DVP生成请求: ADAS基础配置
🚀 开始生成DVP方案: ADAS基础配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['AD摄像头前视/后视/侧视', 'EPS']
🔍 项目匹配结果:
  - 数据回传: 匹配组件1个, 匹配度0.33
  - 感知数采: 匹配组件1个, 匹配度0.25
  - 行泊主功能开发: 匹配组件2个, 匹配度0.20
  - 功能集成测试: 匹配组件2个, 匹配度0.20
  - 泛化路试: 匹配组件2个, 匹配度0.20
  - 感知开发: 匹配组件1个, 匹配度0.17
  - 产线标定: 匹配组件1个, 匹配度0.17
🎯 过滤后保留 1 个高质量匹配项目 (阈值: 0.3)
📊 匹配到 1 个试验项目
🎯 DVP计算参数: 项目类型=次发, 变化程度=单一变化, 资源优先级=normal
📊 应用PDF B列业务逻辑计算...
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 1 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 6 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_121649.json'
🎯 收到DVP生成请求: 高端智能驾驶配置
🚀 开始生成DVP方案: 高端智能驾驶配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 基于PDF业务逻辑矩阵匹配试验项目...
🎯 输入组件: ['FSD变更', '激光雷达', '毫米波雷达', '超声波雷达', 'AD摄像头前视/后视/侧视', '环视摄像头', 'DPB+ESP/IPB', 'EPS', '后轮转向', '悬架']
🔍 项目匹配结果:
  - 感知数采: 匹配组件4个, 匹配度1.00
  - 感知开发: 匹配组件6个, 匹配度1.00
  - 产线标定: 匹配组件6个, 匹配度1.00
  - 数据回传: 匹配组件3个, 匹配度1.00
  - 行泊主功能开发: 匹配组件10个, 匹配度1.00
  - 功能集成测试: 匹配组件10个, 匹配度1.00
  - 泛化路试: 匹配组件10个, 匹配度1.00
  - 超声波雷达标定匹配: 匹配组件1个, 匹配度0.33
  - 毫米波雷达标定匹配: 匹配组件1个, 匹配度0.33
  - 激光雷达标定匹配: 匹配组件1个, 匹配度0.33
  - 定位IMU模块标定匹配: 匹配组件1个, 匹配度0.33
  - 底软开发: 匹配组件1个, 匹配度0.33
  - 地图定位: 匹配组件1个, 匹配度0.33
🎯 过滤后保留 7 个高质量匹配项目 (阈值: 0.5)
📊 匹配到 7 个试验项目
🎯 DVP计算参数: 项目类型=首发, 变化程度=多重变化, 资源优先级=urgent
📊 应用PDF B列业务逻辑计算...
🤖 开始AI推理计算...
LLM API调用错误: The read operation timed out
🤖 AI推理完成: API调用失败: The read operation timed out...
⚠️ AI响应格式不正确，使用备用计算
🚗 AI计算车辆数量: 38 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 17 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_121723.json'
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
127.0.0.1 - - [07/Jul/2025 12:34:51] "GET /api/get-components HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 12:35:12] "GET /api/get-test-projects HTTP/1.1" 200 -
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
🔍 从Neo4j数据库查询配件信息...
✅ 查询到 10 个配件
🔍 从Neo4j数据库查询测试项目信息...
✅ 查询到 13 个测试项目
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
127.0.0.1 - - [07/Jul/2025 13:00:52] "GET /api/get-components HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 13:03:33] "GET /api/get-components HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 13:03:33] "GET /api/get-test-projects HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 13:05:41] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 13:05:51] "[35m[1mPOST /api/generate-dvp HTTP/1.1[0m" 500 -
127.0.0.1 - - [07/Jul/2025 13:07:16] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 13:07:27] "[35m[1mPOST /api/generate-dvp HTTP/1.1[0m" 500 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 1, column: 10, offset: 9} for query: 'MATCH (c:Component) RETURN c.name as name ORDER BY c.name'
127.0.0.1 - - [07/Jul/2025 23:11:08] "GET /api/get-components HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: testCycle)} {position: line: 3, column: 50, offset: 96} for query: '\n                        MATCH (t:TestProject)\n                        RETURN t.name as name, t.testCycle as testCycle\n                        ORDER BY t.name\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
127.0.0.1 - - [07/Jul/2025 23:11:10] "GET /api/get-test-projects HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 23:11:19] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 55, offset: 55} for query: '\n                        MATCH (t:TestProject)-[r]->(c:Component)\n                        WHERE c.name IN $components\n                        WITH t, type(r) as relation_type, collect(c.name) as matched_components\n                        RETURN t.name as project_name, \n                               t.testCycle as test_cycle,\n                               matched_components,\n                               size(matched_components) as match_count\n                        ORDER BY match_count DESC\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: testCycle)} {position: line: 6, column: 34, offset: 303} for query: '\n                        MATCH (t:TestProject)-[r]->(c:Component)\n                        WHERE c.name IN $components\n                        WITH t, type(r) as relation_type, collect(c.name) as matched_components\n                        RETURN t.name as project_name, \n                               t.testCycle as test_cycle,\n                               matched_components,\n                               size(matched_components) as match_count\n                        ORDER BY match_count DESC\n                    '
127.0.0.1 - - [07/Jul/2025 23:11:30] "POST /api/generate-dvp HTTP/1.1" 200 -
127.0.0.1 - - [07/Jul/2025 23:15:23] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 55, offset: 55} for query: '\n                        MATCH (t:TestProject)-[r]->(c:Component)\n                        WHERE c.name IN $components\n                        WITH t, type(r) as relation_type, collect(c.name) as matched_components\n                        RETURN t.name as project_name, \n                               t.testCycle as test_cycle,\n                               matched_components,\n                               size(matched_components) as match_count\n                        ORDER BY match_count DESC\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: testCycle)} {position: line: 6, column: 34, offset: 303} for query: '\n                        MATCH (t:TestProject)-[r]->(c:Component)\n                        WHERE c.name IN $components\n                        WITH t, type(r) as relation_type, collect(c.name) as matched_components\n                        RETURN t.name as project_name, \n                               t.testCycle as test_cycle,\n                               matched_components,\n                               size(matched_components) as match_count\n                        ORDER BY match_count DESC\n                    '
127.0.0.1 - - [07/Jul/2025 23:15:34] "POST /api/generate-dvp HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: testCycle)} {position: line: 3, column: 50, offset: 96} for query: '\n                        MATCH (t:TestProject)\n                        RETURN t.name as name, t.testCycle as testCycle\n                        ORDER BY t.name\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 1, column: 10, offset: 9} for query: 'MATCH (c:Component) RETURN c.name as name ORDER BY c.name'
127.0.0.1 - - [08/Jul/2025 07:01:24] "GET /api/get-components HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
127.0.0.1 - - [08/Jul/2025 07:01:25] "GET /api/get-test-projects HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 07:01:41] "OPTIONS /api/generate-dvp HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 55, offset: 55} for query: '\n                        MATCH (t:TestProject)-[r]->(c:Component)\n                        WHERE c.name IN $components\n                        WITH t, type(r) as relation_type, collect(c.name) as matched_components\n                        RETURN t.name as project_name, \n                               t.testCycle as test_cycle,\n                               matched_components,\n                               size(matched_components) as match_count\n                        ORDER BY match_count DESC\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: testCycle)} {position: line: 6, column: 34, offset: 303} for query: '\n                        MATCH (t:TestProject)-[r]->(c:Component)\n                        WHERE c.name IN $components\n                        WITH t, type(r) as relation_type, collect(c.name) as matched_components\n                        RETURN t.name as project_name, \n                               t.testCycle as test_cycle,\n                               matched_components,\n                               size(matched_components) as match_count\n                        ORDER BY match_count DESC\n                    '
127.0.0.1 - - [08/Jul/2025 07:01:51] "POST /api/generate-dvp HTTP/1.1" 200 -
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
🔍 从Neo4j数据库查询配件信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j驱动未安装，使用模拟连接...
Neo4j模拟连接成功!
⚠️ Neo4j查询失败: 'NoneType' object has no attribute 'session'
📋 使用备用数据源...
✅ 从备用数据源查询到 10 个配件
🔍 从Neo4j数据库查询配件信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j驱动未安装，使用模拟连接...
Neo4j模拟连接成功!
⚠️ Neo4j查询失败: 'NoneType' object has no attribute 'session'
📋 使用备用数据源...
✅ 从备用数据源查询到 10 个配件
🔍 从Neo4j数据库查询测试项目信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j驱动未安装，使用模拟连接...
Neo4j模拟连接成功!
⚠️ Neo4j查询失败: 'NoneType' object has no attribute 'session'
📋 使用备用数据源...
✅ 从备用数据源查询到 13 个测试项目
🎯 收到DVP生成请求: ADAS L2级配置
🚀 开始生成DVP方案: ADAS L2级配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 查询Neo4j数据库匹配试验项目...
🎯 用户选择的配件组合: ['AD摄像头前视/后视/侧视', 'DPB+ESP/IPB', 'EPS', '毫米波雷达']
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j驱动未安装，使用模拟连接...
Neo4j模拟连接成功!
⚠️ Neo4j查询失败: 'NoneType' object has no attribute 'session'
📋 使用备用项目匹配...
🎯 过滤后保留 7 个高质量匹配项目 (阈值: 0.3)
📊 从Neo4j数据库匹配到 7 个试验项目
🤖 开始LLM DVP推理生成...
🤖 发送推理请求给LLM...
LLM API调用错误: The read operation timed out
🤖 LLM推理完成，响应长度: 37 字符
⚠️ LLM响应格式不正确，使用备用计算
📊 使用备用车辆计算方法...
🚗 LLM推理生成车辆数量: 10 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
⚠️ AI排期计算失败: 'complexity'
❌ DVP生成失败: 'complexity'
🎯 收到DVP生成请求: ADAS L2+配置
🚀 开始生成DVP方案: ADAS L2+配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 查询Neo4j数据库匹配试验项目...
🎯 用户选择的配件组合: ['AD摄像头前视/后视/侧视', 'DPB+ESP/IPB', 'EPS', '毫米波雷达', '激光雷达', '环视摄像头']
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j驱动未安装，使用模拟连接...
Neo4j模拟连接成功!
⚠️ Neo4j查询失败: 'NoneType' object has no attribute 'session'
📋 使用备用项目匹配...
🎯 过滤后保留 9 个高质量匹配项目 (阈值: 0.3)
📊 从Neo4j数据库匹配到 9 个试验项目
🤖 开始LLM DVP推理生成...
🤖 发送推理请求给LLM...
LLM API调用错误: The read operation timed out
🤖 LLM推理完成，响应长度: 37 字符
⚠️ LLM响应格式不正确，使用备用计算
📊 使用备用车辆计算方法...
🚗 LLM推理生成车辆数量: 12 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
⚠️ AI排期计算失败: 'complexity'
❌ DVP生成失败: 'complexity'
🔍 从Neo4j数据库查询配件信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
🔍 从Neo4j数据库查询测试项目信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
📋 使用备用数据源...
✅ 从备用数据源查询到 10 个配件
Neo4j连接成功!
✅ 从Neo4j查询到 13 个测试项目
🎯 收到DVP生成请求: ADAS L2级配置
🚀 开始生成DVP方案: ADAS L2级配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 查询Neo4j数据库匹配试验项目...
🎯 用户选择的配件组合: ['AD摄像头前视/后视/侧视', 'DPB+ESP/IPB', 'EPS', '毫米波雷达']
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 0 个匹配项目
🎯 过滤后保留 0 个高质量匹配项目 (阈值: 0.3)
📊 从Neo4j数据库匹配到 0 个试验项目
🤖 开始LLM DVP推理生成...
🚗 LLM推理生成车辆数量: 0 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 0 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_231130.json'
🎯 收到DVP生成请求: ADAS L2级配置
🚀 开始生成DVP方案: ADAS L2级配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 查询Neo4j数据库匹配试验项目...
🎯 用户选择的配件组合: ['AD摄像头前视/后视/侧视', 'DPB+ESP/IPB', 'EPS', '毫米波雷达']
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 0 个匹配项目
🎯 过滤后保留 0 个高质量匹配项目 (阈值: 0.3)
📊 从Neo4j数据库匹配到 0 个试验项目
🤖 开始LLM DVP推理生成...
🚗 LLM推理生成车辆数量: 0 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 0 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250707_231534.json'
🔍 从Neo4j数据库查询配件信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
🔍 从Neo4j数据库查询测试项目信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
Neo4j连接成功!
📋 使用备用数据源...
✅ 从备用数据源查询到 10 个配件
✅ 从Neo4j查询到 13 个测试项目
🎯 收到DVP生成请求: ADAS L2级配置
🚀 开始生成DVP方案: ADAS L2级配置
📅 DVP执行时间窗口: 2025-07-07 → 2026-01-07
🔍 查询Neo4j数据库匹配试验项目...
🎯 用户选择的配件组合: ['AD摄像头前视/后视/侧视', 'DPB+ESP/IPB', 'EPS', '毫米波雷达']
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 0 个匹配项目
🎯 过滤后保留 0 个高质量匹配项目 (阈值: 0.3)
📊 从Neo4j数据库匹配到 0 个试验项目
🤖 开始LLM DVP推理生成...
🚗 LLM推理生成车辆数量: 0 台
⏰ 排期时间窗口: 2025-07-07 至 2026-01-07 (184天)
🤖 使用AI生成智能排期...
LLM API调用错误: The read operation timed out
📅 生成测试排期，总周期: 0 天
保存报告失败: [Errno 2] No such file or directory: 'reports/dvp_report_ai_DVP_AI_20250708_070151.json'
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: testCycle)} {position: line: 3, column: 50, offset: 96} for query: '\n                        MATCH (t:TestProject)\n                        RETURN t.name as name, t.testCycle as testCycle\n                        ORDER BY t.name\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 1, column: 10, offset: 9} for query: 'MATCH (c:Component) RETURN c.name as name ORDER BY c.name'
127.0.0.1 - - [08/Jul/2025 11:35:30] "GET /api/get-components HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
127.0.0.1 - - [08/Jul/2025 11:35:32] "GET /api/get-test-projects HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 1, column: 10, offset: 9} for query: 'MATCH (c:Component) RETURN c.name as name ORDER BY c.name'
127.0.0.1 - - [08/Jul/2025 11:35:46] "GET /api/get-components HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: testCycle)} {position: line: 3, column: 50, offset: 96} for query: '\n                        MATCH (t:TestProject)\n                        RETURN t.name as name, t.testCycle as testCycle\n                        ORDER BY t.name\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
127.0.0.1 - - [08/Jul/2025 11:35:49] "GET /api/get-test-projects HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 66} for query: '\n                        MATCH (n)\n                        RETURN id(n) as id, labels(n) as labels, properties(n) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 75} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 52, offset: 95} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
127.0.0.1 - - [08/Jul/2025 11:35:53] "GET /api/get-knowledge-graph HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 66} for query: '\n                        MATCH (n)\n                        RETURN id(n) as id, labels(n) as labels, properties(n) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 75} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 52, offset: 95} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
127.0.0.1 - - [08/Jul/2025 11:35:55] "GET /api/get-knowledge-graph HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 66} for query: '\n                        MATCH (n)\n                        RETURN id(n) as id, labels(n) as labels, properties(n) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 75} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 52, offset: 95} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
127.0.0.1 - - [08/Jul/2025 11:36:00] "GET /api/get-knowledge-graph HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 66} for query: '\n                        MATCH (n)\n                        RETURN id(n) as id, labels(n) as labels, properties(n) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 75} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 52, offset: 95} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
127.0.0.1 - - [08/Jul/2025 11:36:03] "GET /api/get-knowledge-graph HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 66} for query: '\n                        MATCH (n)\n                        RETURN id(n) as id, labels(n) as labels, properties(n) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 75} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 52, offset: 95} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
127.0.0.1 - - [08/Jul/2025 11:36:14] "GET /api/get-knowledge-graph HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 66} for query: '\n                        MATCH (n)\n                        RETURN id(n) as id, labels(n) as labels, properties(n) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 75} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 52, offset: 95} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
127.0.0.1 - - [08/Jul/2025 11:36:19] "GET /api/get-knowledge-graph HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: testCycle)} {position: line: 3, column: 50, offset: 96} for query: '\n                        MATCH (t:TestProject)\n                        RETURN t.name as name, t.testCycle as testCycle\n                        ORDER BY t.name\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 1, column: 10, offset: 9} for query: 'MATCH (c:Component) RETURN c.name as name ORDER BY c.name'
127.0.0.1 - - [08/Jul/2025 11:36:32] "GET /api/get-components HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
127.0.0.1 - - [08/Jul/2025 11:36:33] "GET /api/get-test-projects HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: testCycle)} {position: line: 3, column: 50, offset: 96} for query: '\n                        MATCH (t:TestProject)\n                        RETURN t.name as name, t.testCycle as testCycle\n                        ORDER BY t.name\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
127.0.0.1 - - [08/Jul/2025 11:36:38] "GET /api/get-test-projects HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 1, column: 10, offset: 9} for query: 'MATCH (c:Component) RETURN c.name as name ORDER BY c.name'
127.0.0.1 - - [08/Jul/2025 11:36:38] "GET /api/get-components HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 66} for query: '\n                        MATCH (n)\n                        RETURN id(n) as id, labels(n) as labels, properties(n) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 75} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 52, offset: 95} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
127.0.0.1 - - [08/Jul/2025 11:36:38] "GET /api/get-knowledge-graph HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 66} for query: '\n                        MATCH (n)\n                        RETURN id(n) as id, labels(n) as labels, properties(n) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 75} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 52, offset: 95} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
127.0.0.1 - - [08/Jul/2025 11:36:53] "GET /api/get-knowledge-graph HTTP/1.1" 200 -
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownPropertyKeyWarning} {category: UNRECOGNIZED} {title: The provided property key is not in the database} {description: One of the property names in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing property name is: testCycle)} {position: line: 3, column: 50, offset: 96} for query: '\n                        MATCH (t:TestProject)\n                        RETURN t.name as name, t.testCycle as testCycle\n                        ORDER BY t.name\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.UnknownLabelWarning} {category: UNRECOGNIZED} {title: The provided label is not in the database.} {description: One of the labels in your query is not available in the database, make sure you didn't misspell it or that the label is available when you run this statement in your application (the missing label name is: Component)} {position: line: 2, column: 81, offset: 81} for query: '\n                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)\n                            RETURN c.name as component, type(r) as relation_type\n                        '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 66} for query: '\n                        MATCH (n)\n                        RETURN id(n) as id, labels(n) as labels, properties(n) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 32, offset: 75} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
Received notification from DBMS server: {severity: WARNING} {code: Neo.ClientNotification.Statement.FeatureDeprecationWarning} {category: DEPRECATION} {title: This feature is deprecated and will be removed in future versions.} {description: The query used a deprecated function. ('id' has been replaced by 'elementId or consider using an application-generated id')} {position: line: 3, column: 52, offset: 95} for query: '\n                        MATCH (a)-[r]->(b)\n                        RETURN id(a) as source_id, id(b) as target_id, type(r) as relation_type, properties(r) as properties\n                    '
127.0.0.1 - - [08/Jul/2025 11:37:01] "GET /api/get-knowledge-graph HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 11:38:59] "GET /api/get-test-projects HTTP/1.1" 200 -
127.0.0.1 - - [08/Jul/2025 11:38:59] "GET /api/get-components HTTP/1.1" 200 -
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/dvp_api_server.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5000
🔍 从Neo4j数据库查询配件信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
🔍 从Neo4j数据库查询测试项目信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
Neo4j连接成功!
📋 使用备用数据源...
✅ 从备用数据源查询到 10 个配件
✅ 从Neo4j查询到 13 个测试项目
🔍 从Neo4j数据库查询配件信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
🔍 从Neo4j数据库查询测试项目信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
Neo4j连接成功!
📋 使用备用数据源...
✅ 从备用数据源查询到 10 个配件
✅ 从Neo4j查询到 13 个测试项目
🔍 从Neo4j数据库查询知识图谱数据...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 23 个节点, 53 个关系
🔍 从Neo4j数据库查询知识图谱数据...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 23 个节点, 53 个关系
🔍 从Neo4j数据库查询知识图谱数据...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 23 个节点, 53 个关系
🔍 从Neo4j数据库查询知识图谱数据...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 23 个节点, 53 个关系
🔍 从Neo4j数据库查询知识图谱数据...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 23 个节点, 53 个关系
🔍 从Neo4j数据库查询知识图谱数据...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 23 个节点, 53 个关系
🔍 从Neo4j数据库查询配件信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
🔍 从Neo4j数据库查询测试项目信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
Neo4j连接成功!
📋 使用备用数据源...
✅ 从备用数据源查询到 10 个配件
✅ 从Neo4j查询到 13 个测试项目
🔍 从Neo4j数据库查询配件信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
🔍 从Neo4j数据库查询测试项目信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
Neo4j连接成功!
🔍 从Neo4j数据库查询知识图谱数据...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
⚠️ Neo4j查询失败: Failed to read from defunct connection IPv4Address(('**************', 27687)) (ResolvedIPv4Address(('**************', 27687)))
📋 使用备用数据源...
✅ 从备用数据源查询到 13 个测试项目
📋 使用备用数据源...
✅ 从备用数据源查询到 10 个配件
Neo4j连接成功!
✅ 从Neo4j查询到 23 个节点, 53 个关系
🔍 从Neo4j数据库查询知识图谱数据...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 23 个节点, 53 个关系
🔍 从Neo4j数据库查询配件信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
🔍 从Neo4j数据库查询测试项目信息...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
Neo4j连接成功!
🔍 从Neo4j数据库查询知识图谱数据...
正在连接到Neo4j数据库...
URI: bolt://**************:27687
用户名: neo4j
Neo4j连接成功!
✅ 从Neo4j查询到 23 个节点, 53 个关系
⚠️ Neo4j查询失败: Failed to read from defunct connection IPv4Address(('**************', 27687)) (ResolvedIPv4Address(('**************', 27687)))
📋 使用备用数据源...
✅ 从备用数据源查询到 13 个测试项目
⚠️ Neo4j查询失败: Failed to read from defunct connection IPv4Address(('**************', 27687)) (ResolvedIPv4Address(('**************', 27687)))
📋 使用备用数据源...
✅ 从备用数据源查询到 10 个配件
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/setup_neo4j_knowledge_graph.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5001
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/llm_client.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5001
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
 * Detected change in '/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/neo4j_connector.py', reloading
矩阵数据加载失败：找不到parsed_matrix_data.json文件
✅ 矩阵数据加载成功: /Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/claude-code/../data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5001
 * Restarting with stat
 * Debugger is active!
 * Debugger PIN: 281-256-029
/usr/local/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/multiprocessing/resource_tracker.py:301: UserWarning: resource_tracker: There appear to be 1 leaked semaphore objects to clean up at shutdown: {'/mp-5kpslxp1'}
  warnings.warn(
