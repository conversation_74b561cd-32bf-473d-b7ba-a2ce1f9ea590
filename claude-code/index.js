// DVP项目工具函数 - 浏览器兼容版本
(function() {
    // 项目配置对象
    const DVPProject = {
        // 项目根目录相对路径
        projectRoot: '..',
        
        // 获取项目信息
        getProjectInfo: function() {
            return {
                name: 'DVP-AI 算法生成 POC',
                version: '1.0.0',
                description: '基于AI的DVP测试方案生成系统'
            };
        },
        
        // 获取相对路径
        getRelativePath: function(dir) {
            return this.projectRoot + '/' + dir;
        }
    };
    
    // 如果在浏览器环境中，挂载到全局对象
    if (typeof window !== 'undefined') {
        window.DVPProject = DVPProject;
    }
    
    // 如果支持 CommonJS，则导出模块
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = DVPProject;
    }
})();