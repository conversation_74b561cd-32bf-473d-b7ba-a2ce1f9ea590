{"relationships": [{"test_project": "超声波雷达标定匹配", "required": true, "component": "超声波雷达", "relationship": "✅"}, {"test_project": "超声波雷达标定匹配", "required": false, "component": "FSD变更", "relationship": "×"}, {"test_project": "超声波雷达标定匹配", "required": false, "component": "激光雷达", "relationship": "×"}, {"test_project": "超声波雷达标定匹配", "required": false, "component": "毫米波雷达", "relationship": "×"}, {"test_project": "超声波雷达标定匹配", "required": false, "component": "AD摄像头前视/后视/侧视", "relationship": "×"}, {"test_project": "超声波雷达标定匹配", "required": false, "component": "环视摄像头", "relationship": "×"}, {"test_project": "超声波雷达标定匹配", "required": false, "component": "DPB+ESP/IPB", "relationship": "×"}, {"test_project": "超声波雷达标定匹配", "required": false, "component": "EPS", "relationship": "×"}, {"test_project": "超声波雷达标定匹配", "required": false, "component": "后轮转向", "relationship": "×"}, {"test_project": "超声波雷达标定匹配", "required": false, "component": "悬架", "relationship": "×"}, {"test_project": "毫米波雷达标定匹配", "required": true, "component": "毫米波雷达", "relationship": "✅"}, {"test_project": "毫米波雷达标定匹配", "required": false, "component": "FSD变更", "relationship": "×"}, {"test_project": "毫米波雷达标定匹配", "required": false, "component": "激光雷达", "relationship": "×"}, {"test_project": "毫米波雷达标定匹配", "required": false, "component": "超声波雷达", "relationship": "×"}, {"test_project": "毫米波雷达标定匹配", "required": false, "component": "AD摄像头前视/后视/侧视", "relationship": "×"}, {"test_project": "毫米波雷达标定匹配", "required": false, "component": "环视摄像头", "relationship": "×"}, {"test_project": "毫米波雷达标定匹配", "required": false, "component": "DPB+ESP/IPB", "relationship": "×"}, {"test_project": "毫米波雷达标定匹配", "required": false, "component": "EPS", "relationship": "×"}, {"test_project": "毫米波雷达标定匹配", "required": false, "component": "后轮转向", "relationship": "×"}, {"test_project": "毫米波雷达标定匹配", "required": false, "component": "悬架", "relationship": "×"}, {"test_project": "激光雷达标定匹配", "required": true, "component": "激光雷达", "relationship": "✅"}, {"test_project": "激光雷达标定匹配", "required": false, "component": "FSD变更", "relationship": "×"}, {"test_project": "激光雷达标定匹配", "required": false, "component": "毫米波雷达", "relationship": "×"}, {"test_project": "激光雷达标定匹配", "required": false, "component": "超声波雷达", "relationship": "×"}, {"test_project": "激光雷达标定匹配", "required": false, "component": "AD摄像头前视/后视/侧视", "relationship": "×"}, {"test_project": "激光雷达标定匹配", "required": false, "component": "环视摄像头", "relationship": "×"}, {"test_project": "激光雷达标定匹配", "required": false, "component": "DPB+ESP/IPB", "relationship": "×"}, {"test_project": "激光雷达标定匹配", "required": false, "component": "EPS", "relationship": "×"}, {"test_project": "激光雷达标定匹配", "required": false, "component": "后轮转向", "relationship": "×"}, {"test_project": "激光雷达标定匹配", "required": false, "component": "悬架", "relationship": "×"}, {"test_project": "定位IMU模块标定匹配", "required": true, "component": "FSD变更", "relationship": "✅"}, {"test_project": "定位IMU模块标定匹配", "required": false, "component": "激光雷达", "relationship": "×"}, {"test_project": "定位IMU模块标定匹配", "required": false, "component": "毫米波雷达", "relationship": "×"}, {"test_project": "定位IMU模块标定匹配", "required": false, "component": "超声波雷达", "relationship": "×"}, {"test_project": "定位IMU模块标定匹配", "required": false, "component": "AD摄像头前视/后视/侧视", "relationship": "×"}, {"test_project": "定位IMU模块标定匹配", "required": false, "component": "环视摄像头", "relationship": "×"}, {"test_project": "定位IMU模块标定匹配", "required": false, "component": "DPB+ESP/IPB", "relationship": "×"}, {"test_project": "定位IMU模块标定匹配", "required": false, "component": "EPS", "relationship": "×"}, {"test_project": "定位IMU模块标定匹配", "required": false, "component": "后轮转向", "relationship": "×"}, {"test_project": "定位IMU模块标定匹配", "required": false, "component": "悬架", "relationship": "×"}, {"test_project": "感知数采", "required": true, "component": "FSD变更", "relationship": "✅"}, {"test_project": "感知数采", "required": true, "component": "激光雷达", "relationship": "✅"}, {"test_project": "感知数采", "required": false, "component": "毫米波雷达", "relationship": "×"}, {"test_project": "感知数采", "required": false, "component": "超声波雷达", "relationship": "×"}, {"test_project": "感知数采", "required": true, "component": "AD摄像头前视/后视/侧视", "relationship": "✅"}, {"test_project": "感知数采", "required": true, "component": "环视摄像头", "relationship": "✅"}, {"test_project": "感知数采", "required": false, "component": "DPB+ESP/IPB", "relationship": "×"}, {"test_project": "感知数采", "required": false, "component": "EPS", "relationship": "×"}, {"test_project": "感知数采", "required": false, "component": "后轮转向", "relationship": "×"}, {"test_project": "感知数采", "required": false, "component": "悬架", "relationship": "×"}, {"test_project": "感知开发", "required": true, "component": "FSD变更", "relationship": "✅"}, {"test_project": "感知开发", "required": true, "component": "激光雷达", "relationship": "✅"}, {"test_project": "感知开发", "required": true, "component": "毫米波雷达", "relationship": "✅"}, {"test_project": "感知开发", "required": true, "component": "超声波雷达", "relationship": "✅"}, {"test_project": "感知开发", "required": true, "component": "AD摄像头前视/后视/侧视", "relationship": "✅"}, {"test_project": "感知开发", "required": true, "component": "环视摄像头", "relationship": "✅"}, {"test_project": "感知开发", "required": false, "component": "DPB+ESP/IPB", "relationship": "×"}, {"test_project": "感知开发", "required": false, "component": "EPS", "relationship": "×"}, {"test_project": "感知开发", "required": false, "component": "后轮转向", "relationship": "×"}, {"test_project": "感知开发", "required": false, "component": "悬架", "relationship": "×"}, {"test_project": "产线标定", "required": true, "component": "FSD变更", "relationship": "✅"}, {"test_project": "产线标定", "required": true, "component": "激光雷达", "relationship": "✅"}, {"test_project": "产线标定", "required": true, "component": "毫米波雷达", "relationship": "✅"}, {"test_project": "产线标定", "required": true, "component": "超声波雷达", "relationship": "✅"}, {"test_project": "产线标定", "required": true, "component": "AD摄像头前视/后视/侧视", "relationship": "✅"}, {"test_project": "产线标定", "required": true, "component": "环视摄像头", "relationship": "✅"}, {"test_project": "产线标定", "required": false, "component": "DPB+ESP/IPB", "relationship": "×"}, {"test_project": "产线标定", "required": false, "component": "EPS", "relationship": "×"}, {"test_project": "产线标定", "required": false, "component": "后轮转向", "relationship": "×"}, {"test_project": "产线标定", "required": false, "component": "悬架", "relationship": "×"}, {"test_project": "底软开发", "required": true, "component": "FSD变更", "relationship": "✅"}, {"test_project": "底软开发", "required": false, "component": "激光雷达", "relationship": "×"}, {"test_project": "底软开发", "required": false, "component": "毫米波雷达", "relationship": "×"}, {"test_project": "底软开发", "required": false, "component": "超声波雷达", "relationship": "×"}, {"test_project": "底软开发", "required": false, "component": "AD摄像头前视/后视/侧视", "relationship": "×"}, {"test_project": "底软开发", "required": false, "component": "环视摄像头", "relationship": "×"}, {"test_project": "底软开发", "required": false, "component": "DPB+ESP/IPB", "relationship": "×"}, {"test_project": "底软开发", "required": false, "component": "EPS", "relationship": "×"}, {"test_project": "底软开发", "required": false, "component": "后轮转向", "relationship": "×"}, {"test_project": "底软开发", "required": false, "component": "悬架", "relationship": "×"}, {"test_project": "数据回传", "required": true, "component": "FSD变更", "relationship": "✅"}, {"test_project": "数据回传", "required": false, "component": "激光雷达", "relationship": "×"}, {"test_project": "数据回传", "required": false, "component": "毫米波雷达", "relationship": "×"}, {"test_project": "数据回传", "required": false, "component": "超声波雷达", "relationship": "×"}, {"test_project": "数据回传", "required": true, "component": "AD摄像头前视/后视/侧视", "relationship": "✅"}, {"test_project": "数据回传", "required": true, "component": "环视摄像头", "relationship": "✅"}, {"test_project": "数据回传", "required": false, "component": "DPB+ESP/IPB", "relationship": "×"}, {"test_project": "数据回传", "required": false, "component": "EPS", "relationship": "×"}, {"test_project": "数据回传", "required": false, "component": "后轮转向", "relationship": "×"}, {"test_project": "数据回传", "required": false, "component": "悬架", "relationship": "×"}, {"test_project": "地图定位", "required": true, "component": "FSD变更", "relationship": "✅"}, {"test_project": "地图定位", "required": false, "component": "激光雷达", "relationship": "×"}, {"test_project": "地图定位", "required": false, "component": "毫米波雷达", "relationship": "×"}, {"test_project": "地图定位", "required": false, "component": "超声波雷达", "relationship": "×"}, {"test_project": "地图定位", "required": false, "component": "AD摄像头前视/后视/侧视", "relationship": "×"}, {"test_project": "地图定位", "required": false, "component": "环视摄像头", "relationship": "×"}, {"test_project": "地图定位", "required": false, "component": "DPB+ESP/IPB", "relationship": "×"}, {"test_project": "地图定位", "required": false, "component": "EPS", "relationship": "×"}, {"test_project": "地图定位", "required": false, "component": "后轮转向", "relationship": "×"}, {"test_project": "地图定位", "required": false, "component": "悬架", "relationship": "×"}, {"test_project": "行泊主功能开发", "required": true, "component": "FSD变更", "relationship": "✅"}, {"test_project": "行泊主功能开发", "required": true, "component": "激光雷达", "relationship": "✅"}, {"test_project": "行泊主功能开发", "required": true, "component": "毫米波雷达", "relationship": "✅"}, {"test_project": "行泊主功能开发", "required": true, "component": "超声波雷达", "relationship": "✅"}, {"test_project": "行泊主功能开发", "required": true, "component": "AD摄像头前视/后视/侧视", "relationship": "✅"}, {"test_project": "行泊主功能开发", "required": true, "component": "环视摄像头", "relationship": "✅"}, {"test_project": "行泊主功能开发", "required": true, "component": "DPB+ESP/IPB", "relationship": "✅"}, {"test_project": "行泊主功能开发", "required": true, "component": "EPS", "relationship": "✅"}, {"test_project": "行泊主功能开发", "required": true, "component": "后轮转向", "relationship": "✅"}, {"test_project": "行泊主功能开发", "required": true, "component": "悬架", "relationship": "✅"}, {"test_project": "功能集成测试", "required": true, "component": "FSD变更", "relationship": "✅"}, {"test_project": "功能集成测试", "required": true, "component": "激光雷达", "relationship": "✅"}, {"test_project": "功能集成测试", "required": true, "component": "毫米波雷达", "relationship": "✅"}, {"test_project": "功能集成测试", "required": true, "component": "超声波雷达", "relationship": "✅"}, {"test_project": "功能集成测试", "required": true, "component": "AD摄像头前视/后视/侧视", "relationship": "✅"}, {"test_project": "功能集成测试", "required": true, "component": "环视摄像头", "relationship": "✅"}, {"test_project": "功能集成测试", "required": true, "component": "DPB+ESP/IPB", "relationship": "✅"}, {"test_project": "功能集成测试", "required": true, "component": "EPS", "relationship": "✅"}, {"test_project": "功能集成测试", "required": true, "component": "后轮转向", "relationship": "✅"}, {"test_project": "功能集成测试", "required": true, "component": "悬架", "relationship": "✅"}, {"test_project": "泛化路试", "required": true, "component": "FSD变更", "relationship": "✅"}, {"test_project": "泛化路试", "required": true, "component": "激光雷达", "relationship": "✅"}, {"test_project": "泛化路试", "required": true, "component": "毫米波雷达", "relationship": "✅"}, {"test_project": "泛化路试", "required": true, "component": "超声波雷达", "relationship": "✅"}, {"test_project": "泛化路试", "required": true, "component": "AD摄像头前视/后视/侧视", "relationship": "✅"}, {"test_project": "泛化路试", "required": true, "component": "环视摄像头", "relationship": "✅"}, {"test_project": "泛化路试", "required": true, "component": "DPB+ESP/IPB", "relationship": "✅"}, {"test_project": "泛化路试", "required": true, "component": "EPS", "relationship": "✅"}, {"test_project": "泛化路试", "required": true, "component": "后轮转向", "relationship": "✅"}, {"test_project": "泛化路试", "required": true, "component": "悬架", "relationship": "✅"}], "test_projects": ["超声波雷达标定匹配", "毫米波雷达标定匹配", "激光雷达标定匹配", "定位IMU模块标定匹配", "感知数采", "感知开发", "产线标定", "底软开发", "数据回传", "地图定位", "行泊主功能开发", "功能集成测试", "泛化路试"], "components": ["FSD变更", "激光雷达", "毫米波雷达", "超声波雷达", "AD摄像头前视/后视/侧视", "环视摄像头", "DPB+ESP/IPB", "EPS", "后轮转向", "悬架"], "test_cycles": {"超声波雷达标定匹配": 3, "毫米波雷达标定匹配": 5, "激光雷达标定匹配": 5, "定位IMU模块标定匹配": 7, "感知数采": 10, "感知开发": 15, "产线标定": 5, "底软开发": 20, "数据回传": 8, "地图定位": 12, "行泊主功能开发": 25, "功能集成测试": 20, "泛化路试": 30}, "vehicle_baselines": {"超声波雷达标定匹配": 1, "毫米波雷达标定匹配": 1, "激光雷达标定匹配": 1, "定位IMU模块标定匹配": 1, "感知数采": 2, "感知开发": 2, "产线标定": 1, "底软开发": 1, "数据回传": 1, "地图定位": 1, "行泊主功能开发": 8, "功能集成测试": 6, "泛化路试": 10}}