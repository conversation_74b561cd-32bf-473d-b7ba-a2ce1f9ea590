# -*- coding: utf-8 -*-
"""
DVP API服务器
连接前端界面和后端服务，集成真实的Neo4j和SiliconCloud API
"""

import json
import os
from datetime import datetime, timedelta
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS

# 导入项目模块
from enhanced_dvp_generator import EnhancedDVPGenerator
from llm_client import SiliconCloudClient
from neo4j_connector import Neo4jConnector

app = Flask(__name__)
CORS(app)

class DVPAPIServer:
    """DVP API服务器"""

    def __init__(self):
        self.dvp_generator = EnhancedDVPGenerator()
        self.llm_client = SiliconCloudClient()
        self.neo4j_client = Neo4jConnector()
        self.matrix_data = self.load_matrix_data()

    def load_matrix_data(self):
        """加载PDF矩阵数据"""
        try:
            # 尝试不同路径
            data_paths = [
                "data/parsed_matrix_data.json",
                "parsed_matrix_data.json",
                os.path.join(os.path.dirname(__file__), "data", "parsed_matrix_data.json")
            ]

            for path in data_paths:
                try:
                    with open(path, "r", encoding='utf-8') as f:
                        data = json.load(f)
                        print(f"✅ 矩阵数据加载成功: {path}")
                        return data
                except FileNotFoundError:
                    continue

            print("⚠️ 未找到矩阵数据文件，使用默认数据")
            return {"relationships": []}

        except Exception as e:
            print(f"❌ 加载矩阵数据失败: {e}")
            return {"relationships": []}

    def generate_dvp_with_ai(self, config):
        """使用AI生成DVP方案"""
        print(f"🚀 开始生成DVP方案: {config['configType']}")

        # 获取开始日期和SOP日期
        start_date = config.get('startDate', datetime.now().strftime('%Y-%m-%d'))
        sop_date = config.get('sopDate', (datetime.now() + timedelta(days=180)).strftime('%Y-%m-%d'))

        print(f"📅 DVP执行时间窗口: {start_date} → {sop_date}")

        # 1. 从Neo4j数据库查询匹配的试验项目
        matched_projects = self.query_neo4j_for_matched_projects(config['components'])
        print(f"📊 从Neo4j数据库匹配到 {len(matched_projects)} 个试验项目")

        # 2. 使用LLM基于Neo4j查询结果和用户参数进行DVP推理生成
        vehicle_calculation = self.llm_dvp_reasoning(matched_projects, config)
        print(f"🚗 LLM推理生成车辆数量: {vehicle_calculation['totalVehicles']} 台")

        # 3. 生成智能测试排期
        schedule = self.generate_schedule_with_ai(matched_projects, start_date, sop_date, vehicle_calculation['totalVehicles'])
        print(f"📅 生成测试排期，总周期: {schedule['totalDuration']} 天")

        # 4. 不再生成成本分析（缺乏真实数据源）

        # 5. 组装完整DVP报告
        dvp_report = {
            "id": f"DVP_AI_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "generatedDate": datetime.now().isoformat(),
            "config": config,
            "matchedProjects": matched_projects,
            "vehicleCalculation": vehicle_calculation,
            "schedule": schedule,
            "summary": {
                "totalProjects": len(matched_projects),
                "totalVehicles": vehicle_calculation['totalVehicles'],
                "totalDuration": schedule['totalDuration'],
                "riskLevel": vehicle_calculation['totalVehicles'] > 20 and '高' or (vehicle_calculation['totalVehicles'] > 10 and '中' or '低'),
                "aiPowered": True,
                "dataSource": "PDF业务逻辑矩阵 + Neo4j知识图谱"
            }
        }

        # 6. 保存报告
        self.save_report(dvp_report)

        return dvp_report

    def query_neo4j_for_matched_projects(self, components):
        """从Neo4j数据库查询匹配的测试项目"""
        print("🔍 查询Neo4j数据库匹配试验项目...")
        print(f"🎯 用户选择的配件组合: {components}")

        matched_projects = []

        # 尝试从Neo4j查询
        if self.neo4j_client.connect():
            try:
                with self.neo4j_client.driver.session() as session:
                    # 查询匹配的测试项目
                    cypher_query = """
                        MATCH (t:TestProject)-[r]->(c:Component)
                        WHERE c.name IN $components
                        WITH t, type(r) as relation_type, collect(c.name) as matched_components
                        RETURN t.name as project_name,
                               t.testCycle as test_cycle,
                               matched_components,
                               size(matched_components) as match_count
                        ORDER BY match_count DESC
                    """

                    result = session.run(cypher_query, components=components)

                    for record in result:
                        project_name = record["project_name"]
                        test_cycle = record["test_cycle"]
                        matched_components = record["matched_components"]
                        match_count = record["match_count"]

                        # 查询该项目的所有依赖组件
                        total_components_query = """
                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)
                            RETURN count(c) as total_components
                        """
                        total_result = session.run(total_components_query, project_name=project_name)
                        total_components = total_result.single()["total_components"]

                        # 计算匹配度
                        match_ratio = match_count / max(total_components, 1)

                        project_info = {
                            "name": project_name,
                            "testCycle": test_cycle,
                            "matchedComponents": matched_components,
                            "matchRatio": match_ratio,
                            "matchCount": match_count,
                            "totalComponents": total_components,
                            "source": "Neo4j数据库"
                        }

                        matched_projects.append(project_info)

                    print(f"✅ 从Neo4j查询到 {len(matched_projects)} 个匹配项目")

            except Exception as neo4j_error:
                print(f"⚠️ Neo4j查询失败: {neo4j_error}")
                # 使用备用查询方法
                matched_projects = self.fallback_match_projects(components)
        else:
            # 使用备用查询方法
            matched_projects = self.fallback_match_projects(components)

        # 过滤项目
        min_match_ratio = 0.3
        filtered_projects = [p for p in matched_projects if p["matchRatio"] >= min_match_ratio]

        print(f"🎯 过滤后保留 {len(filtered_projects)} 个高质量匹配项目 (阈值: {min_match_ratio})")

        return filtered_projects

    def fallback_match_projects(self, components):
        """备用项目匹配方法（使用本地矩阵数据）"""
        print("📋 使用备用项目匹配...")

        project_matches = {}

        # 遍历矩阵关系数据
        for relationship in self.matrix_data["relationships"]:
            if relationship["relationship"] == "✅":
                test_project = relationship["test_project"]
                component = relationship["component"]

                # 精确匹配用户选择的组件
                if component in components:
                    if test_project not in project_matches:
                        project_matches[test_project] = {
                            "name": test_project,
                            "matchedComponents": [],
                            "testCycle": self.matrix_data.get("test_cycles", {}).get(test_project, 10),
                            "source": "备用数据源"
                        }

                    project_matches[test_project]["matchedComponents"].append(component)

        # 计算匹配度和添加必需字段
        matched_projects = []
        for project_name, project_info in project_matches.items():
            total_components = self.get_total_components_for_project(project_name)
            match_ratio = len(project_info["matchedComponents"]) / max(total_components, 1)

            # 确定复杂度
            complexity = self.determine_complexity_from_matrix(project_name)

            # 基于复杂度计算推荐车辆数量
            complexity_factor = {"高": 3, "中": 2, "低": 1}[complexity]
            component_factor = len(project_info["matchedComponents"]) // 2 + 1
            recommended_vehicles = max(1, complexity_factor + component_factor)

            project_info["matchRatio"] = match_ratio
            project_info["matchCount"] = len(project_info["matchedComponents"])
            project_info["totalComponents"] = total_components
            project_info["complexity"] = complexity
            project_info["recommendedVehicles"] = recommended_vehicles
            project_info["aiCalculatedVehicles"] = recommended_vehicles  # 添加这个字段以保持兼容性

            matched_projects.append(project_info)

        # 按匹配度排序
        matched_projects.sort(key=lambda x: x["matchRatio"], reverse=True)

        return matched_projects

    def determine_complexity_from_matrix(self, project_name):
        """从矩阵数据确定项目复杂度"""
        # 根据测试周期和车辆基线确定复杂度
        test_cycle = self.matrix_data.get("test_cycles", {}).get(project_name, 10)
        vehicle_baseline = self.matrix_data.get("vehicle_baselines", {}).get(project_name, 1)

        if test_cycle >= 20 or vehicle_baseline >= 6:
            return "高"
        elif test_cycle >= 10 or vehicle_baseline >= 2:
            return "中"
        else:
            return "低"

    def get_total_components_for_project(self, project_name):
        """获取项目所需的总组件数量"""
        total = 0
        for relationship in self.matrix_data["relationships"]:
            if (relationship["test_project"] == project_name and
                relationship["relationship"] == "✅"):
                total += 1
        return max(total, 3)  # 最少3个组件

    def determine_complexity(self, project_name, component_count):
        """确定项目复杂度"""
        complexity_keywords = {
            "高": ["ACC", "AEB", "APA", "自动", "智能"],
            "低": ["LDW", "TSR", "HMA", "警告", "识别"]
        }

        for level, keywords in complexity_keywords.items():
            if any(keyword in project_name for keyword in keywords):
                return level

        # 根据组件数量判断
        if component_count >= 5:
            return "高"
        elif component_count >= 3:
            return "中"
        else:
            return "低"

    def determine_test_cycle(self, project_name, complexity):
        """确定测试周期"""
        base_cycles = {"高": 20, "中": 12, "低": 8}
        return base_cycles.get(complexity, 12)

    def calculate_project_vehicles(self, project_info):
        """计算单个项目所需车辆数量"""
        complexity_factor = {"高": 3, "中": 2, "低": 1}[project_info["complexity"]]
        component_factor = len(project_info["matchedComponents"]) // 2 + 1
        return max(1, complexity_factor + component_factor)

    def llm_dvp_reasoning(self, matched_projects, config):
        """使用LLM基于Neo4j查询结果和用户参数进行DVP推理生成"""
        print("🤖 开始LLM DVP推理生成...")

        if not matched_projects:
            return {"totalVehicles": 0, "reasoning": "无匹配项目", "aiPowered": False}

        # 提取用户配置参数
        project_type = config.get('complexity', 'standard')     # 项目类型：standard(次发) / comprehensive(首发)
        change_degree = config.get('environment', 'normal')     # 变化程度：normal(单一变化) / extreme(多重变化)
        resource_priority = config.get('priority', 'normal')    # 资源优先级：normal / urgent / low

        # 构建Neo4j查询结果摘要
        neo4j_query_summary = self.build_neo4j_query_summary(matched_projects)

        # 构建用户参数逻辑
        user_params_logic = self.build_user_params_logic(project_type, change_degree, resource_priority)

        # 构建LLM推理提示
        llm_prompt = f"""
你是一位专业的汽车DVP（设计验证计划）专家。请根据以下信息进行DVP方案推理生成：

## Neo4j知识图谱查询结果：
{neo4j_query_summary}

## 用户配置参数计算逻辑：
{user_params_logic}

## 推理要求：
1. **车辆数量计算**：根据测试项目的复杂度、测试周期和用户参数，推理每个项目的合理车辆数量
2. **项目排期建议**：考虑测试周期和项目间的依赖关系
3. **风险评估**：识别潜在的资源冲突和时间风险

## 输出格式：
请返回JSON格式的推理结果：
{{
    "totalVehicles": <总车辆数量>,
    "reasoning": "<详细推理过程>",
    "projectDetails": [
        {{
            "name": "<项目名称>",
            "recommendedVehicles": <推荐车辆数>,
            "reasoning": "<推理依据>"
        }}
    ],
    "riskAssessment": "<风险评估>",
    "scheduleRecommendations": "<排期建议>"
}}

请基于汽车行业的实际经验和最佳实践进行推理。
"""

        try:
            print(f"🤖 发送推理请求给LLM...")
            ai_response = self.llm_client.call_llm(llm_prompt)
            print(f"🤖 LLM推理完成，响应长度: {len(ai_response)} 字符")

            # 尝试解析JSON响应
            if "{" in ai_response and "}" in ai_response:
                json_start = ai_response.find("{")
                json_end = ai_response.rfind("}") + 1
                json_str = ai_response[json_start:json_end]
                ai_result = json.loads(json_str)

                total_vehicles = ai_result.get("totalVehicles", 0)
                if isinstance(total_vehicles, int) and total_vehicles > 0:
                    print(f"✅ LLM推理成功，推荐车辆数量: {total_vehicles}")
                    return {
                        "totalVehicles": total_vehicles,
                        "reasoning": ai_result.get("reasoning", "LLM推理完成"),
                        "projectDetails": ai_result.get("projectDetails", []),
                        "riskAssessment": ai_result.get("riskAssessment", ""),
                        "scheduleRecommendations": ai_result.get("scheduleRecommendations", ""),
                        "aiPowered": True,
                        "dataSource": "Neo4j + LLM推理"
                    }

            # LLM响应格式不正确，使用备用计算
            print("⚠️ LLM响应格式不正确，使用备用计算")
            return self.fallback_vehicle_calculation(matched_projects, project_type, change_degree, resource_priority)

        except Exception as e:
            print(f"⚠️ LLM推理失败: {e}")
            return self.fallback_vehicle_calculation(matched_projects, project_type, change_degree, resource_priority)

    def build_neo4j_query_summary(self, matched_projects):
        """构建Neo4j查询结果摘要"""
        summary_lines = []
        summary_lines.append(f"匹配到 {len(matched_projects)} 个测试项目：")

        for i, project in enumerate(matched_projects, 1):
            summary_lines.append(f"{i}. **{project['name']}**")
            summary_lines.append(f"   - 测试周期: {project['testCycle']} 天")
            summary_lines.append(f"   - 匹配组件: {project['matchedComponents']}")
            summary_lines.append(f"   - 匹配度: {project['matchRatio']:.2f} ({project['matchCount']}/{project['totalComponents']})")
            summary_lines.append(f"   - 数据源: {project['source']}")
            summary_lines.append("")

        return "\n".join(summary_lines)

    def build_user_params_logic(self, project_type, change_degree, resource_priority):
        """构建用户参数逻辑说明"""
        is_first_launch = (project_type == 'comprehensive')
        is_multiple_changes = (change_degree == 'extreme')

        logic_lines = []
        logic_lines.append(f"**项目类型**: {'首发项目' if is_first_launch else '次发项目'}")
        logic_lines.append(f"  - 影响: {'首发项目通常需要更多测试车辆和更长周期' if is_first_launch else '次发项目可复用已有经验，车辆需求相对较少'}")
        logic_lines.append("")

        logic_lines.append(f"**零件变化程度**: {'多重变化' if is_multiple_changes else '单一变化'}")
        logic_lines.append(f"  - 影响: {'多重变化增加测试复杂度，需要额外的验证车辆' if is_multiple_changes else '单一变化风险较低，可使用标准配置'}")
        logic_lines.append("")

        priority_desc = {
            'urgent': '优先资源(+20%)',
            'normal': '标准资源',
            'low': '节约资源(-10%)'
        }
        logic_lines.append(f"**资源优先级**: {priority_desc.get(resource_priority, '标准资源')}")
        logic_lines.append(f"  - 影响: 根据优先级调整最终车辆数量分配")

        return "\n".join(logic_lines)

    def fallback_vehicle_calculation(self, matched_projects, project_type, change_degree, resource_priority):
        """备用车辆计算方法"""
        print("📊 使用备用车辆计算方法...")

        is_first_launch = (project_type == 'comprehensive')
        is_multiple_changes = (change_degree == 'extreme')

        base_vehicles = 0
        project_details = []

        for project in matched_projects:
            # 基于测试周期估算车辆需求
            cycle = project['testCycle']
            if cycle >= 20:
                vehicles = 3 if is_first_launch else 2
            elif cycle >= 10:
                vehicles = 2 if is_first_launch else 1
            else:
                vehicles = 1

            # 多重变化增加车辆需求
            if is_multiple_changes:
                vehicles = int(vehicles * 1.5)

            base_vehicles += vehicles

            project_details.append({
                "name": project['name'],
                "recommendedVehicles": vehicles,
                "reasoning": f"基于{cycle}天测试周期的标准配置"
            })

        # 优先级调整
        priority_multiplier = {'urgent': 1.2, 'normal': 1.0, 'low': 0.9}[resource_priority]
        total_vehicles = int(base_vehicles * priority_multiplier)

        return {
            "totalVehicles": total_vehicles,
            "reasoning": f"备用计算：基础{base_vehicles}台 × 优先级{priority_multiplier} = {total_vehicles}台",
            "projectDetails": project_details,
            "riskAssessment": "使用备用计算方法，建议人工复核",
            "scheduleRecommendations": "建议按测试周期从短到长排序执行",
            "aiPowered": False,
            "dataSource": "备用计算算法"
        }

    def calculate_vehicles_with_ai(self, matched_projects, config):
        """基于PDF B列业务逻辑计算车辆数量"""
        if not matched_projects:
            return {"totalVehicles": 0, "reasoning": "无匹配项目", "aiPowered": False}

        # 提取用户输入参数
        project_type = config.get('complexity', 'standard')     # 项目类型：standard(次发) / comprehensive(首发)
        change_degree = config.get('environment', 'normal')     # 变化程度：normal(单一变化) / extreme(多重变化)
        resource_priority = config.get('priority', 'normal')    # 资源优先级：normal / urgent / low

        # 参数映射说明
        is_first_launch = (project_type == 'comprehensive')
        is_multiple_changes = (change_degree == 'extreme')

        print(f"🎯 DVP计算参数: 项目类型={'首发' if is_first_launch else '次发'}, 变化程度={'多重变化' if is_multiple_changes else '单一变化'}, 资源优先级={resource_priority}")

        # 基于PDF B列逻辑计算车辆数量
        vehicle_calculation = self.calculate_vehicles_by_business_logic(matched_projects, project_type, change_degree, resource_priority)

        # 构建详细的AI推理提示（基于真实业务逻辑）
        business_logic_prompt = f"""
汽车DVP测试车辆计算（基于理想汽车业务逻辑）:

项目配置:
- 匹配项目数量: {len(matched_projects)}个
- 项目类型: {project_type} ({'首发' if project_type == 'comprehensive' else '次发'})
- 变化程度: {change_degree} ({'多重变化' if change_degree == 'extreme' else '单一变化'})
- 资源优先级: {resource_priority}

业务逻辑规则:
1. 雷达标定: P→0台, M/X/U→1台
2. 感知数采: 零件变化需1~2台，最大2台
3. 感知开发: 首发2台，次发1台
4. 产线标定: 任何零件变化需1台
5. 功能集成测试: 首发6台(临牌)+2台(铁牌)，次发减半
6. 泛化路试: 首发10台，次发3.33台

基础计算结果: {vehicle_calculation['baseVehicles']}台
调整后结果: {vehicle_calculation['totalVehicles']}台

请基于以上逻辑验证计算结果，返回JSON: {{"totalVehicles": 数字, "reasoning": "详细分析过程"}}
"""

        try:
            print(f"🤖 开始AI推理计算...")
            ai_response = self.llm_client.call_llm(business_logic_prompt)
            print(f"🤖 AI推理完成: {ai_response[:100]}...")

            # 尝试解析AI响应中的JSON
            if "{" in ai_response and "}" in ai_response:
                json_start = ai_response.find("{")
                json_end = ai_response.rfind("}") + 1
                json_str = ai_response[json_start:json_end]
                ai_result = json.loads(json_str)

                total_vehicles = ai_result.get("totalVehicles", vehicle_calculation["totalVehicles"])
                if isinstance(total_vehicles, int) and total_vehicles > 0:
                    return {
                        "totalVehicles": total_vehicles,
                        "reasoning": ai_result.get("reasoning", "AI推理完成"),
                        "aiPowered": True
                    }

            # AI响应格式不正确，使用备用计算
            print("⚠️ AI响应格式不正确，使用备用计算")
            return vehicle_calculation

        except Exception as e:
            print(f"⚠️ AI推理失败，使用备用计算: {e}")
            return vehicle_calculation

    def calculate_vehicles_by_business_logic(self, matched_projects, project_type, change_degree, resource_priority):
        """基于PDF B列业务逻辑计算车辆数量"""
        print(f"📊 应用PDF B列业务逻辑计算...")

        # 确定是首发还是次发
        is_first_launch = (project_type == 'comprehensive')

        # 确定变化程度（单一变化 vs 多重变化）
        is_multiple_changes = (change_degree == 'extreme')

        # 基础车辆计算
        base_vehicles = 0
        calculation_details = []

        # 根据真实业务测试项目计算车辆需求
        project_names = [p['name'] for p in matched_projects]

        # 1. 雷达标定匹配项目（超声波雷达、毫米波雷达、激光雷达）
        radar_calibration_projects = [p for p in project_names if '雷达标定匹配' in p]
        if radar_calibration_projects:
            radar_vehicles = len(radar_calibration_projects) * 1  # 每种雷达标定需要1台
            base_vehicles += radar_vehicles
            calculation_details.append(f"雷达标定匹配: {len(radar_calibration_projects)}个项目 × 1台 = {radar_vehicles}台")

        # 2. 定位IMU模块标定匹配
        if "定位IMU模块标定匹配" in project_names:
            imu_vehicles = 1
            base_vehicles += imu_vehicles
            calculation_details.append(f"定位IMU模块标定匹配: {imu_vehicles}台")

        # 3. 感知数采
        if "感知数采" in project_names:
            perception_data_vehicles = 2  # 固定2台
            base_vehicles += perception_data_vehicles
            calculation_details.append(f"感知数采: {perception_data_vehicles}台")

        # 4. 感知开发
        if "感知开发" in project_names:
            perception_dev_vehicles = 2 if is_first_launch else 1
            base_vehicles += perception_dev_vehicles
            calculation_details.append(f"感知开发: {perception_dev_vehicles}台 ({'首发' if is_first_launch else '次发'})")

        # 5. 产线标定
        if "产线标定" in project_names:
            production_vehicles = 1  # 任何零件变化都需要1台
            base_vehicles += production_vehicles
            calculation_details.append(f"产线标定: {production_vehicles}台")

        # 6. 底软开发
        if "底软开发" in project_names:
            software_vehicles = 1  # 与FSD变更关联
            base_vehicles += software_vehicles
            calculation_details.append(f"底软开发: {software_vehicles}台")

        # 7. 数据回传
        if "数据回传" in project_names:
            data_vehicles = 1
            base_vehicles += data_vehicles
            calculation_details.append(f"数据回传: {data_vehicles}台")

        # 8. 地图定位
        if "地图定位" in project_names:
            mapping_vehicles = 1
            base_vehicles += mapping_vehicles
            calculation_details.append(f"地图定位: {mapping_vehicles}台")

        # 9. 行泊主功能开发
        if "行泊主功能开发" in project_names:
            main_function_vehicles = 8  # 主要功能开发需要较多车辆
            base_vehicles += main_function_vehicles
            calculation_details.append(f"行泊主功能开发: {main_function_vehicles}台")

        # 10. 功能集成测试
        if "功能集成测试" in project_names:
            if is_multiple_changes:
                # 多重变化：临牌6台 + 铁牌2台
                integration_vehicles = 8 if is_first_launch else 4
            else:
                # 单一变化：临牌4台 + 铁牌2台
                integration_vehicles = 6 if is_first_launch else 3

            base_vehicles += integration_vehicles
            calculation_details.append(f"功能集成测试: {integration_vehicles}台 ({'多重变化' if is_multiple_changes else '单一变化'}, {'首发' if is_first_launch else '次发'})")

        # 11. 泛化路试
        if "泛化路试" in project_names:
            if is_first_launch:
                road_test_vehicles = 10  # 首发项目需要更多里程测试
            else:
                road_test_vehicles = 4   # 次发项目减少

            base_vehicles += road_test_vehicles
            calculation_details.append(f"泛化路试: {road_test_vehicles}台 ({'首发' if is_first_launch else '次发'})")

        # 优先级调整
        priority_multiplier = 1.0
        if resource_priority == 'urgent':
            priority_multiplier = 1.2  # 紧急项目增加20%
        elif resource_priority == 'low':
            priority_multiplier = 0.9  # 低优先级减少10%

        # 应用优先级调整
        adjusted_vehicles = int(base_vehicles * priority_multiplier)

        return {
            "baseVehicles": base_vehicles,
            "totalVehicles": adjusted_vehicles,
            "reasoning": f"基于PDF B列真实业务逻辑计算：\n" + "\n".join(calculation_details) + f"\n优先级调整: ×{priority_multiplier} = {adjusted_vehicles}台",
            "aiPowered": True,
            "calculationDetails": calculation_details,
            "parameters": {
                "projectType": project_type,
                "changeDegree": change_degree,
                "resourcePriority": resource_priority,
                "isFirstLaunch": is_first_launch,
                "isMultipleChanges": is_multiple_changes
            }
        }

    def fallback_calculation(self, matched_projects):
        """备用车辆数量计算"""
        base_vehicles = sum(p["aiCalculatedVehicles"] for p in matched_projects)
        total_vehicles = int(base_vehicles * 1.25)  # 增加25%缓解风险

        return {
            "totalVehicles": max(total_vehicles, len(matched_projects)),
            "reasoning": "使用备用算法计算",
            "aiPowered": False
        }

    def generate_schedule_with_ai(self, matched_projects, start_date, sop_date, total_vehicles):
        """使用AI生成测试排期"""
        start = datetime.strptime(start_date, "%Y-%m-%d")
        sop = datetime.strptime(sop_date, "%Y-%m-%d")

        print(f"⏰ 排期时间窗口: {start.strftime('%Y-%m-%d')} 至 {sop.strftime('%Y-%m-%d')} ({(sop-start).days}天)")

        # 使用AI进行智能排期计算
        ai_schedule = self.calculate_intelligent_schedule(matched_projects, start, sop, total_vehicles)

        if ai_schedule:
            return ai_schedule

        # 备用算法：基于项目复杂度和依赖关系的智能排期
        return self.fallback_schedule_calculation(matched_projects, start, sop, total_vehicles)

    def calculate_intelligent_schedule(self, matched_projects, start, sop, total_vehicles):
        """使用AI计算智能排期"""
        try:
            # 构建AI排期提示
            project_info = []
            for project in matched_projects:
                project_info.append({
                    "name": project["name"],
                    "duration": project["testCycle"],
                    "complexity": project["complexity"],
                    "vehicles": project["aiCalculatedVehicles"]
                })

            prompt = f"""
汽车DVP测试排期规划：
DVP开始日期: {start.strftime('%Y-%m-%d')}
SOP量产日期: {sop.strftime('%Y-%m-%d')}
可用时间窗口: {(sop-start).days}天
可用车辆总数: {total_vehicles}台

测试项目清单:
{json.dumps(project_info, ensure_ascii=False, indent=2)}

排期规则:
1. 所有测试必须在{start.strftime('%Y-%m-%d')}到{sop.strftime('%Y-%m-%d')}之间完成
2. 高复杂度项目优先开始，需要更多准备时间
3. 考虑车辆资源限制，合理安排并行测试
4. 预留10-15天缓冲时间

请返回JSON格式的排期计划，包含每个项目的startDate和endDate（ISO格式）。
"""

            print(f"🤖 使用AI生成智能排期...")
            ai_response = self.llm_client.call_llm(prompt)

            # 尝试解析AI响应
            if "{" in ai_response and "}" in ai_response:
                json_start = ai_response.find("{")
                json_end = ai_response.rfind("}") + 1
                json_str = ai_response[json_start:json_end]
                ai_result = json.loads(json_str)

                # 验证AI结果格式
                if "projects" in ai_result:
                    print(f"✅ AI排期计算成功")
                    return ai_result

        except Exception as e:
            print(f"⚠️ AI排期计算失败: {e}")

        return None

    def fallback_schedule_calculation(self, matched_projects, start, sop, total_vehicles):
        """备用排期计算算法"""
        # 按复杂度和车辆需求排序，添加安全检查
        def get_sort_key(project):
            # 安全获取复杂度
            complexity = project.get("complexity", "中")
            complexity_score = {"高": 3, "中": 2, "低": 1}.get(complexity, 2)

            # 安全获取车辆数量
            vehicles = project.get("aiCalculatedVehicles", project.get("recommendedVehicles", 1))

            return (complexity_score, vehicles)

        sorted_projects = sorted(matched_projects, key=get_sort_key, reverse=True)

        # 计算可用时间窗口
        available_days = (sop - start).days
        max_cycle = max([p["testCycle"] for p in sorted_projects]) if sorted_projects else 0

        # 预留缓冲时间
        buffer_days = 15 if len(sorted_projects) > 5 else 10
        effective_days = available_days - buffer_days
        total_duration = min(effective_days, int(max_cycle * 0.85))  # 并行优化15%

        # 智能排期：考虑车辆资源和依赖关系
        projects = []
        vehicle_schedule = {}  # 车辆使用时间表

        for i, project in enumerate(sorted_projects):
            # 为高复杂度项目预留更多时间，从开始日期计算
            if project["complexity"] == "高":
                project_start = start + timedelta(days=i * 2)
            else:
                project_start = start + timedelta(days=i * 3)

            project_end = project_start + timedelta(days=project["testCycle"])

            # 确保不超过SOP日期
            if project_end > sop - timedelta(days=buffer_days):
                project_end = sop - timedelta(days=buffer_days)
                project_start = project_end - timedelta(days=project["testCycle"])

            # 确保开始日期不早于DVP开始日期
            if project_start < start:
                project_start = start
                project_end = project_start + timedelta(days=project["testCycle"])

            projects.append({
                "name": project["name"],
                "startDate": project_start.isoformat(),
                "endDate": project_end.isoformat(),
                "duration": project["testCycle"],
                "complexity": project["complexity"],
                "vehicles": project["aiCalculatedVehicles"]
            })

        return {
            "startDate": start.isoformat(),
            "endDate": sop.isoformat(),
            "totalDuration": total_duration,
            "projects": projects,
            "aiOptimized": False,
            "scheduling": f"智能算法排期({available_days}天窗口)"
        }


    def save_report(self, report):
        """保存DVP报告"""
        try:
            filename = f"reports/dvp_report_ai_{report['id']}.json"
            with open(filename, "w", encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"📁 报告已保存: {filename}")
        except Exception as e:
            print(f"保存报告失败: {e}")

# 创建API服务器实例
dvp_server = DVPAPIServer()

@app.route('/api/generate-dvp', methods=['POST', 'OPTIONS'])
def generate_dvp():
    """生成DVP方案API"""
    if request.method == 'OPTIONS':
        # 处理CORS预检请求
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', '*')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type')
        response.headers.add('Access-Control-Allow-Methods', 'POST')
        return response

    try:
        config = request.json
        print(f"🎯 收到DVP生成请求: {config.get('configType', 'Unknown')}")

        # 使用真实AI服务生成DVP
        result = dvp_server.generate_dvp_with_ai(config)

        return jsonify({
            "success": True,
            "data": result
        })

    except Exception as e:
        print(f"❌ DVP生成失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/get-components', methods=['GET'])
def get_components():
    """从Neo4j数据库动态查询所有可用的车辆配件"""
    try:
        print("🔍 从Neo4j数据库查询配件信息...")

        # 尝试从Neo4j查询配件
        if dvp_server.neo4j_client.connect():
            try:
                with dvp_server.neo4j_client.driver.session() as session:
                    result = session.run("MATCH (c:Component) RETURN c.name as name ORDER BY c.name")
                    components = [record["name"] for record in result]

                    if components:
                        print(f"✅ 从Neo4j查询到 {len(components)} 个配件")
                        return jsonify({
                            "success": True,
                            "components": components,
                            "source": "Neo4j数据库",
                            "count": len(components)
                        })
            except Exception as neo4j_error:
                print(f"⚠️ Neo4j查询失败: {neo4j_error}")

        # 备用方案：从矩阵数据获取
        print("📋 使用备用数据源...")
        components = list(set([
            rel['component'] for rel in dvp_server.matrix_data['relationships']
        ]))
        components.sort()

        print(f"✅ 从备用数据源查询到 {len(components)} 个配件")

        return jsonify({
            "success": True,
            "components": components,
            "source": "备用数据源(矩阵文件)",
            "count": len(components)
        })

    except Exception as e:
        print(f"❌ 查询配件失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/get-test-projects', methods=['GET'])
def get_test_projects():
    """从Neo4j数据库动态查询所有可用的测试项目"""
    try:
        print("🔍 从Neo4j数据库查询测试项目信息...")

        # 尝试从Neo4j查询测试项目
        if dvp_server.neo4j_client.connect():
            try:
                with dvp_server.neo4j_client.driver.session() as session:
                    # 查询所有测试项目及其测试周期
                    result = session.run("""
                        MATCH (t:TestProject)
                        RETURN t.name as name, t.testCycle as testCycle
                        ORDER BY t.name
                    """)

                    test_projects = []
                    for record in result:
                        project_name = record["name"]
                        test_cycle = record["testCycle"]

                        # 查询该项目需要的配件
                        component_result = session.run("""
                            MATCH (t:TestProject {name: $project_name})-[r]->(c:Component)
                            RETURN c.name as component, type(r) as relation_type
                        """, project_name=project_name)

                        required_components = []
                        optional_components = []

                        for comp_record in component_result:
                            component = comp_record["component"]
                            relation_type = comp_record["relation_type"]

                            if relation_type == "REQUIRES":
                                required_components.append(component)
                            else:
                                optional_components.append(component)

                        project_info = {
                            "name": project_name,
                            "testCycle": test_cycle,
                            "requiredComponents": required_components,
                            "optionalComponents": optional_components,
                            "totalComponents": len(required_components) + len(optional_components)
                        }

                        test_projects.append(project_info)

                    if test_projects:
                        print(f"✅ 从Neo4j查询到 {len(test_projects)} 个测试项目")
                        return jsonify({
                            "success": True,
                            "projects": test_projects,
                            "source": "Neo4j数据库",
                            "count": len(test_projects)
                        })
            except Exception as neo4j_error:
                print(f"⚠️ Neo4j查询失败: {neo4j_error}")

        # 备用方案：从矩阵数据获取
        print("📋 使用备用数据源...")
        test_projects = []
        for project_name in dvp_server.matrix_data.get('test_projects', []):
            project_info = {
                "name": project_name,
                "testCycle": dvp_server.matrix_data.get('test_cycles', {}).get(project_name, 10),
                "requiredComponents": [],
                "optionalComponents": []
            }

            # 获取项目所需的配件
            for rel in dvp_server.matrix_data['relationships']:
                if rel['test_project'] == project_name and rel['relationship'] == '✅':
                    if rel.get('required', False):
                        project_info['requiredComponents'].append(rel['component'])
                    else:
                        project_info['optionalComponents'].append(rel['component'])

            project_info['totalComponents'] = len(project_info['requiredComponents']) + len(project_info['optionalComponents'])
            test_projects.append(project_info)

        print(f"✅ 从备用数据源查询到 {len(test_projects)} 个测试项目")

        return jsonify({
            "success": True,
            "projects": test_projects,
            "source": "备用数据源(矩阵文件)",
            "count": len(test_projects)
        })

    except Exception as e:
        print(f"❌ 查询测试项目失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/get-knowledge-graph', methods=['GET'])
def get_knowledge_graph():
    """从Neo4j数据库获取知识图谱数据"""
    try:
        print("🔍 从Neo4j数据库查询知识图谱数据...")

        # 尝试从Neo4j查询图谱数据
        if dvp_server.neo4j_client.connect():
            try:
                with dvp_server.neo4j_client.driver.session() as session:
                    # 查询所有节点
                    nodes_result = session.run("""
                        MATCH (n)
                        RETURN elementId(n) as id, labels(n) as labels, properties(n) as properties
                    """)

                    nodes = []
                    for record in nodes_result:
                        node_id = record["id"]
                        labels = record["labels"]
                        properties = record["properties"]

                        # 确定节点类型和名称
                        node_type = labels[0] if labels else "Unknown"
                        node_name = properties.get('name', f"{node_type}_{node_id}")

                        nodes.append({
                            "id": str(node_id),
                            "name": node_name,
                            "category": node_type,
                            "properties": properties,
                            "symbolSize": 30 if node_type == "Component" else 40
                        })

                    # 查询所有关系
                    relationships_result = session.run("""
                        MATCH (a)-[r]->(b)
                        RETURN elementId(a) as source_id, elementId(b) as target_id, type(r) as relation_type, properties(r) as properties
                    """)

                    links = []
                    for record in relationships_result:
                        links.append({
                            "source": str(record["source_id"]),
                            "target": str(record["target_id"]),
                            "relation": record["relation_type"],
                            "properties": record["properties"]
                        })

                    # 统计信息
                    stats = {
                        "totalNodes": len(nodes),
                        "totalLinks": len(links),
                        "nodeTypes": {},
                        "relationTypes": {}
                    }

                    # 统计节点类型
                    for node in nodes:
                        node_type = node["category"]
                        stats["nodeTypes"][node_type] = stats["nodeTypes"].get(node_type, 0) + 1

                    # 统计关系类型
                    for link in links:
                        relation_type = link["relation"]
                        stats["relationTypes"][relation_type] = stats["relationTypes"].get(relation_type, 0) + 1

                    print(f"✅ 从Neo4j查询到 {len(nodes)} 个节点, {len(links)} 个关系")

                    return jsonify({
                        "success": True,
                        "data": {
                            "nodes": nodes,
                            "links": links,
                            "stats": stats
                        },
                        "source": "Neo4j数据库"
                    })

            except Exception as neo4j_error:
                print(f"⚠️ Neo4j查询失败: {neo4j_error}")
                return jsonify({
                    "success": False,
                    "error": f"Neo4j查询失败: {str(neo4j_error)}"
                }), 500
        else:
            return jsonify({
                "success": False,
                "error": "无法连接到Neo4j数据库"
            }), 500

    except Exception as e:
        print(f"❌ 查询知识图谱失败: {e}")
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/api/test-connection', methods=['GET'])
def test_connection():
    """测试API连接"""
    try:
        # 测试SiliconCloud连接
        llm_test = dvp_server.llm_client.call_llm("请用中文回复'API连接正常'")

        # 测试Neo4j连接
        neo4j_status = dvp_server.neo4j_client.connect()

        return jsonify({
            "success": True,
            "siliconcloud": "连接正常" in llm_test,
            "neo4j": neo4j_status,
            "matrixData": len(dvp_server.matrix_data.get("relationships", [])) > 0
        })

    except Exception as e:
        return jsonify({
            "success": False,
            "error": str(e)
        }), 500

@app.route('/')
def serve_index():
    """服务主页"""
    return send_from_directory('.', 'enhanced_dvp_interface.html')

@app.route('/<path:filename>')
def serve_static(filename):
    """服务静态文件"""
    return send_from_directory('.', filename)

if __name__ == '__main__':
    print("🚀 启动DVP API服务器...")
    print("📊 已集成真实AI推理和业务逻辑矩阵")
    print("🌐 访问地址: http://localhost:5002")
    app.run(host='0.0.0.0', port=5002, debug=False)