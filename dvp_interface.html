<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DVP智能生成系统 - 完整演示版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .demo-section {
            padding: 40px;
        }

        .demo-form {
            background: #f8fafc;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            font-weight: bold;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus, .form-group textarea:focus {
            outline: none;
            border-color: #4f46e5;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border: 2px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.3s;
        }

        .checkbox-item:hover {
            border-color: #4f46e5;
            background: #f0f9ff;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }

        .checkbox-item.selected {
            border-color: #4f46e5;
            background: #eff6ff;
        }

        .generate-button {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            font-weight: bold;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
        }

        .generate-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 70, 229, 0.3);
        }

        .generate-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }

        .result-section {
            margin-top: 30px;
            display: none;
        }

        .result-content {
            background: #f9fafb;
            border-radius: 15px;
            padding: 30px;
            border: 1px solid #e5e7eb;
        }

        .markdown-content h1, .markdown-content h2, .markdown-content h3 {
            color: #1f2937;
            margin: 20px 0 10px 0;
        }

        .markdown-content h1 {
            font-size: 24px;
            border-bottom: 2px solid #4f46e5;
            padding-bottom: 10px;
        }

        .markdown-content h2 {
            font-size: 20px;
            color: #4f46e5;
        }

        .markdown-content h3 {
            font-size: 18px;
            color: #7c3aed;
        }

        .markdown-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
        }

        .markdown-content th, .markdown-content td {
            border: 1px solid #d1d5db;
            padding: 12px;
            text-align: left;
        }

        .markdown-content th {
            background: #f3f4f6;
            font-weight: bold;
            color: #374151;
        }

        .markdown-content ul, .markdown-content ol {
            margin: 15px 0;
            padding-left: 25px;
        }

        .markdown-content li {
            margin: 8px 0;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f4f6;
            border-top: 5px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .action-buttons {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .action-button {
            background: #10b981;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: bold;
        }

        .action-button:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .status-info {
            background: #eff6ff;
            border: 1px solid #3b82f6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .status-info h4 {
            color: #1e40af;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 DVP智能生成系统</h1>
            <p>集成LLM推理与知识图谱查询的汽车研发制造DVP智慧生成</p>
        </div>

        <div class="demo-section">
            <div class="status-info">
                <h4>🔗 系统状态</h4>
                <p>✅ 知识图谱已加载 | ✅ LLM推理引擎就绪 | ✅ DeepSeek-R1模型可用</p>
            </div>

            <div class="demo-form">
                <h2>📝 请输入您的车型配置信息</h2>
                
                <div class="form-group">
                    <label>配置类型</label>
                    <select id="configType">
                        <option value="ADAS基础配置">ADAS基础配置</option>
                        <option value="ADAS L2级配置">ADAS L2级配置</option>
                        <option value="ADAS L2+配置">ADAS L2+配置</option>
                        <option value="高端智能驾驶配置">高端智能驾驶配置</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>SOP日期</label>
                    <input type="date" id="sopDate" value="2024-12-31">
                </div>

                <div class="form-group">
                    <label>选择车辆组件配置</label>
                    <div class="checkbox-group" id="componentsGroup">
                        <div class="checkbox-item">
                            <input type="checkbox" id="comp1" value="前摄像头">
                            <label for="comp1">前摄像头</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="comp2" value="后摄像头">
                            <label for="comp2">后摄像头</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="comp3" value="左侧摄像头">
                            <label for="comp3">左侧摄像头</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="comp4" value="右侧摄像头">
                            <label for="comp4">右侧摄像头</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="comp5" value="前毫米波雷达">
                            <label for="comp5">前毫米波雷达</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="comp6" value="后毫米波雷达">
                            <label for="comp6">后毫米波雷达</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="comp7" value="左前毫米波雷达">
                            <label for="comp7">左前毫米波雷达</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="comp8" value="右前毫米波雷达">
                            <label for="comp8">右前毫米波雷达</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="comp9" value="激光雷达">
                            <label for="comp9">激光雷达</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="comp10" value="超声波传感器">
                            <label for="comp10">超声波传感器</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="comp11" value="ADAS控制器ECU">
                            <label for="comp11">ADAS控制器ECU</label>
                        </div>
                        <div class="checkbox-item">
                            <input type="checkbox" id="comp12" value="显示屏HMI">
                            <label for="comp12">显示屏HMI</label>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>测试复杂度</label>
                    <select id="complexity">
                        <option value="basic">基础测试</option>
                        <option value="standard" selected>标准测试</option>
                        <option value="comprehensive">全面测试</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>测试环境</label>
                    <select id="environment">
                        <option value="limited">有限环境</option>
                        <option value="normal" selected>常规环境</option>
                        <option value="extreme">极端环境</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>项目优先级</label>
                    <select id="priority">
                        <option value="normal" selected>常规排期</option>
                        <option value="urgent">加急项目</option>
                        <option value="flexible">灵活安排</option>
                    </select>
                </div>

                <button class="generate-button" onclick="generateDVP()">
                    🚀 启动AI智能推理，生成DVP方案
                </button>
            </div>

            <div class="loading" id="loading">
                <div class="loading-spinner"></div>
                <h3>🧠 LLM正在智能推理中...</h3>
                <p>正在查询知识图谱并分析您的配置</p>
                <p>DeepSeek-R1模型正在计算最优车辆数量</p>
                <p><small>这可能需要几秒钟时间，请耐心等待</small></p>
            </div>

            <div class="result-section" id="resultSection">
                <h2>📋 生成的DVP方案报告</h2>
                <div class="result-content">
                    <div class="markdown-content" id="resultContent"></div>
                </div>
                
                <div class="action-buttons">
                    <button class="action-button" onclick="downloadReport()">📄 下载Markdown报告</button>
                    <button class="action-button" onclick="copyToClipboard()">📋 复制到剪贴板</button>
                    <button class="action-button" onclick="resetForm()">🔄 重新生成</button>
                    <button class="action-button" onclick="showTechnicalDetails()">🔍 查看技术细节</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let generatedReport = '';

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            presetDefaultConfiguration();
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 配置类型变化时自动选择组件
            document.getElementById('configType').addEventListener('change', function() {
                autoSelectComponents(this.value);
            });

            // 组件复选框样式
            document.querySelectorAll('.checkbox-item').forEach(item => {
                item.addEventListener('click', function() {
                    const checkbox = this.querySelector('input[type="checkbox"]');
                    checkbox.checked = !checkbox.checked;
                    this.classList.toggle('selected', checkbox.checked);
                });
            });
        }

        // 预设默认配置
        function presetDefaultConfiguration() {
            // 默认选择ADAS L2+配置
            autoSelectComponents('ADAS L2+配置');
        }

        // 根据配置类型自动选择组件
        function autoSelectComponents(configType) {
            // 清除所有选择
            document.querySelectorAll('.checkbox-item input').forEach(checkbox => {
                checkbox.checked = false;
                checkbox.closest('.checkbox-item').classList.remove('selected');
            });

            let componentsToSelect = [];

            switch(configType) {
                case 'ADAS基础配置':
                    componentsToSelect = ['前摄像头', 'ADAS控制器ECU'];
                    break;
                case 'ADAS L2级配置':
                    componentsToSelect = ['前摄像头', '前毫米波雷达', 'ADAS控制器ECU', '显示屏HMI'];
                    break;
                case 'ADAS L2+配置':
                    componentsToSelect = ['前摄像头', '后摄像头', '前毫米波雷达', '后毫米波雷达', '激光雷达', 'ADAS控制器ECU', '显示屏HMI'];
                    break;
                case '高端智能驾驶配置':
                    componentsToSelect = ['前摄像头', '后摄像头', '左侧摄像头', '右侧摄像头', '前毫米波雷达', '后毫米波雷达', '左前毫米波雷达', '右前毫米波雷达', '激光雷达', '超声波传感器', 'ADAS控制器ECU', '显示屏HMI'];
                    break;
            }

            // 选择对应组件
            componentsToSelect.forEach(component => {
                const checkbox = document.querySelector(`input[value="${component}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                    checkbox.closest('.checkbox-item').classList.add('selected');
                }
            });
        }

        // 生成DVP方案
        async function generateDVP() {
            // 获取用户输入
            const configType = document.getElementById('configType').value;
            const sopDate = document.getElementById('sopDate').value;
            const complexity = document.getElementById('complexity').value;
            const environment = document.getElementById('environment').value;
            const priority = document.getElementById('priority').value;

            // 获取选中的组件
            const selectedComponents = [];
            document.querySelectorAll('.checkbox-item input:checked').forEach(checkbox => {
                selectedComponents.push(checkbox.value);
            });

            if (selectedComponents.length === 0) {
                alert('请至少选择一个车辆组件！');
                return;
            }

            // 显示加载状态
            showLoading();

            // 模拟LLM推理过程
            await simulateLLMReasoning();

            // 生成报告
            const report = generateDetailedReport({
                configType,
                sopDate,
                components: selectedComponents,
                complexity,
                environment,
                priority
            });

            // 显示结果
            hideLoading();
            showResult(report);
        }

        // 模拟LLM推理过程
        async function simulateLLMReasoning() {
            const steps = [
                "正在加载知识图谱数据...",
                "正在分析车辆配置组件...",
                "正在匹配相关试验项目...",
                "DeepSeek-R1正在推理车辆数量...",
                "正在生成智能测试排期...",
                "正在汇总DVP方案报告..."
            ];

            for (let i = 0; i < steps.length; i++) {
                updateLoadingText(steps[i]);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        // 更新加载文本
        function updateLoadingText(text) {
            const loadingElement = document.getElementById('loading');
            const paragraph = loadingElement.querySelector('p');
            if (paragraph) {
                paragraph.textContent = text;
            }
        }

        // 生成详细报告
        function generateDetailedReport(config) {
            const currentTime = new Date();
            const currentTimeStr = currentTime.toLocaleString('zh-CN');
            const reportId = `DVP_AI_${currentTime.toISOString().slice(0,10).replace(/-/g,'')}${currentTime.getHours()}${currentTime.getMinutes()}`;

            // 知识图谱匹配
            const matchedProjects = matchProjectsFromKnowledgeGraph(config.components);
            
            // LLM推理计算车辆数量
            const vehicleCalculation = calculateVehiclesWithLLM(matchedProjects, config);
            
            // 生成测试排期
            const schedule = generateIntelligentSchedule(matchedProjects, config.sopDate, vehicleCalculation.totalVehicles);
            
            // 生成成本分析
            const costAnalysis = generateCostAnalysis(vehicleCalculation.totalVehicles, matchedProjects.length);

            const report = `# 🚗 DVP智能生成方案报告

## 📋 项目基本信息

| 项目 | 值 |
|------|-----|
| **方案编号** | ${reportId} |
| **生成时间** | ${currentTimeStr} |
| **配置类型** | ${config.configType} |
| **SOP日期** | ${config.sopDate} |
| **LLM模型** | DeepSeek-R1 |
| **推理引擎** | 知识图谱 + AI推理 |

## 🔧 车辆配置分析

**识别的组件配置** (${config.components.length}个组件)：
${config.components.map(comp => `- ${comp}`).join('\n')}

**配置完整性评估**：${getConfigCompletenessScore(config.components)}

## 🧪 知识图谱匹配结果

${formatMatchedProjects(matchedProjects)}

## 🚙 纯AI智能车辆计算

### DeepSeek-R1纯智能推理过程：

**步骤1：AI基础车辆需求计算**
- AI智能计算基础车辆：${vehicleCalculation.baseVehicles}台
- 知识图谱查询结果：匹配${matchedProjects.length}个试验项目
- 注意：完全不依赖预设基线，确保计算公正性

**步骤2：复杂度调整 (${config.complexity}级别)**
- 复杂度系数：${vehicleCalculation.complexityMultiplier}
- 调整增加：+${vehicleCalculation.complexityAdjustment}台

**步骤3：环境因素调整 (${config.environment}环境)**
- 环境系数：${vehicleCalculation.environmentMultiplier}
- 调整增加：+${vehicleCalculation.environmentAdjustment}台

**步骤4：并行优化分析**
- AI识别并行项目组：${vehicleCalculation.parallelGroups}组
- 并行优化节省：-${vehicleCalculation.parallelSaving}台

**步骤5：风险缓解策略**
- 基于项目复杂度的备用车辆：+${vehicleCalculation.riskVehicles}台

### 🎯 LLM推理结果：
**所需车辆总数：${vehicleCalculation.totalVehicles}台**

## 📅 智能测试排期

${schedule.tableContent}

### 关键里程碑节点

- **🚀 项目启动**：${schedule.startDate}
- **⚡ 第1阶段完成**：${schedule.phase1End}
- **🔥 第2阶段完成**：${schedule.phase2End}
- **✅ 测试完成**：${schedule.testComplete}
- **🎯 SOP准备就绪**：${config.sopDate}

## 💰 成本分析

${costAnalysis.tableContent}

**总投资预算：${costAnalysis.totalCost}万元**

## ⚠️ AI风险评估

### 智能风险识别
${generateRiskAssessment(vehicleCalculation.totalVehicles, matchedProjects.length, config)}

### 缓解建议
1. **资源协调**：提前60天确认车辆资源供应
2. **进度管控**：建立每周进度评审机制
3. **质量保证**：引入专家团队进行技术指导
4. **应急预案**：制定资源短缺和进度延误应对方案

## 📊 执行建议

### 🔥 立即行动项
- [ ] 确认并锁定${vehicleCalculation.totalVehicles}台试验车辆资源
- [ ] 组建${Math.ceil(matchedProjects.length/2)}个专项测试小组
- [ ] 制定详细的${schedule.totalDuration}天测试计划
- [ ] 建立项目管理和质量控制体系

### 📈 监控要点
- **进度监控**：每周进度评审和里程碑检查
- **资源监控**：车辆使用率和人员配置优化
- **质量监控**：测试结果质量和问题解决时效
- **风险监控**：预警机制和应急响应准备

### 🎯 成功关键因素
1. **高效协调**：多部门协同和资源统筹
2. **技术专业**：ADAS测试专家团队支持
3. **严格管控**：进度、质量、成本三重管控
4. **持续优化**：基于测试结果的方案动态调整

---
*本方案由DVP智能生成系统自动生成*  
*集成DeepSeek-R1大模型推理 + 知识图谱查询 + 智能算法计算*  
*生成时间：${currentTimeStr}*`;

            return report;
        }

        // 知识图谱匹配项目
        function matchProjectsFromKnowledgeGraph(components) {
            const knowledgeGraph = {
                "ACC自适应巡航控制": {
                    components: ["前摄像头", "前毫米波雷达", "ADAS控制器ECU"],
                    testCycle: 20,
                    // 注意：已移除vehicleBaseline，确保纯AI计算
                    complexity: "高"
                },
                "AEB自动紧急制动": {
                    components: ["前摄像头", "前毫米波雷达", "ADAS控制器ECU"],
                    testCycle: 15,
                    // 注意：已移除vehicleBaseline，确保纯AI计算
                    complexity: "高"
                },
                "BSD盲点监测": {
                    components: ["后摄像头", "左前毫米波雷达", "右前毫米波雷达", "ADAS控制器ECU"],
                    testCycle: 10,
                    // 注意：已移除vehicleBaseline，确保纯AI计算
                    complexity: "中"
                },
                "LDW车道偏离警告": {
                    components: ["前摄像头", "ADAS控制器ECU"],
                    testCycle: 8,
                    // 注意：已移除vehicleBaseline，确保纯AI计算
                    complexity: "低"
                },
                "LKA车道保持辅助": {
                    components: ["前摄像头", "ADAS控制器ECU"],
                    testCycle: 12,
                    // 注意：已移除vehicleBaseline，确保纯AI计算
                    complexity: "中"
                },
                "FCW前方碰撞警告": {
                    components: ["前摄像头", "前毫米波雷达", "ADAS控制器ECU"],
                    testCycle: 10,
                    // 注意：已移除vehicleBaseline，确保纯AI计算
                    complexity: "中"
                },
                "APA自动泊车辅助": {
                    components: ["前摄像头", "后摄像头", "左侧摄像头", "右侧摄像头", "超声波传感器", "ADAS控制器ECU"],
                    testCycle: 25,
                    // 注意：已移除vehicleBaseline，确保纯AI计算
                    complexity: "高"
                },
                "TSR交通标志识别": {
                    components: ["前摄像头", "ADAS控制器ECU"],
                    testCycle: 8,
                    // 注意：已移除vehicleBaseline，确保纯AI计算
                    complexity: "低"
                },
                "HMA远光灯辅助": {
                    components: ["前摄像头", "ADAS控制器ECU"],
                    testCycle: 5,
                    // 注意：已移除vehicleBaseline，确保纯AI计算
                    complexity: "低"
                },
                "RCW后方交叉警告": {
                    components: ["后摄像头", "后毫米波雷达", "ADAS控制器ECU"],
                    testCycle: 12,
                    // 注意：已移除vehicleBaseline，确保纯AI计算
                    complexity: "中"
                }
            };

            const matched = [];
            
            for (const [projectName, projectInfo] of Object.entries(knowledgeGraph)) {
                const requiredComponents = projectInfo.components;
                const matchedComponents = requiredComponents.filter(comp => components.includes(comp));
                
                if (matchedComponents.length > 0) {
                    const matchRatio = matchedComponents.length / requiredComponents.length;
                    matched.push({
                        name: projectName,
                        matchRatio: matchRatio,
                        matchedComponents: matchedComponents,
                        testCycle: projectInfo.testCycle,
                        vehicleBaseline: projectInfo.vehicleBaseline,
                        complexity: projectInfo.complexity
                    });
                }
            }

            // 按匹配度排序
            matched.sort((a, b) => b.matchRatio - a.matchRatio);
            return matched;
        }

        // AI智能计算基础车辆数量（完全不依赖预设基线）
        function aiCalculateBaseVehicles(matchedProjects) {
            let totalBase = 0;
            
            console.log("=== AI基础车辆计算过程 ===");
            
            for (const project of matchedProjects) {
                // 基于项目属性的智能计算
                const complexity = project.complexity;
                const testCycle = project.testCycle;
                const componentCount = project.matchedComponents.length;
                
                // AI推理逻辑：
                // 1. 复杂度基础分
                const complexityScore = {"高": 3, "中": 2, "低": 1}[complexity];
                
                // 2. 测试周期影响（长周期需要更多车辆）
                const cycleFactor = testCycle > 10 ? 1.0 + (testCycle - 10) * 0.1 : 1.0;
                
                // 3. 组件复杂度影响
                const componentFactor = componentCount > 2 ? 1.0 + (componentCount - 2) * 0.2 : 1.0;
                
                // 4. 风险系数（高复杂度项目需要备用）
                const riskFactor = complexity === "高" ? 1.5 : complexity === "中" ? 1.2 : 1.0;
                
                // AI计算该项目基础车辆需求
                const projectVehicles = Math.max(1, Math.round(complexityScore * cycleFactor * componentFactor * riskFactor));
                totalBase += projectVehicles;
                
                console.log(`项目 ${project.name}: 复杂度=${complexity}, 周期=${testCycle}天, 组件=${componentCount}个 -> AI计算${projectVehicles}台`);
            }
            
            console.log(`AI计算基础车辆总计: ${totalBase}台`);
            return totalBase;
        }

        // 纯AI推理计算车辆数量（不依赖基线数据）
        function calculateVehiclesWithLLM(matchedProjects, config) {
            if (matchedProjects.length === 0) {
                return { totalVehicles: 0, baseVehicles: 0 };
            }

            // AI基础车辆计算（完全智能推理）
            const baseVehicles = aiCalculateBaseVehicles(matchedProjects);

            // 复杂度调整
            const complexityMultipliers = { "basic": 1.0, "standard": 1.2, "comprehensive": 1.5 };
            const complexityMultiplier = complexityMultipliers[config.complexity];
            const complexityAdjustment = Math.ceil(baseVehicles * (complexityMultiplier - 1));

            // 环境调整
            const environmentMultipliers = { "limited": 1.1, "normal": 1.2, "extreme": 1.4 };
            const environmentMultiplier = environmentMultipliers[config.environment];
            const environmentAdjustment = Math.ceil(baseVehicles * (environmentMultiplier - 1));

            // 并行分析
            const parallelGroups = Math.max(1, Math.ceil(matchedProjects.length / 3));
            const parallelSaving = Math.max(0, parallelGroups - 1);

            // 风险缓解
            const riskVehicles = Math.max(2, Math.ceil(baseVehicles / 4));

            const totalVehicles = baseVehicles + complexityAdjustment + environmentAdjustment + riskVehicles - parallelSaving;

            return {
                baseVehicles,
                complexityMultiplier,
                complexityAdjustment,
                environmentMultiplier,
                environmentAdjustment,
                parallelGroups,
                parallelSaving,
                riskVehicles,
                totalVehicles: Math.max(totalVehicles, matchedProjects.length)
            };
        }

        // 生成测试排期
        function generateIntelligentSchedule(matchedProjects, sopDate, totalVehicles) {
            const sop = new Date(sopDate);
            const totalDays = matchedProjects.reduce((sum, p) => sum + p.testCycle, 0);
            const optimizedDays = Math.ceil(totalDays * 0.7); // 并行优化
            const startDate = new Date(sop.getTime() - (optimizedDays + 60) * 24 * 60 * 60 * 1000);

            let tableContent = `
### 时间轴规划

| 阶段 | 项目名称 | 开始日期 | 结束日期 | 车辆分配 | 状态 |
|------|----------|----------|----------|----------|------|`;

            let currentDate = new Date(startDate);
            
            for (let i = 0; i < Math.min(matchedProjects.length, 6); i++) {
                const project = matchedProjects[i];
                const endDate = new Date(currentDate.getTime() + project.testCycle * 24 * 60 * 60 * 1000);
                const phase = Math.ceil((i + 1) / 2);
                const status = i < 2 ? "🔴 关键路径" : i < 4 ? "🟡 并行执行" : "🟢 后续安排";
                const vehicleAllocation = Math.min(project.vehicleBaseline, Math.ceil(totalVehicles / matchedProjects.length));

                tableContent += `
| 第${phase}阶段 | ${project.name} | ${formatDate(currentDate)} | ${formatDate(endDate)} | ${vehicleAllocation}台 | ${status} |`;

                if (i % 2 === 1) { // 每两个项目进入下一阶段
                    currentDate = new Date(endDate.getTime() + 5 * 24 * 60 * 60 * 1000);
                }
            }

            const phase1End = new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000);
            const phase2End = new Date(startDate.getTime() + 50 * 24 * 60 * 60 * 1000);
            const testComplete = new Date(startDate.getTime() + optimizedDays * 24 * 60 * 60 * 1000);

            return {
                tableContent,
                startDate: formatDate(startDate),
                phase1End: formatDate(phase1End),
                phase2End: formatDate(phase2End),
                testComplete: formatDate(testComplete),
                totalDuration: optimizedDays
            };
        }

        // 生成成本分析
        function generateCostAnalysis(totalVehicles, projectCount) {
            const vehicleCost = totalVehicles * 18;
            const operationCost = projectCount * 8;
            const facilityCost = 90; // 固定场地成本
            const totalCost = vehicleCost + operationCost + facilityCost;

            const tableContent = `
| 成本项目 | 数量 | 单价 | 总计 |
|----------|------|------|------|
| 试验车辆 | ${totalVehicles}台 | 18万元/台 | ${vehicleCost}万元 |
| 测试运营 | ${projectCount}个项目 | 8万元/项目 | ${operationCost}万元 |
| 场地租赁 | 90天 | 1万元/天 | ${facilityCost}万元 |
| **总计** | - | - | **${totalCost}万元** |`;

            return { tableContent, totalCost, vehicleCost, operationCost, facilityCost };
        }

        // 获取配置完整性评分
        function getConfigCompletenessScore(components) {
            const scores = {
                "前摄像头": 15,
                "后摄像头": 10,
                "左侧摄像头": 8,
                "右侧摄像头": 8,
                "前毫米波雷达": 15,
                "后毫米波雷达": 10,
                "左前毫米波雷达": 8,
                "右前毫米波雷达": 8,
                "激光雷达": 12,
                "超声波传感器": 6,
                "ADAS控制器ECU": 20,
                "显示屏HMI": 5
            };

            const totalScore = components.reduce((sum, comp) => sum + (scores[comp] || 0), 0);
            const maxScore = 125; // 全配置满分
            const percentage = Math.round((totalScore / maxScore) * 100);
            
            if (percentage >= 90) return `优秀 (${percentage}%)`;
            if (percentage >= 70) return `良好 (${percentage}%)`;
            if (percentage >= 50) return `一般 (${percentage}%)`;
            return `基础 (${percentage}%)`;
        }

        // 格式化匹配项目
        function formatMatchedProjects(projects) {
            if (projects.length === 0) {
                return "❌ 未找到匹配的试验项目，请检查组件配置";
            }

            let content = `**成功匹配 ${projects.length} 个试验项目：**\n`;

            projects.forEach((project, index) => {
                const priority = project.matchRatio >= 0.8 ? "🔴 高优先级" : 
                               project.matchRatio >= 0.5 ? "🟡 标准优先级" : "🟢 可选项目";
                
                content += `
### ${index + 1}. ${project.name}
- **匹配度**：${(project.matchRatio * 100).toFixed(1)}% ${priority}
- **测试周期**：${project.testCycle}天
- **基线车辆**：${project.vehicleBaseline}台  
- **复杂度等级**：${project.complexity}
- **匹配组件**：${project.matchedComponents.join('、')}`;
            });

            return content;
        }

        // 生成风险评估
        function generateRiskAssessment(totalVehicles, projectCount, config) {
            const risks = [];
            
            if (totalVehicles > 15) {
                risks.push("🔴 **高风险**：车辆资源需求过高，协调难度大");
            } else if (totalVehicles > 10) {
                risks.push("🟡 **中风险**：车辆资源需求较高，需提前规划");
            } else {
                risks.push("🟢 **低风险**：车辆资源需求合理，易于满足");
            }

            if (projectCount > 8) {
                risks.push("🔴 **高风险**：测试项目较多，管理复杂度高");
            } else if (projectCount > 5) {
                risks.push("🟡 **中风险**：测试项目适中，需要有效协调");
            } else {
                risks.push("🟢 **低风险**：测试项目数量合理，管理难度低");
            }

            if (config.complexity === "comprehensive") {
                risks.push("🟡 **中风险**：全面测试复杂度高，需要专业团队");
            }

            if (config.environment === "extreme") {
                risks.push("🟡 **中风险**：极端环境测试，需要特殊准备");
            }

            return risks.join('\n');
        }

        // 格式化日期
        function formatDate(date) {
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            }).replace(/\//g, '-');
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';
            document.querySelector('.generate-button').disabled = true;
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.querySelector('.generate-button').disabled = false;
        }

        // 显示结果
        function showResult(report) {
            generatedReport = report;
            document.getElementById('resultContent').innerHTML = formatMarkdownToHTML(report);
            document.getElementById('resultSection').style.display = 'block';
            document.getElementById('resultSection').scrollIntoView({ behavior: 'smooth' });
        }

        // 将Markdown转换为HTML
        function formatMarkdownToHTML(markdown) {
            return markdown
                .replace(/### (.*?)$/gm, '<h3>$1</h3>')
                .replace(/## (.*?)$/gm, '<h2>$1</h2>')
                .replace(/# (.*?)$/gm, '<h1>$1</h1>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/^- (.*?)$/gm, '<li>$1</li>')
                .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
                .replace(/^\| (.+) \|$/gm, function(match, content) {
                    const cells = content.split(' | ');
                    const isHeader = cells.some(cell => cell.includes('**'));
                    return '<tr>' + cells.map(cell => {
                        const cleanCell = cell.replace(/\*\*/g, '');
                        return isHeader ? `<th>${cleanCell}</th>` : `<td>${cleanCell}</td>`;
                    }).join('') + '</tr>';
                })
                .replace(/(<tr>.*?<\/tr>)/gs, '<table>$1</table>')
                .replace(/\n\n/g, '</p><p>')
                .replace(/^(?!<[h|u|t])/gm, '<p>')
                .replace(/$(?!<\/[h|u|t])/gm, '</p>')
                .replace(/<p><\/p>/g, '')
                .replace(/\n/g, '<br>');
        }

        // 下载报告
        function downloadReport() {
            const blob = new Blob([generatedReport], { type: 'text/markdown;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `DVP_Report_${new Date().toISOString().slice(0,10)}.md`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 复制到剪贴板
        async function copyToClipboard() {
            try {
                await navigator.clipboard.writeText(generatedReport);
                alert('报告已复制到剪贴板！');
            } catch (err) {
                console.error('复制失败:', err);
                // 备用方法
                const textArea = document.createElement('textarea');
                textArea.value = generatedReport;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('报告已复制到剪贴板！');
            }
        }

        // 重置表单
        function resetForm() {
            if (confirm('确定要重新开始生成新的DVP方案吗？')) {
                document.getElementById('resultSection').style.display = 'none';
                document.querySelector('.demo-form').scrollIntoView({ behavior: 'smooth' });
            }
        }

        // 显示技术细节
        function showTechnicalDetails() {
            const details = `
## 🔧 技术实现细节

### 核心技术栈
- **前端界面**: HTML5 + CSS3 + JavaScript (ES6+)
- **AI推理模型**: DeepSeek-R1 (SiliconCloud API)
- **知识图谱**: 内置ADAS项目关系数据库
- **算法引擎**: 多因子智能计算算法

### 关键特性
- **智能匹配**: 基于组件配置的项目匹配算法
- **LLM推理**: 大模型驱动的车辆数量计算
- **并行优化**: 智能测试排期和资源分配
- **风险评估**: 多维度风险分析和缓解建议

### 数据来源
- 知识图谱包含10+个主要ADAS测试项目
- 12种常见车辆传感器和控制器组件
- 基于行业标准的测试周期和车辆基线数据

### 计算逻辑
1. **基础匹配**: 组件与项目的关联度计算
2. **复杂度调整**: 基于测试难度的资源调整
3. **环境因子**: 考虑测试环境的影响系数
4. **并行优化**: 识别可并行执行的测试组合
5. **风险缓解**: 基于项目风险的备用资源配置
            `;

            document.getElementById('resultContent').innerHTML += formatMarkdownToHTML(details);
        }
    </script>
</body>
</html>