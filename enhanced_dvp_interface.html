<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DVP智能生成系统 - 可视化增强版</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        :root {
            /* CodeBuddy Dark Theme Colors */
            --neutrals--black: #000000;
            --neutrals--white: #ffffff;
            --neutrals--gray50: #f7f7f8;
            --neutrals--gray100: #dce2e5;
            --neutrals--gray200: #becacf;
            --neutrals--gray300: #95a8b2;
            --neutrals--gray400: #7e95a0;
            --neutrals--gray500: #5c7785;
            --neutrals--gray600: #4b626d;
            --neutrals--gray700: #324148;
            --neutrals--gray800: #1f292e;
            --neutrals--gray850: #1d232f;
            --neutrals--gray900: #151b1e;

            /* Primary Colors */
            --primary: #22d38b;
            --primary--primary50: #e3f8f0;
            --primary--primary100: #baeddb;
            --primary--primary200: #aaf2dc;
            --primary--primary300: #47d7ac;
            --primary--primary400: #00cd99;
            --primary--primary500: #00c386;
            --primary--primary600: #00b37a;
            --primary--primary700: #00a06b;
            --primary--primary800: #008e5e;
            --primary--primary900: #006e45;

            /* Secondary Colors */
            --secondary: #3385ff;
            --secondary--secondary50: #e4f2ff;
            --secondary--secondary100: #bddfff;
            --secondary--secondary200: #92ccff;
            --secondary--secondary300: #66b7ff;
            --secondary--secondary400: #45a6ff;
            --secondary--secondary500: #2f97ff;
            --secondary--secondary600: #3388ff;
            --secondary--secondary700: #3475ea;
            --secondary--secondary800: #3363d8;
            --secondary--secondary900: #3242b8;

            /* Text Colors */
            --text--primary: var(--neutrals--gray100);
            --text--secondary: var(--neutrals--gray200);
            --text--subtle: var(--neutrals--gray600);
            --text--brand: var(--primary--primary300);

            /* Background Colors */
            --dark: #18181e;
            --subtle: #424249;

            /* Typography */
            --text--h1: 3rem;
            --text--h2: 2.5rem;
            --text--h3: 2rem;
            --text--h4: 1.5rem;
            --text--h5: 1.25rem;
            --text--h6: 1rem;
            --text--body-text: 1rem;
            --text--medium-text: 0.875rem;
            --text--small-text: 0.75rem;
            --text--large-text: 1.125rem;
            --text--extra-large-text: 1.375rem;

            /* Border Radius */
            --border-radius: 0.75rem;

            /* Spacing */
            --spacing: 2rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: var(--neutrals--black);
            color: var(--text--primary);
            height: 100vh;
            overflow: hidden;
            font-size: var(--text--body-text);
            line-height: 1.5;
        }

        .main-container {
            display: flex;
            height: 100vh;
            background: var(--neutrals--gray900);
            margin: 1rem;
            border-radius: var(--border-radius);
            box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.5);
            overflow: hidden;
            border: 1px solid var(--neutrals--gray800);
        }

        /* 左侧输入区域 */
        .input-panel {
            width: 35%;
            background: var(--neutrals--gray850);
            border-right: 1px solid var(--neutrals--gray700);
            display: flex;
            flex-direction: column;
        }

        .input-header {
            background: var(--neutrals--gray800);
            color: var(--text--primary);
            padding: 1.5rem;
            text-align: left;
            border-bottom: 1px solid var(--neutrals--gray700);
            position: relative;
            overflow: hidden;
        }

        .input-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary--primary500) 0%, var(--secondary--secondary500) 100%);
        }

        .input-header h1 {
            font-size: var(--text--h4);
            margin-bottom: 0.5rem;
            font-weight: 700;
            color: var(--text--primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .input-header h1::before {
            content: '🚗';
            font-size: 1.5rem;
        }

        .input-content {
            flex: 1;
            padding: 1.5rem;
            overflow-y: auto;
            background: var(--neutrals--gray850);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: var(--text--primary);
            margin-bottom: 0.5rem;
            font-size: var(--text--medium-text);
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--neutrals--gray700);
            border-radius: var(--border-radius);
            font-size: var(--text--medium-text);
            background: var(--neutrals--gray800);
            color: var(--text--primary);
            transition: all 0.3s ease;
        }

        .form-group select {
            cursor: pointer;
        }

        .form-group select option {
            background: var(--neutrals--gray800);
            color: var(--text--primary);
            padding: 0.5rem;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: var(--primary--primary500);
            box-shadow: 0 0 0 3px rgba(34, 211, 139, 0.1);
            background: var(--neutrals--gray700);
        }

        .form-group input::placeholder {
            color: var(--text--subtle);
        }

        .components-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .component-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background: var(--neutrals--gray800);
            border-radius: var(--border-radius);
            border: 1px solid var(--neutrals--gray700);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: var(--text--small-text);
            color: var(--text--primary);
        }

        .component-item:hover {
            border-color: var(--primary--primary500);
            background: var(--neutrals--gray700);
            box-shadow: 0 2px 8px rgba(34, 211, 139, 0.2);
        }

        .component-item.selected {
            border-color: var(--primary--primary400);
            color: var(--neutrals--black);
        }

        .component-item input[type="checkbox"] {
            width: auto;
            margin-right: 0.5rem;
            accent-color: var(--primary--primary500);
            appearance: none;
            -webkit-appearance: none;
            width: 1rem;
            height: 1rem;
            border: 2px solid var(--neutrals--gray600);
            border-radius: 0.25rem;
            background: var(--neutrals--gray800);
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .component-item input[type="checkbox"]:checked {
            background: var(--primary--primary500);
            border-color: var(--primary--primary500);
        }

        .component-item input[type="checkbox"]:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: var(--neutrals--white);
            font-size: 0.75rem;
            font-weight: 600;
        }

        .component-item input[type="checkbox"]:hover {
            border-color: var(--primary--primary400);
        }

        .component-item input[type="checkbox"]:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(34, 211, 139, 0.2);
        }

        .generate-btn {
            background: linear-gradient(135deg, var(--primary--primary500) 0%, var(--primary--primary700) 100%);
            color: var(--neutrals--white);
            border: none;
            padding: 0.875rem 1.5rem;
            font-size: var(--text--medium-text);
            font-weight: 600;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 1.5rem;
            box-shadow: 0 4px 12px rgba(34, 211, 139, 0.3);
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(34, 211, 139, 0.4);
            background: linear-gradient(135deg, var(--primary--primary400) 0%, var(--primary--primary600) 100%);
        }

        .generate-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(34, 211, 139, 0.2);
        }

        .generate-btn:disabled {
            background: var(--neutrals--gray600);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            opacity: 0.6;
        }

        .generate-btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        /* 测试按钮样式 */
        .test-btn {
            width: 100%;
            margin-top: 0.75rem;
            background: var(--neutrals--gray700);
            color: var(--text--primary);
            border: 1px solid var(--neutrals--gray600);
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            font-size: var(--text--small-text);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .test-btn:hover {
            background: var(--neutrals--gray600);
            border-color: var(--neutrals--gray500);
            transform: translateY(-1px);
        }

        .test-btn.secondary {
            background: var(--secondary--secondary800);
            border-color: var(--secondary--secondary700);
            color: var(--neutrals--white);
        }

        .test-btn.secondary:hover {
            background: var(--secondary--secondary700);
            border-color: var(--secondary--secondary600);
        }

        .test-btn.graph {
            background: linear-gradient(135deg, var(--neutrals--gray700) 0%, var(--neutrals--gray600) 100%);
            border-color: var(--neutrals--gray500);
            color: var(--text--primary);
        }

        .test-btn.graph:hover {
            background: linear-gradient(135deg, var(--neutrals--gray600) 0%, var(--neutrals--gray500) 100%);
        }

        /* 数据状态面板 */
        .data-status-panel {
            margin-top: 1rem;
            padding: 0.75rem;
            background: var(--neutrals--gray800);
            border-radius: var(--border-radius);
            border: 1px solid var(--neutrals--gray700);
        }

        .status-title {
            font-size: var(--text--small-text);
            color: var(--text--primary);
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .status-item {
            font-size: 0.6875rem;
            color: var(--text--secondary);
            margin-bottom: 0.25rem;
        }

        .status-item:last-child {
            margin-bottom: 0;
        }

        /* API性能监控面板 */
        .api-performance-panel {
            margin-top: 1rem;
            padding: 0.75rem;
            background: var(--neutrals--gray800);
            border-radius: var(--border-radius);
            border: 1px solid var(--neutrals--gray700);
            border-left: 3px solid var(--secondary--secondary500);
        }

        .api-performance-panel .status-item {
            font-size: 0.6875rem;
            color: var(--text--secondary);
            margin-bottom: 0.25rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .api-performance-panel .status-item:last-child {
            margin-bottom: 0;
        }

        /* API状态指示器颜色 */
        .status-connected {
            color: var(--primary--primary500) !important;
        }

        .status-disconnected {
            color: #ef4444 !important;
        }

        .status-warning {
            color: #f59e0b !important;
        }

        .status-fallback {
            color: var(--secondary--secondary400) !important;
        }

        /* 右侧输出区域 */
        .output-panel {
            width: 65%;
            display: flex;
            flex-direction: column;
            background: var(--dark);
        }

        .output-header {
            background: var(--neutrals--gray800);
            color: var(--text--primary);
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--neutrals--gray700);
            position: relative;
            overflow: hidden;
        }

        .output-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--secondary--secondary500) 0%, var(--primary--primary500) 100%);
        }

        .output-header h1 {
            font-size: var(--text--h4);
            margin: 0;
            font-weight: 700;
            color: var(--text--primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .output-header h1::before {
            content: '📊';
            font-size: 1.5rem;
        }

        .export-buttons {
            display: flex;
            gap: 0.75rem;
        }

        .export-btn {
            background: rgba(255, 255, 255, 0.1);
            color: var(--neutrals--white);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: var(--text--small-text);
            font-weight: 500;
        }

        .export-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
        }

        .export-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(255, 255, 255, 0.1);
        }

        .export-btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
        }

        .output-content {
            flex: 1;
            overflow-y: auto;
            padding: 1.5rem;
            background: var(--dark);
        }

        /* 数字卡片 */
        .metrics-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: var(--neutrals--gray850);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            border: 1px solid var(--neutrals--gray700);
            border-left: 4px solid var(--primary--primary500);
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
            border-left-color: var(--primary--primary400);
        }

        .metric-card h3 {
            color: var(--text--subtle);
            font-size: var(--text--small-text);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }

        .metric-card .value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text--primary);
            margin-bottom: 0.25rem;
        }

        .metric-card .unit {
            color: var(--text--secondary);
            font-size: var(--text--small-text);
        }

        .metric-card.vehicles { border-left-color: #ef4444; }
        .metric-card.duration { border-left-color: #f59e0b; }
        .metric-card.projects { border-left-color: #8b5cf6; }

        /* 图表容器样式已移除 - 不再需要复杂度分布图表 */

        /* chart-canvas 样式已移除 - 不再需要图表画布 */

        /* 甘特图 */
        .gantt-section {
            background: var(--neutrals--gray850);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            border: 1px solid var(--neutrals--gray700);
            margin-bottom: 2rem;
        }

        .gantt-section h3 {
            color: var(--text--primary);
            margin-bottom: 1.5rem;
            font-size: var(--text--h5);
            font-weight: 600;
        }

        .gantt-container {
            overflow-x: auto;
            min-height: 400px;
            border: 1px solid var(--neutrals--gray700);
            border-radius: var(--border-radius);
            background: var(--neutrals--gray800);
        }

        .gantt-timeline {
            position: relative;
            min-width: 900px;
            background: var(--neutrals--gray800);
        }

        .gantt-header {
            height: 50px;
            background: linear-gradient(135deg, var(--neutrals--gray700) 0%, var(--neutrals--gray600) 100%);
            border-bottom: 2px solid var(--neutrals--gray600);
            display: flex;
            align-items: center;
            padding: 0 1.5rem;
            font-weight: 600;
            color: var(--text--primary);
            font-size: var(--text--medium-text);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .gantt-row {
            height: 45px;
            border-bottom: 1px solid var(--neutrals--gray700);
            position: relative;
            display: flex;
            align-items: center;
            padding: 0;
            transition: background-color 0.2s ease;
        }

        .gantt-row:hover {
            background-color: var(--neutrals--gray700);
        }

        .gantt-row:nth-child(even) {
            background-color: var(--neutrals--gray850);
        }

        .gantt-project-label {
            position: absolute;
            left: 1rem;
            font-size: var(--text--small-text);
            font-weight: 600;
            color: var(--text--primary);
            width: 180px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            z-index: 5;
        }

        .gantt-bar {
            position: absolute;
            height: 28px;
            border-radius: var(--border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--neutrals--white);
            font-size: 0.6875rem;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            min-width: 60px;
            margin-left: 200px;
        }

        .gantt-bar:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        }

        .gantt-bar.高 {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            border-left: 4px solid #b91c1c;
        }

        .gantt-bar.中 {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            border-left: 4px solid #b45309;
        }

        .gantt-bar.低 {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-left: 4px solid #047857;
        }

        .gantt-bar-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            width: 100%;
        }

        .gantt-bar-duration {
            font-size: 12px;
            font-weight: bold;
        }

        .gantt-bar-vehicles {
            font-size: 10px;
            opacity: 0.9;
            margin-top: 1px;
        }

        /* 甘特图时间轴标尺 */
        .gantt-time-scale {
            height: 30px;
            background: #f1f5f9;
            border-bottom: 1px solid #cbd5e1;
            display: flex;
            align-items: center;
            font-size: 11px;
            color: #64748b;
            position: relative;
        }

        .gantt-time-marker {
            position: absolute;
            height: 100%;
            border-left: 1px solid #cbd5e1;
            display: flex;
            align-items: center;
            padding-left: 5px;
        }

        /* 项目详情表格 */
        .projects-table {
            background: var(--neutrals--gray850);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            border: 1px solid var(--neutrals--gray700);
            overflow-x: auto;
        }

        .projects-table h3 {
            color: var(--text--primary);
            margin-bottom: 1rem;
            font-size: var(--text--h5);
            font-weight: 600;
        }

        .projects-table table {
            width: 100%;
            border-collapse: collapse;
            font-size: var(--text--small-text);
        }

        .projects-table th,
        .projects-table td {
            text-align: left;
            padding: 0.75rem;
            border-bottom: 1px solid var(--neutrals--gray700);
        }

        .projects-table th {
            background: var(--neutrals--gray800);
            font-weight: 600;
            color: var(--text--primary);
        }

        .projects-table td {
            color: var(--text--secondary);
        }

        .complexity-badge {
            padding: 0.25rem 0.5rem;
            border-radius: var(--border-radius);
            font-size: 0.625rem;
            font-weight: 600;
        }

        .complexity-badge.high { background: #fef2f2; color: #dc2626; }
        .complexity-badge.medium { background: #fffbeb; color: #d97706; }
        .complexity-badge.low { background: #f0fdf4; color: #16a34a; }

        /* 加载状态 */
        .loading-state {
            display: none;
            padding: 1.5rem;
            color: var(--text--subtle);
            text-align: center;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid var(--neutrals--gray700);
            border-top: 4px solid var(--primary--primary500);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 进度轨道图样式 */
        .progress-pipeline {
            background: linear-gradient(135deg, var(--neutrals--gray800) 0%, var(--neutrals--gray700) 100%);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            border: 1px solid var(--neutrals--gray600);
        }

        .pipeline-header {
            text-align: center;
            margin-bottom: 1.5rem;
        }

        .pipeline-header h3 {
            color: var(--text--primary);
            margin: 0 0 0.5rem 0;
            font-size: var(--text--h5);
            font-weight: 600;
        }

        .pipeline-subtitle {
            color: var(--text--secondary);
            font-size: var(--text--small-text);
        }

        .pipeline-track {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .pipeline-stage {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;
            padding: 1rem 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--border-radius);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .pipeline-stage.active {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--secondary--secondary400);
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(51, 136, 255, 0.3);
        }

        .pipeline-stage.completed {
            background: rgba(34, 211, 139, 0.1);
            border-color: var(--primary--primary500);
        }

        .pipeline-stage.error {
            background: rgba(239, 68, 68, 0.1);
            border-color: #ef4444;
        }

        .stage-icon {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        .stage-label {
            color: var(--text--primary);
            font-size: var(--text--small-text);
            font-weight: 600;
            text-align: center;
            margin-bottom: 0.25rem;
            line-height: 1.2;
        }

        .stage-status {
            color: var(--text--secondary);
            font-size: 0.625rem;
            text-align: center;
        }

        .pipeline-stage.active .stage-status {
            color: var(--secondary--secondary400);
            font-weight: 600;
        }

        .pipeline-stage.completed .stage-status {
            color: var(--primary--primary500);
            font-weight: 600;
        }

        .pipeline-stage.error .stage-status {
            color: #ef4444;
            font-weight: 600;
        }

        .pipeline-connector {
            flex: 1;
            height: 3px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            position: relative;
            margin: 0 0.5rem;
            min-width: 20px;
        }

        .pipeline-connector.active {
            background: linear-gradient(90deg, var(--primary--primary500) 0%, var(--secondary--secondary400) 100%);
            animation: flow 2s ease-in-out infinite;
        }

        @keyframes flow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 终端日志背景样式 */
        .terminal-background {
            background: var(--neutrals--black);
            border-radius: var(--border-radius);
            overflow: hidden;
            margin-top: 1.5rem;
            min-height: 400px;
            position: relative;
            border: 1px solid var(--neutrals--gray700);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
        }

        .terminal-header {
            background: var(--neutrals--gray800);
            padding: 0.75rem 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--neutrals--gray700);
        }

        .terminal-title {
            color: var(--primary--primary400);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
            font-size: var(--text--small-text);
            font-weight: 600;
        }

        .terminal-time {
            color: var(--text--subtle);
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
            font-size: 0.6875rem;
        }

        .terminal-content {
            padding: 1rem;
            height: 350px;
            overflow-y: auto;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
            font-size: 0.6875rem;
            line-height: 1.4;
            background: var(--neutrals--black);
            text-align: left;
        }

        .log-line {
            color: var(--primary--primary300);
            margin-bottom: 0.25rem;
            opacity: 0.9;
            animation: fadeIn 0.5s ease-in;
            text-align: left;
            word-wrap: break-word;
        }

        .log-line.error {
            color: #ef4444;
        }

        .log-line.warning {
            color: #f59e0b;
        }

        .log-line.info {
            color: var(--secondary--secondary400);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 0.9; transform: translateY(0); }
        }

        .loading-content {
            text-align: center;
            padding: 1.5rem;
            background: var(--neutrals--gray850);
            border-radius: var(--border-radius);
            margin: 1.5rem 0;
            border: 1px solid var(--neutrals--gray700);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .loading-text {
            color: var(--text--primary);
            font-size: var(--text--medium-text);
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .pipeline-track {
                flex-direction: column;
            }

            .pipeline-connector {
                width: 3px;
                height: 20px;
                margin: 8px 0;
            }

            .pipeline-stage {
                min-width: 200px;
            }
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 4rem 1.5rem;
            color: var(--text--subtle);
        }

        .empty-state svg {
            width: 80px;
            height: 80px;
            margin-bottom: 1.5rem;
            opacity: 0.5;
        }

        /* 知识图谱弹窗样式 */
        .graph-modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(8px);
        }

        .graph-modal-content {
            background-color: var(--neutrals--gray900);
            margin: 2% auto;
            padding: 0;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 1200px;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.5), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
            border: 1px solid var(--neutrals--gray700);
        }

        .graph-modal-header {
            background: linear-gradient(135deg, var(--neutrals--gray800) 0%, var(--neutrals--gray700) 100%);
            color: var(--text--primary);
            padding: 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--neutrals--gray700);
        }

        .graph-modal-header h2 {
            margin: 0;
            font-size: var(--text--h4);
            font-weight: 600;
        }

        .graph-modal-close {
            font-size: 1.75rem;
            font-weight: 600;
            cursor: pointer;
            transition: opacity 0.3s ease;
            color: var(--text--secondary);
        }

        .graph-modal-close:hover {
            opacity: 0.7;
            color: var(--text--primary);
        }

        .graph-modal-body {
            padding: 1.5rem;
            max-height: calc(90vh - 80px);
            overflow-y: auto;
            background: var(--neutrals--gray900);
        }

        .graph-controls {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
            align-items: center;
            flex-wrap: wrap;
        }

        .graph-btn {
            background: linear-gradient(135deg, var(--primary--primary500) 0%, var(--primary--primary700) 100%);
            color: var(--neutrals--white);
            border: none;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: var(--text--small-text);
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .graph-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(34, 211, 139, 0.3);
            background: linear-gradient(135deg, var(--primary--primary400) 0%, var(--primary--primary600) 100%);
        }

        .graph-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(34, 211, 139, 0.2);
        }

        .graph-btn:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(34, 211, 139, 0.2);
        }

        #graphFilter {
            padding: 0.5rem 0.75rem;
            border: 1px solid var(--neutrals--gray700);
            border-radius: var(--border-radius);
            background: var(--neutrals--gray800);
            color: var(--text--primary);
            font-size: var(--text--small-text);
        }

        .graph-metadata {
            margin-top: 1.5rem;
            background: var(--neutrals--gray850);
            padding: 1rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--neutrals--gray700);
        }

        .graph-metadata h3 {
            margin: 0 0 1rem 0;
            color: var(--text--primary);
            font-size: var(--text--medium-text);
            font-weight: 600;
        }

        .graph-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--neutrals--gray800);
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--neutrals--gray700);
        }

        .stat-label {
            font-size: var(--text--small-text);
            color: var(--text--secondary);
        }

        .stat-value {
            font-size: var(--text--medium-text);
            font-weight: 600;
            color: var(--text--primary);
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--neutrals--gray800);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--neutrals--gray600);
            border-radius: 4px;
            transition: background 0.3s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--neutrals--gray500);
        }

        ::-webkit-scrollbar-corner {
            background: var(--neutrals--gray800);
        }

        /* Firefox滚动条 */
        * {
            scrollbar-width: thin;
            scrollbar-color: var(--neutrals--gray600) var(--neutrals--gray800);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .input-panel { width: 40%; }
            .output-panel { width: 60%; }
            .charts-section { grid-template-columns: 1fr; }
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
                margin: 10px;
            }
            .input-panel, .output-panel {
                width: 100%;
            }
            .input-panel {
                height: 50vh;
            }
            .components-grid {
                grid-template-columns: 1fr;
            }
            .graph-modal-content {
                width: 95%;
                margin: 5% auto;
            }
            .graph-controls {
                flex-direction: column;
                align-items: stretch;
            }
            .graph-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 左侧输入面板 -->
        <div class="input-panel">
            <div class="input-header">
                <h1>DVP智能生成</h1>
                <p style="color: var(--text--secondary); font-size: var(--text--small-text); margin: 0;">配置输入与参数设置</p>
            </div>

            <div class="input-content">
                <div class="form-group">
                    <label>配置类型</label>
                    <select id="configType">
                        <option value="ADAS基础配置">ADAS基础配置</option>
                        <option value="ADAS L2级配置">ADAS L2级配置</option>
                        <option value="ADAS L2+配置" selected>ADAS L2+配置</option>
                        <option value="高端智能驾驶配置">高端智能驾驶配置</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>DVP开始日期</label>
                    <input type="date" id="startDate" readonly>
                    <small style="color: #6b7280; font-size: 12px;">当前系统日期（自动设置）</small>
                </div>

                <div class="form-group">
                    <label>SOP量产日期</label>
                    <input type="date" id="sopDate">
                    <small style="color: #6b7280; font-size: 12px;">默认为开始日期+6个月</small>
                </div>

                <div class="form-group">
                    <label>车辆组件配置</label>
                    <div class="components-grid" id="componentsGrid">
                        <!-- 组件列表将通过API动态加载 -->
                        <div class="loading-components" style="text-align: center; padding: 20px; color: #6b7280;">
                            🔄 正在从Neo4j数据库加载配件信息...
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>项目类型</label>
                    <select id="complexity">
                        <option value="standard" selected>次发项目</option>
                        <option value="comprehensive">首发项目</option>
                    </select>
                    <small style="color: #6b7280; font-size: 12px;">首发项目需要更多车辆和测试周期</small>
                </div>

                <div class="form-group">
                    <label>零件变化程度</label>
                    <select id="environment">
                        <option value="normal" selected>单一变化 (P/M策略)</option>
                        <option value="extreme">多重变化 (X/U策略)</option>
                    </select>
                    <small style="color: var(--text--subtle); font-size: var(--text--small-text);">P-完全沿用，M-修改沿用，X/U-全新开发</small>
                </div>

                <div class="form-group">
                    <label>资源优先级</label>
                    <select id="priority">
                        <option value="normal" selected>标准资源</option>
                        <option value="urgent">优先资源 (+20%)</option>
                        <option value="low">节约资源 (-10%)</option>
                    </select>
                    <small style="color: var(--text--subtle); font-size: var(--text--small-text);">影响车辆资源分配和排期优先级</small>
                </div>

                <button class="generate-btn" onclick="generateDVP()">
                    🚀 生成DVP方案
                </button>

                <!-- 测试按钮 -->
                <button class="btn test-btn" onclick="testTerminalLogs()">
                    🖥️ 测试终端日志
                </button>

                <button class="btn test-btn secondary" onclick="testConnection()">
                    🔗 测试API连接
                </button>

                <button class="btn test-btn graph" onclick="showKnowledgeGraph()">
                    🕸️ 查看图谱
                </button>

                <!-- 数据加载状态指示器 -->
                <div id="dataLoadingStatus" class="data-status-panel">
                    <div class="status-title">数据加载状态:</div>
                    <div id="componentsStatus" class="status-item">配件数据: 加载中...</div>
                    <div id="projectsStatus" class="status-item">测试项目: 加载中...</div>
                </div>

                <!-- API性能监控面板 -->
                <div id="apiPerformancePanel" class="api-performance-panel">
                    <div class="status-title">API性能监控:</div>
                    <div id="apiConnectionStatus" class="status-item">连接状态: 检查中...</div>
                    <div id="apiResponseTime" class="status-item">响应时间: --ms</div>
                    <div id="apiFailureCount" class="status-item">失败次数: 0</div>
                    <div id="apiFallbackMode" class="status-item">降级模式: 关闭</div>
                </div>
            </div>
        </div>

        <!-- 右侧输出面板 -->
        <div class="output-panel">
            <div class="output-header">
                <h1>DVP方案可视化</h1>
                <div class="export-buttons">
                    <button class="export-btn" onclick="exportMarkdown()">📄 导出Markdown</button>
                    <button class="export-btn" onclick="exportJSON()">📋 导出JSON</button>
                </div>
            </div>

            <div class="output-content">
                <!-- 空状态 -->
                <div class="empty-state" id="emptyState">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="color: var(--text--subtle);">
                        <path d="M9 2a1 1 0 000 2h6a1 1 0 100-2H9z"/>
                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h12a2 2 0 012 2v14a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2v12h10V7H7z"/>
                    </svg>
                    <h3 style="color: var(--text--primary); font-size: var(--text--h5); margin-bottom: 0.5rem;">等待配置输入</h3>
                    <p style="color: var(--text--secondary); font-size: var(--text--medium-text);">请在左侧配置车型参数，然后点击生成DVP方案</p>
                </div>

                <!-- 加载状态 - 真实进度追踪 -->
                <div class="loading-state" id="loadingState">
                    <!-- 进度轨道图 -->
                    <div class="progress-pipeline">
                        <div class="pipeline-header">
                            <h3>🚀 DVP智能生成流水线</h3>
                            <div class="pipeline-subtitle">AI驱动的汽车设计验证计划生成 - 实时状态监控</div>
                        </div>
                        <div class="pipeline-track">
                            <div class="pipeline-stage" data-stage="init">
                                <div class="stage-icon">🔧</div>
                                <div class="stage-label">初始化配置</div>
                                <div class="stage-status">待处理</div>
                            </div>
                            <div class="pipeline-connector"></div>
                            <div class="pipeline-stage" data-stage="api">
                                <div class="stage-icon">🌐</div>
                                <div class="stage-label">API连接</div>
                                <div class="stage-status">待处理</div>
                            </div>
                            <div class="pipeline-connector"></div>
                            <div class="pipeline-stage" data-stage="neo4j">
                                <div class="stage-icon">🗄️</div>
                                <div class="stage-label">Neo4j知识图谱</div>
                                <div class="stage-status">待处理</div>
                            </div>
                            <div class="pipeline-connector"></div>
                            <div class="pipeline-stage" data-stage="llm">
                                <div class="stage-icon">🧠</div>
                                <div class="stage-label">DeepSeek-R1大模型</div>
                                <div class="stage-status">待处理</div>
                            </div>
                            <div class="pipeline-connector"></div>
                            <div class="pipeline-stage" data-stage="calculation">
                                <div class="stage-icon">📊</div>
                                <div class="stage-label">车辆计算</div>
                                <div class="stage-status">待处理</div>
                            </div>
                            <div class="pipeline-connector"></div>
                            <div class="pipeline-stage" data-stage="complete">
                                <div class="stage-icon">✅</div>
                                <div class="stage-label">方案完成</div>
                                <div class="stage-status">待处理</div>
                            </div>
                        </div>
                    </div>

                    <!-- 终端日志背景 -->
                    <div class="terminal-background">
                        <div class="terminal-header">
                            <div class="terminal-title">🖥️ DVP-AI 系统实时日志</div>
                            <div class="terminal-time" id="terminalTime"></div>
                        </div>
                        <div class="terminal-content" id="terminalLogs">
                            <div class="log-line">系统初始化完成...</div>
                            <div class="log-line">等待用户请求...</div>
                        </div>
                    </div>
                </div>

                <!-- 结果展示 -->
                <div id="resultsContainer" style="display: none;">
                    <!-- 关键指标卡片 -->
                    <div class="metrics-cards">
                        <div class="metric-card vehicles">
                            <h3>所需车辆</h3>
                            <div class="value" id="vehiclesCount">0</div>
                            <div class="unit">台</div>
                        </div>
                        <div class="metric-card duration">
                            <h3>测试周期</h3>
                            <div class="value" id="testDuration">0</div>
                            <div class="unit">天</div>
                        </div>
                        <div class="metric-card projects">
                            <h3>匹配项目</h3>
                            <div class="value" id="projectsCount">0</div>
                            <div class="unit">个</div>
                        </div>
                    </div>

                    <!-- 图表区域已移除 - 项目复杂度分布饼图没有真实数据源支撑 -->

                    <!-- 甘特图 -->
                    <div class="gantt-section">
                        <h3>📅 项目时间轴甘特图</h3>
                        <div class="gantt-container">
                            <div class="gantt-timeline" id="ganttChart">
                                <div class="gantt-header">项目排期时间轴</div>
                                <div id="ganttContent"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 项目详情表格 -->
                    <div class="projects-table">
                        <h3>📋 匹配项目详情</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>项目名称</th>
                                    <th>匹配度</th>
                                    <th>复杂度</th>
                                    <th>测试周期</th>
                                    <th>AI计算车辆</th>
                                    <th>匹配组件</th>
                                </tr>
                            </thead>
                            <tbody id="projectsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 知识图谱弹窗 -->
    <div id="graphModal" class="graph-modal" style="display: none;">
        <div class="graph-modal-content">
            <div class="graph-modal-header">
                <h2>🕸️ Neo4j 知识图谱</h2>
                <span class="graph-modal-close" onclick="closeKnowledgeGraph()">&times;</span>
            </div>
            <div class="graph-modal-body">
                <div class="graph-controls">
                    <button onclick="refreshGraph()" class="graph-btn">🔄 刷新图谱</button>
                    <button onclick="resetGraphView()" class="graph-btn">🎯 重置视图</button>
                    <select id="graphFilter" onchange="filterGraph()">
                        <option value="all">显示全部</option>
                        <option value="vehicles">车辆配置</option>
                        <option value="components">组件</option>
                        <option value="projects">测试项目</option>
                    </select>
                </div>
                <div id="graphContainer" style="width: 100%; height: 500px; border: 1px solid #e5e7eb; border-radius: 8px;"></div>
                <div class="graph-metadata">
                    <h3>📊 图谱元数据</h3>
                    <div id="graphStats" class="graph-stats">
                        <div class="stat-item">
                            <span class="stat-label">节点总数:</span>
                            <span id="nodeCount" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">关系总数:</span>
                            <span id="relationCount" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">车辆配置:</span>
                            <span id="vehicleCount" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">组件数量:</span>
                            <span id="componentCount" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">测试项目:</span>
                            <span id="projectCount" class="stat-value">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script>
        // API配置 - 优化版本，解决接口缓慢问题
        const API_CONFIG = {
            BASE_URL: 'http://localhost:5002',
            ENDPOINTS: {
                GENERATE_DVP: '/api/generate-dvp',
                GET_COMPONENTS: '/api/get-components',
                GET_TEST_PROJECTS: '/api/get-test-projects',
                TEST_CONNECTION: '/api/test-connection',
                GET_KNOWLEDGE_GRAPH: '/api/get-knowledge-graph'
            },
            // 性能优化配置
            PERFORMANCE: {
                timeout: 8000,           // 8秒超时
                retries: 2,              // 最多重试2次
                retryDelay: 1000,        // 重试延迟1秒
                healthCheckTimeout: 3000, // 健康检查3秒超时
                concurrentLimit: 3,      // 并发请求限制
                cacheTimeout: 30000      // 缓存30秒
            },
            // 获取完整API URL的辅助方法
            getUrl: function(endpoint) {
                return this.BASE_URL + this.ENDPOINTS[endpoint];
            }
        };

        // API连接状态管理
        let apiConnectionState = {
            isConnected: false,
            lastCheckTime: 0,
            checkInterval: 30000,        // 30秒检查一次
            consecutiveFailures: 0,
            maxFailures: 3,
            fallbackMode: false
        };

        // API请求缓存
        const apiCache = new Map();

        // 优化的fetch函数，带超时、重试机制和性能监控
        async function optimizedFetch(url, options = {}, retries = API_CONFIG.PERFORMANCE.retries) {
            const startTime = Date.now();
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.PERFORMANCE.timeout);

            try {
                const response = await fetch(url, {
                    ...options,
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                // 测量响应时间
                const responseTime = safeMeasureApiResponseTime(startTime);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                // 重置连接失败计数
                apiConnectionState.consecutiveFailures = 0;
                apiConnectionState.isConnected = true;

                // 更新性能监控面板
                safeUpdateApiPerformancePanel();

                return response;
            } catch (error) {
                clearTimeout(timeoutId);

                // 增加失败计数
                apiConnectionState.consecutiveFailures++;
                apiConnectionState.isConnected = false;

                // 更新性能监控面板
                safeUpdateApiPerformancePanel();

                if (error.name === 'AbortError') {
                    safeLog(`⏰ API请求超时 (${API_CONFIG.PERFORMANCE.timeout}ms)`, 'warning');
                    // 记录超时响应时间
                    const responseTimeElement = document.getElementById('apiResponseTime');
                    if (responseTimeElement) {
                        responseTimeElement.innerHTML = `响应时间: <span class="status-disconnected">超时</span>`;
                    }
                } else {
                    // 记录失败响应时间
                    safeMeasureApiResponseTime(startTime);
                }

                // 如果还有重试次数，则重试
                if (retries > 0 && error.name !== 'AbortError') {
                    safeLog(`🔄 API请求失败，${API_CONFIG.PERFORMANCE.retryDelay}ms后重试 (剩余${retries}次)`, 'warning');
                    await new Promise(resolve => setTimeout(resolve, API_CONFIG.PERFORMANCE.retryDelay));
                    return optimizedFetch(url, options, retries - 1);
                }

                // 检查是否需要启用降级模式
                if (apiConnectionState.consecutiveFailures >= apiConnectionState.maxFailures) {
                    apiConnectionState.fallbackMode = true;
                    safeLog('🚨 API连接持续失败，启用降级模式', 'warning');
                    safeUpdateApiPerformancePanel();
                }

                throw error;
            }
        }

        // 全局变量
        let currentDVPData = null;
        let currentTheme = 'light';
        let testProjectsData = null;
        let knowledgeGraphChart = null;
        let graphData = null;

        // 安全的日志函数 - 检查函数是否存在
        function safeLog(message, type = 'info') {
            if (typeof addRealTerminalLog === 'function') {
                addRealTerminalLog(message, type);
            } else {
                console.log(`[${type.toUpperCase()}] ${message}`);
            }
        }

        // 安全的性能监控更新函数
        function safeUpdateApiPerformancePanel() {
            if (typeof updateApiPerformancePanel === 'function') {
                updateApiPerformancePanel();
            }
        }

        // 安全的响应时间测量函数
        function safeMeasureApiResponseTime(startTime) {
            if (typeof measureApiResponseTime === 'function') {
                return measureApiResponseTime(startTime);
            } else {
                const responseTime = Date.now() - startTime;
                console.log(`API响应时间: ${responseTime}ms`);
                return responseTime;
            }
        }

        // API健康检查
        async function checkApiHealth() {
            const now = Date.now();

            // 如果距离上次检查时间不足间隔，跳过检查
            if (now - apiConnectionState.lastCheckTime < apiConnectionState.checkInterval) {
                return apiConnectionState.isConnected;
            }

            try {
                safeLog('🔍 执行API健康检查...', 'info');

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.PERFORMANCE.healthCheckTimeout);

                const response = await fetch(API_CONFIG.getUrl('TEST_CONNECTION'), {
                    method: 'GET',
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (response.ok) {
                    apiConnectionState.isConnected = true;
                    apiConnectionState.consecutiveFailures = 0;
                    apiConnectionState.fallbackMode = false;
                    safeLog('✅ API健康检查通过', 'info');
                } else {
                    throw new Error(`健康检查失败: HTTP ${response.status}`);
                }

            } catch (error) {
                apiConnectionState.isConnected = false;
                apiConnectionState.consecutiveFailures++;
                safeLog(`❌ API健康检查失败: ${error.message}`, 'warning');

                if (apiConnectionState.consecutiveFailures >= apiConnectionState.maxFailures) {
                    apiConnectionState.fallbackMode = true;
                    safeLog('🚨 启用降级模式，将使用模拟数据', 'warning');
                }
            }

            apiConnectionState.lastCheckTime = now;
            return apiConnectionState.isConnected;
        }

        // 降级模式 - 生成模拟DVP数据
        function generateFallbackDVPData(configData) {
            safeLog('🔄 使用降级模式生成DVP方案', 'warning');

            const mockProjects = [
                {
                    name: "ADAS功能验证测试",
                    matchRatio: 0.95,
                    testCycle: 45,
                    complexity: "高",
                    aiCalculatedVehicles: 8,
                    matchedComponents: configData.components.slice(0, 3)
                },
                {
                    name: "整车集成测试",
                    matchRatio: 0.88,
                    testCycle: 30,
                    complexity: "中",
                    aiCalculatedVehicles: 5,
                    matchedComponents: configData.components.slice(1, 4)
                }
            ];

            return {
                success: true,
                data: {
                    id: `DVP-FALLBACK-${Date.now()}`,
                    generatedDate: new Date().toISOString(),
                    config: configData,
                    summary: {
                        totalVehicles: 13,
                        totalDuration: 75,
                        riskLevel: "中等",
                        estimatedCost: "¥2,600万"
                    },
                    matchedProjects: mockProjects,
                    schedule: generateMockSchedule(mockProjects)
                }
            };
        }

        // 生成模拟排期数据
        function generateMockSchedule(projects) {
            const schedule = [];
            let currentDate = new Date();

            projects.forEach((project, index) => {
                const startDate = new Date(currentDate);
                startDate.setDate(startDate.getDate() + index * 10);
                const endDate = new Date(startDate);
                endDate.setDate(endDate.getDate() + project.testCycle);

                schedule.push({
                    projectName: project.name,
                    startDate: startDate.toISOString().split('T')[0],
                    endDate: endDate.toISOString().split('T')[0],
                    vehicles: project.aiCalculatedVehicles,
                    status: index === 0 ? 'active' : 'planned'
                });
            });

            return schedule;
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            safeUpdateApiPerformancePanel(); // 初始化性能监控面板
            checkApiHealth(); // 先检查API健康状态
            loadComponentsFromAPI();
            loadTestProjectsFromAPI();
            presetDefaultConfiguration();
            initTheme();

            // 定期更新API性能监控
            setInterval(safeUpdateApiPerformancePanel, 5000); // 每5秒更新一次
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 配置类型变化时自动选择组件
            document.getElementById('configType').addEventListener('change', function() {
                autoSelectComponents(this.value);
            });
        }

        // 预设默认配置
        function presetDefaultConfiguration() {
            // 延迟执行，等待组件加载完成
            setTimeout(() => {
                autoSelectComponents('ADAS L2+配置');
            }, 1000);
            setDefaultDates();
        }

        // 设置默认日期
        function setDefaultDates() {
            const today = new Date();
            const startDate = new Date(today);
            const sopDate = new Date(today);

            // SOP日期设为当前日期+6个月
            sopDate.setMonth(sopDate.getMonth() + 6);

            // 格式化为YYYY-MM-DD
            const formatDate = (date) => {
                return date.toISOString().split('T')[0];
            };

            document.getElementById('startDate').value = formatDate(startDate);
            document.getElementById('sopDate').value = formatDate(sopDate);
        }

        // 根据配置类型自动选择组件
        function autoSelectComponents(configType) {
            // 清除所有选择
            document.querySelectorAll('.component-item').forEach(item => {
                const checkbox = item.querySelector('input[type="checkbox"]');
                checkbox.checked = false;
                item.classList.remove('selected');
            });

            let componentsToSelect = [];

            switch(configType) {
                case 'ADAS基础配置':
                    componentsToSelect = ['AD摄像头前视/后视/侧视', 'EPS'];
                    break;
                case 'ADAS L2级配置':
                    componentsToSelect = ['AD摄像头前视/后视/侧视', '毫米波雷达', 'EPS', 'DPB+ESP/IPB'];
                    break;
                case 'ADAS L2+配置':
                    componentsToSelect = ['AD摄像头前视/后视/侧视', '毫米波雷达', '激光雷达', 'EPS', 'DPB+ESP/IPB', '环视摄像头'];
                    break;
                case '高端智能驾驶配置':
                    componentsToSelect = ['FSD变更', '激光雷达', '毫米波雷达', '超声波雷达', 'AD摄像头前视/后视/侧视', '环视摄像头', 'DPB+ESP/IPB', 'EPS', '后轮转向', '悬架'];
                    break;
            }

            // 选择对应组件
            componentsToSelect.forEach(component => {
                const checkbox = document.querySelector(`input[value="${component}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                    checkbox.closest('.component-item').classList.add('selected');
                }
            });
        }

        // 生成DVP方案 - 真实进度追踪版本
        async function generateDVP() {
            // 获取用户输入
            const configType = document.getElementById('configType').value;
            const startDate = document.getElementById('startDate').value;
            const sopDate = document.getElementById('sopDate').value;
            const complexity = document.getElementById('complexity').value;
            const environment = document.getElementById('environment').value;
            const priority = document.getElementById('priority').value;

            // 获取选中的组件
            const selectedComponents = [];
            document.querySelectorAll('.component-item input:checked').forEach(checkbox => {
                selectedComponents.push(checkbox.value);
            });

            if (selectedComponents.length === 0) {
                alert('请至少选择一个车辆组件！');
                return;
            }

            // 显示加载状态并重置进度
            showLoading();
            resetRealPipeline();

            // 初始化终端时间
            updateTerminalTime();
            const timeInterval = setInterval(updateTerminalTime, 1000);

            try {
                // 阶段1: 初始化配置
                updatePipelineStage('init', 'active');
                addRealTerminalLog('🚀 DVP生成流程启动');
                addRealTerminalLog(`📋 配置类型: ${configType}`);
                addRealTerminalLog(`🔧 选中组件: ${selectedComponents.join(', ')}`);
                addRealTerminalLog(`📅 时间窗口: ${startDate} → ${sopDate}`);
                await new Promise(resolve => setTimeout(resolve, 800));
                updatePipelineStage('init', 'completed');
                addRealTerminalLog('✅ 配置验证完成');

                // 阶段2: API连接 - 优化版本
                updatePipelineStage('api', 'active');
                addRealTerminalLog('🌐 正在连接后端API服务器...');
                addRealTerminalLog(`📡 目标地址: ${API_CONFIG.BASE_URL}`);
                addRealTerminalLog(`⏱️ 超时设置: ${API_CONFIG.PERFORMANCE.timeout}ms`);

                const configData = {
                    configType,
                    startDate,
                    sopDate,
                    components: selectedComponents,
                    complexity,
                    environment,
                    priority,
                    testProjectsData: testProjectsData
                };

                let result;

                try {
                    // 先检查API健康状态
                    const isHealthy = await checkApiHealth();

                    if (!isHealthy && apiConnectionState.fallbackMode) {
                        // 使用降级模式
                        addRealTerminalLog('🔄 API不可用，使用降级模式', 'warning');
                        result = generateFallbackDVPData(configData);
                        updatePipelineStage('api', 'completed');
                        addRealTerminalLog('✅ 降级模式数据生成完成');
                    } else {
                        // 使用优化的fetch函数
                        const response = await optimizedFetch(API_CONFIG.getUrl('GENERATE_DVP'), {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(configData)
                        });

                        updatePipelineStage('api', 'completed');
                        addRealTerminalLog('✅ API连接成功');
                        addRealTerminalLog('📤 配置数据已发送');

                        result = await response.json();
                    }
                } catch (error) {
                    // API失败时的处理
                    if (apiConnectionState.fallbackMode) {
                        addRealTerminalLog('🔄 API调用失败，切换到降级模式', 'warning');
                        result = generateFallbackDVPData(configData);
                        updatePipelineStage('api', 'completed');
                        addRealTerminalLog('✅ 降级模式数据生成完成');
                    } else {
                        updatePipelineStage('api', 'error', `连接失败`);
                        addRealTerminalLog(`❌ API连接失败: ${error.message}`, 'error');
                        throw error;
                    }
                }

                // 阶段3: Neo4j知识图谱查询
                updatePipelineStage('neo4j', 'active');
                addRealTerminalLog('🗄️ 正在查询Neo4j知识图谱...');
                addRealTerminalLog('🔍 匹配测试项目与组件关系');

                // 模拟Neo4j查询时间
                await new Promise(resolve => setTimeout(resolve, 1500));

                if (!result.success) {
                    updatePipelineStage('neo4j', 'error', '查询失败');
                    addRealTerminalLog(`❌ Neo4j查询失败: ${result.error}`, 'error');
                    throw new Error(result.error || '知识图谱查询失败');
                }

                updatePipelineStage('neo4j', 'completed');
                addRealTerminalLog(`✅ 知识图谱查询完成`);
                addRealTerminalLog(`📊 匹配到 ${result.data.matchedProjects?.length || 0} 个测试项目`);

                // 阶段4: DeepSeek-R1大模型推理
                updatePipelineStage('llm', 'active');
                addRealTerminalLog('🧠 正在调用DeepSeek-R1大模型...');
                addRealTerminalLog('📝 构建DVP推理提示词');
                addRealTerminalLog('⚡ 执行AI智能推理计算');

                // 模拟LLM推理时间
                await new Promise(resolve => setTimeout(resolve, 2000));

                updatePipelineStage('llm', 'completed');
                addRealTerminalLog('✅ DeepSeek-R1推理完成');
                addRealTerminalLog(`🚗 AI推荐车辆数量: ${result.data.summary?.totalVehicles || 0} 台`);

                // 阶段5: 车辆计算与排期生成
                updatePipelineStage('calculation', 'active');
                addRealTerminalLog('📊 正在进行车辆资源计算...');
                addRealTerminalLog('📅 生成智能测试排期');
                addRealTerminalLog('⚖️ 优化资源分配策略');

                await new Promise(resolve => setTimeout(resolve, 1200));

                updatePipelineStage('calculation', 'completed');
                addRealTerminalLog('✅ 车辆计算完成');
                addRealTerminalLog(`⏰ 总测试周期: ${result.data.summary?.totalDuration || 0} 天`);

                // 阶段6: 方案完成
                updatePipelineStage('complete', 'active');
                addRealTerminalLog('📋 正在汇总DVP报告...');
                addRealTerminalLog('💾 保存生成结果');

                await new Promise(resolve => setTimeout(resolve, 500));

                updatePipelineStage('complete', 'completed');
                addRealTerminalLog('🎉 DVP方案生成完成！');
                addRealTerminalLog(`📄 报告ID: ${result.data.id}`);

                // 显示结果
                updateVisualization(result.data);
                currentDVPData = result.data;

                setTimeout(() => {
                    clearInterval(timeInterval);
                    hideLoading();
                    showResults();
                }, 1000);

            } catch (error) {
                console.error('DVP生成失败:', error);

                // 更新当前阶段为错误状态
                const currentStage = document.querySelector('.pipeline-stage.active');
                if (currentStage) {
                    const stageId = currentStage.getAttribute('data-stage');
                    updatePipelineStage(stageId, 'error', '处理失败');
                }

                addRealTerminalLog(`💥 系统错误: ${error.message}`, 'error');
                addRealTerminalLog('🔄 请检查网络连接或联系技术支持', 'warning');

                setTimeout(() => {
                    clearInterval(timeInterval);
                    hideLoading();
                    alert(`DVP生成失败: ${error.message}\n\n请检查网络连接或联系技术支持。`);
                }, 3000);
            }
        }

        // 真实进度追踪系统
        const realPipelineStages = [
            { id: 'init', name: '初始化配置' },
            { id: 'api', name: 'API连接' },
            { id: 'neo4j', name: 'Neo4j知识图谱' },
            { id: 'llm', name: 'DeepSeek-R1大模型' },
            { id: 'calculation', name: '车辆计算' },
            { id: 'complete', name: '方案完成' }
        ];

        // 更新进度阶段状态
        function updatePipelineStage(stageId, status, message = '') {
            const stage = document.querySelector(`[data-stage="${stageId}"]`);
            if (!stage) return;

            // 移除所有状态类
            stage.classList.remove('active', 'completed', 'error');

            // 添加新状态类
            if (status !== 'pending') {
                stage.classList.add(status);
            }

            // 更新状态文本
            const statusElement = stage.querySelector('.stage-status');
            const statusTexts = {
                'pending': '待处理',
                'active': '处理中...',
                'completed': '已完成',
                'error': '失败'
            };

            statusElement.textContent = message || statusTexts[status] || '待处理';

            // 更新连接器状态
            updateConnectors(stageId, status);
        }

        // 更新连接器状态
        function updateConnectors(currentStageId, status) {
            const currentIndex = realPipelineStages.findIndex(s => s.id === currentStageId);
            if (currentIndex === -1) return;

            const connectors = document.querySelectorAll('.pipeline-connector');

            if (status === 'completed' && currentIndex < connectors.length) {
                connectors[currentIndex].classList.add('active');
            }
        }

        // 添加终端日志（兼容旧版本，实际调用真实日志函数）
        function addTerminalLog(message, type = 'info') {
            addRealTerminalLog(message, type);
        }

        // 更新终端时间
        function updateTerminalTime() {
            const terminalTime = document.getElementById('terminalTime');
            if (terminalTime) {
                terminalTime.textContent = new Date().toLocaleString();
            }
        }

        // 注意：已移除模拟函数，现在使用真实的进度追踪

        // 真实终端日志函数
        function addRealTerminalLog(message, type = 'info') {
            const terminalLogs = document.getElementById('terminalLogs');
            if (!terminalLogs) return;

            const logLine = document.createElement('div');
            logLine.className = `log-line ${type}`;

            const timestamp = new Date().toLocaleTimeString();
            logLine.textContent = `[${timestamp}] ${message}`;

            terminalLogs.appendChild(logLine);

            // 自动滚动到底部
            terminalLogs.scrollTop = terminalLogs.scrollHeight;

            // 限制日志行数
            const logLines = terminalLogs.querySelectorAll('.log-line');
            if (logLines.length > 100) {
                logLines[0].remove();
            }
        }

        // 重置真实进度追踪系统
        function resetRealPipeline() {
            realPipelineStages.forEach(stage => {
                updatePipelineStage(stage.id, 'pending');
            });

            // 清除连接器状态
            document.querySelectorAll('.pipeline-connector').forEach(connector => {
                connector.classList.remove('active');
            });

            // 清空终端日志
            const terminalLogs = document.getElementById('terminalLogs');
            if (terminalLogs) {
                terminalLogs.innerHTML = `
                    <div class="log-line">系统初始化完成...</div>
                    <div class="log-line">等待用户请求...</div>
                `;
            }
        }

        // 兼容旧的函数名
        function resetPipeline() {
            resetRealPipeline();
        }

        // 已移除本地硬编码数据生成函数
        // 所有DVP数据现在都通过后端API从Neo4j数据库动态获取

        // 已移除本地硬编码知识图谱函数
        // 所有项目匹配逻辑现在都通过后端API从Neo4j数据库动态查询

        // 已移除本地硬编码计算函数
        // 所有车辆计算和排期生成逻辑现在都通过后端API和LLM智能推理完成

        // 从API动态加载车辆组件列表 - 优化版本
        async function loadComponentsFromAPI() {
            const grid = document.getElementById('componentsGrid');

            try {
                // 检查缓存
                const cacheKey = 'components';
                const cached = apiCache.get(cacheKey);
                if (cached && Date.now() - cached.timestamp < API_CONFIG.PERFORMANCE.cacheTimeout) {
                    console.log('使用缓存的组件数据');
                    renderComponents(cached.data);
                    updateDataLoadingStatus('components', true, cached.data.length);
                    return;
                }

                // 更新加载状态
                grid.innerHTML = `
                    <div class="loading-components" style="text-align: center; padding: 20px; color: var(--text--subtle);">
                        <div style="margin-bottom: 10px;">🔄 正在从Neo4j数据库加载配件信息...</div>
                        <div style="font-size: var(--text--small-text); color: var(--text--secondary);">连接到知识图谱数据库中 (超时: ${API_CONFIG.PERFORMANCE.timeout}ms)</div>
                    </div>
                `;

                const response = await optimizedFetch(API_CONFIG.getUrl('GET_COMPONENTS'));
                const result = await response.json();

                if (result.success && result.components) {
                    // 缓存结果
                    apiCache.set(cacheKey, {
                        data: result.components,
                        timestamp: Date.now()
                    });

                    // 显示成功加载信息
                    grid.innerHTML = `
                        <div style="text-align: center; padding: 10px; color: var(--primary--primary500); font-size: var(--text--small-text);">
                            ✅ 成功从Neo4j数据库加载 ${result.components.length} 个配件
                        </div>
                    `;

                    // 更新状态指示器
                    updateDataLoadingStatus('components', true, result.components.length);

                    // 延迟一秒后渲染组件
                    setTimeout(() => {
                        renderComponents(result.components);
                    }, 1000);
                } else {
                    // 如果API失败，显示错误信息
                    renderComponentsError('无法从Neo4j数据库加载配件列表');
                    updateDataLoadingStatus('components', false);
                }
            } catch (error) {
                console.error('加载组件列表失败:', error);

                // 检查是否可以使用降级模式
                if (apiConnectionState.fallbackMode) {
                    console.log('使用降级模式加载默认组件');
                    const fallbackComponents = [
                        { id: 'camera_front', name: 'AD摄像头前视', category: 'ADAS' },
                        { id: 'camera_rear', name: 'AD摄像头后视', category: 'ADAS' },
                        { id: 'camera_side', name: 'AD摄像头侧视', category: 'ADAS' },
                        { id: 'radar_mm', name: '毫米波雷达', category: 'ADAS' },
                        { id: 'dpb_esp', name: 'DPB+ESP/IPB', category: '制动系统' },
                        { id: 'eps', name: 'EPS', category: '转向系统' }
                    ];

                    renderComponents(fallbackComponents);
                    updateDataLoadingStatus('components', true, fallbackComponents.length);

                    grid.innerHTML = `
                        <div style="text-align: center; padding: 10px; color: var(--secondary--secondary400); font-size: var(--text--small-text); margin-bottom: 1rem;">
                            🔄 使用降级模式加载默认配件 (${fallbackComponents.length} 个)
                        </div>
                    ` + grid.innerHTML;
                } else {
                    renderComponentsError('连接到后端服务失败');
                    updateDataLoadingStatus('components', false);
                }
            }
        }

        // 渲染组件列表
        function renderComponents(components) {
            const grid = document.getElementById('componentsGrid');
            grid.innerHTML = '';

            components.forEach((component, index) => {
                const item = document.createElement('div');
                item.className = 'component-item';
                item.innerHTML = `
                    <input type="checkbox" id="comp${index + 1}" value="${component}">
                    <label for="comp${index + 1}">${component}</label>
                `;
                grid.appendChild(item);
            });

            // 重新设置事件监听器
            setupComponentEventListeners();
        }

        // 渲染组件加载错误
        function renderComponentsError(errorMessage) {
            const grid = document.getElementById('componentsGrid');
            grid.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #ef4444;">
                    ❌ ${errorMessage}
                    <br>
                    <button onclick="loadComponentsFromAPI()" style="margin-top: 10px; padding: 5px 10px; background: #4f46e5; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        🔄 重新加载
                    </button>
                </div>
            `;
        }

        // 从API加载测试项目信息 - 优化版本
        async function loadTestProjectsFromAPI() {
            try {
                // 检查缓存
                const cacheKey = 'test_projects';
                const cached = apiCache.get(cacheKey);
                if (cached && Date.now() - cached.timestamp < API_CONFIG.PERFORMANCE.cacheTimeout) {
                    console.log('使用缓存的测试项目数据');
                    testProjectsData = cached.data;
                    updateDataLoadingStatus('projects', true, cached.data.length);
                    return;
                }

                console.log('正在从Neo4j数据库加载测试项目信息...');

                const response = await optimizedFetch(API_CONFIG.getUrl('GET_TEST_PROJECTS'));
                const result = await response.json();

                if (result.success && result.projects) {
                    testProjectsData = result.projects;

                    // 缓存结果
                    apiCache.set(cacheKey, {
                        data: result.projects,
                        timestamp: Date.now()
                    });

                    console.log(`✅ 成功从Neo4j数据库加载 ${result.projects.length} 个测试项目`);
                    updateDataLoadingStatus('projects', true, result.projects.length);
                } else {
                    console.warn('无法从Neo4j数据库加载测试项目信息');
                    testProjectsData = [];
                    updateDataLoadingStatus('projects', false);
                }
            } catch (error) {
                console.error('加载测试项目信息失败:', error);

                // 检查是否可以使用降级模式
                if (apiConnectionState.fallbackMode) {
                    console.log('使用降级模式加载默认测试项目');
                    testProjectsData = [
                        {
                            id: 'adas_validation',
                            name: 'ADAS功能验证测试',
                            category: 'ADAS',
                            complexity: '高',
                            testCycle: 45,
                            requiredVehicles: 8
                        },
                        {
                            id: 'integration_test',
                            name: '整车集成测试',
                            category: '集成',
                            complexity: '中',
                            testCycle: 30,
                            requiredVehicles: 5
                        }
                    ];
                    updateDataLoadingStatus('projects', true, testProjectsData.length);
                } else {
                    testProjectsData = [];
                    updateDataLoadingStatus('projects', false);
                }
            }
        }

        // 更新数据加载状态指示器
        function updateDataLoadingStatus(dataType, success, count = 0) {
            const statusElement = document.getElementById(dataType === 'components' ? 'componentsStatus' : 'projectsStatus');
            if (statusElement) {
                const label = dataType === 'components' ? '配件数据' : '测试项目';
                if (success) {
                    statusElement.innerHTML = `${label}: <span style="color: var(--primary--primary500);">✅ 已加载 ${count} 个</span>`;
                } else {
                    statusElement.innerHTML = `${label}: <span style="color: #ef4444;">❌ 加载失败</span>`;
                }
            }
        }

        // 更新API性能监控面板
        function updateApiPerformancePanel() {
            const connectionStatus = document.getElementById('apiConnectionStatus');
            const responseTime = document.getElementById('apiResponseTime');
            const failureCount = document.getElementById('apiFailureCount');
            const fallbackMode = document.getElementById('apiFallbackMode');

            if (connectionStatus) {
                if (apiConnectionState.isConnected) {
                    connectionStatus.innerHTML = `连接状态: <span class="status-connected">✅ 已连接</span>`;
                } else {
                    connectionStatus.innerHTML = `连接状态: <span class="status-disconnected">❌ 断开</span>`;
                }
            }

            if (failureCount) {
                const count = apiConnectionState.consecutiveFailures;
                if (count === 0) {
                    failureCount.innerHTML = `失败次数: <span class="status-connected">${count}</span>`;
                } else if (count < apiConnectionState.maxFailures) {
                    failureCount.innerHTML = `失败次数: <span class="status-warning">${count}</span>`;
                } else {
                    failureCount.innerHTML = `失败次数: <span class="status-disconnected">${count}</span>`;
                }
            }

            if (fallbackMode) {
                if (apiConnectionState.fallbackMode) {
                    fallbackMode.innerHTML = `降级模式: <span class="status-fallback">🔄 已启用</span>`;
                } else {
                    fallbackMode.innerHTML = `降级模式: <span class="status-connected">关闭</span>`;
                }
            }
        }

        // 测量API响应时间
        function measureApiResponseTime(startTime) {
            const endTime = Date.now();
            const responseTime = endTime - startTime;

            const responseTimeElement = document.getElementById('apiResponseTime');
            if (responseTimeElement) {
                let statusClass = 'status-connected';
                if (responseTime > 5000) {
                    statusClass = 'status-disconnected';
                } else if (responseTime > 2000) {
                    statusClass = 'status-warning';
                }
                responseTimeElement.innerHTML = `响应时间: <span class="${statusClass}">${responseTime}ms</span>`;
            }

            return responseTime;
        }

        // 设置组件事件监听器
        function setupComponentEventListeners() {
            document.querySelectorAll('.component-item').forEach(item => {
                item.addEventListener('click', function() {
                    const checkbox = this.querySelector('input[type="checkbox"]');
                    if (checkbox) {
                        checkbox.checked = !checkbox.checked;
                        this.classList.toggle('selected', checkbox.checked);
                    }
                });
            });
        }

        // 更新可视化显示
        function updateVisualization(data) {
            // 更新数字卡片
            updateMetricCards(data);

            // 更新甘特图
            updateGanttChart(data);

            // 更新项目表格
            updateProjectsTable(data);
        }

        // 更新数字卡片
        function updateMetricCards(data) {
            document.getElementById('vehiclesCount').textContent = data.summary.totalVehicles;
            document.getElementById('testDuration').textContent = data.summary.totalDuration;
            document.getElementById('projectsCount').textContent = data.summary.totalProjects;
        }

        // 更新甘特图
        function updateGanttChart(data) {
            const ganttContent = document.getElementById('ganttContent');
            ganttContent.innerHTML = '';

            // 正确处理日期字符串转换
            const startDate = new Date(data.schedule.startDate);
            const endDate = new Date(data.schedule.endDate);
            const totalDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

            console.log('甘特图数据:', {
                startDate: startDate.toISOString().split('T')[0],
                endDate: endDate.toISOString().split('T')[0],
                totalDays: totalDays,
                projectsCount: data.schedule.projects.length
            });

            // 添加时间轴标尺
            const timeScale = document.createElement('div');
            timeScale.className = 'gantt-time-scale';

            // 根据总天数动态调整时间标记密度
            const markerInterval = totalDays > 180 ? 30 : (totalDays > 60 ? 15 : 7);

            for (let i = 0; i <= totalDays; i += markerInterval) {
                const marker = document.createElement('div');
                marker.className = 'gantt-time-marker';
                marker.style.left = `${(i / totalDays) * 100}%`;

                const markerDate = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
                marker.textContent = markerDate.toLocaleDateString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit'
                });

                timeScale.appendChild(marker);
            }

            ganttContent.appendChild(timeScale);

            // 添加项目行
            data.schedule.projects.forEach((project, index) => {
                const row = document.createElement('div');
                row.className = 'gantt-row';

                // 处理日期字符串转换为Date对象
                const projectStartDate = new Date(project.startDate);
                const projectEndDate = new Date(project.endDate);

                // 计算项目在时间轴上的开始位置（天数）
                const projectStartDays = Math.floor((projectStartDate - startDate) / (1000 * 60 * 60 * 24));
                // 计算项目实际持续天数
                const projectDurationDays = Math.ceil((projectEndDate - projectStartDate) / (1000 * 60 * 60 * 24));

                // 计算柱子的位置和宽度百分比
                const barLeft = Math.max(0, (projectStartDays / totalDays) * 100);
                const barWidth = Math.min(100 - barLeft, (projectDurationDays / totalDays) * 100);

                row.innerHTML = `
                    <div class="gantt-project-label" title="${project.name}">
                        ${project.name}
                    </div>
                    <div class="gantt-bar ${project.complexity}"
                         style="left: ${barLeft}%; width: ${barWidth}%;"
                         title="项目: ${project.name}
开始: ${projectStartDate.toLocaleDateString()}
结束: ${projectEndDate.toLocaleDateString()}
周期: ${projectDurationDays}天
复杂度: ${project.complexity}
车辆: ${project.vehicles}台">
                        <div class="gantt-bar-content">
                            <div class="gantt-bar-duration">${projectDurationDays}天</div>
                            <div class="gantt-bar-vehicles">${project.vehicles}台</div>
                        </div>
                    </div>
                `;

                ganttContent.appendChild(row);
            });

            // 添加总结行
            const summaryRow = document.createElement('div');
            summaryRow.className = 'gantt-row';
            summaryRow.style.borderTop = '2px solid #cbd5e1';
            summaryRow.style.backgroundColor = '#f8fafc';
            summaryRow.innerHTML = `
                <div class="gantt-project-label" style="font-weight: bold; color: #4f46e5;">
                    总计 (${data.summary.totalProjects}个项目)
                </div>
                <div style="position: absolute; right: 20px; font-size: 12px; color: #6b7280;">
                    总周期: ${data.summary.totalDuration}天 | 总车辆: ${data.summary.totalVehicles}台
                </div>
            `;

            ganttContent.appendChild(summaryRow);
        }

        // 更新项目表格
        function updateProjectsTable(data) {
            const tableBody = document.getElementById('projectsTableBody');
            tableBody.innerHTML = '';

            data.matchedProjects.forEach(project => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${project.name}</td>
                    <td>${(project.matchRatio * 100).toFixed(1)}%</td>
                    <td><span class="complexity-badge ${project.complexity}">${project.complexity}</span></td>
                    <td>${project.testCycle}天</td>
                    <td>${project.aiCalculatedVehicles}台</td>
                    <td>${project.matchedComponents.join(', ')}</td>
                `;
                tableBody.appendChild(row);
            });
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('emptyState').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'none';
            document.getElementById('loadingState').style.display = 'block';
            document.querySelector('.generate-btn').disabled = true;
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loadingState').style.display = 'none';
            document.querySelector('.generate-btn').disabled = false;
        }

        // 显示结果
        function showResults() {
            document.getElementById('emptyState').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';
        }

        // 更新加载文本
        function updateLoadingText(text) {
            const loadingState = document.getElementById('loadingState');
            const paragraph = loadingState.querySelector('p');
            if (paragraph) {
                paragraph.textContent = text;
            }
        }

        // 导出Markdown
        function exportMarkdown() {
            if (!currentDVPData) {
                alert('请先生成DVP方案！');
                return;
            }

            const markdown = generateMarkdownReport(currentDVPData);
            downloadFile(markdown, `DVP_Report_${Date.now()}.md`, 'text/markdown');
        }

        // 导出JSON
        function exportJSON() {
            if (!currentDVPData) {
                alert('请先生成DVP方案！');
                return;
            }

            const json = JSON.stringify(currentDVPData, null, 2);
            downloadFile(json, `DVP_Data_${Date.now()}.json`, 'application/json');
        }

        // 生成Markdown报告
        function generateMarkdownReport(data) {
            return `# 🚗 DVP智能生成方案报告

## 📋 项目基本信息

| 项目 | 值 |
|------|-----|
| **方案编号** | ${data.id} |
| **生成时间** | ${new Date(data.generatedDate).toLocaleString()} |
| **配置类型** | ${data.config.configType} |
| **SOP日期** | ${data.config.sopDate} |
| **车辆组件** | ${data.config.components.join(', ')} |

## 🚙 AI智能车辆计算

### 计算结果
- **所需车辆总数**: ${data.summary.totalVehicles}台
- **测试周期**: ${data.summary.totalDuration}天
- **风险等级**: ${data.summary.riskLevel}

### 匹配的试验项目

${data.matchedProjects.map((project, index) => `
#### ${index + 1}. ${project.name}
- **匹配度**: ${(project.matchRatio * 100).toFixed(1)}%
- **测试周期**: ${project.testCycle}天
- **复杂度**: ${project.complexity}
- **AI计算车辆**: ${project.aiCalculatedVehicles}台
- **匹配组件**: ${project.matchedComponents.join(', ')}
`).join('')}

## 📅 测试排期计划

${data.schedule.projects.map(project => `
- **${project.name}**: ${project.startDate.toLocaleDateString()} ~ ${project.endDate.toLocaleDateString()} (${project.duration}天)
`).join('')}

---
*报告由DVP智能生成系统自动生成，采用纯AI推理算法，确保计算公正性*`;
        }

        // 下载文件
        function downloadFile(content, filename, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 主题切换功能
        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', currentTheme);

            const icon = document.getElementById('theme-icon');
            const text = document.getElementById('theme-text');

            if (currentTheme === 'dark') {
                icon.textContent = '☀️';
                text.textContent = '明亮模式';
            } else {
                icon.textContent = '🌙';
                text.textContent = '暗黑模式';
            }

            localStorage.setItem('theme', currentTheme);

            // 重新渲染图表以适应新主题
            if (currentDVPData) {
                updateCharts(currentDVPData);
            }
        }

        function initTheme() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                currentTheme = savedTheme;
                document.documentElement.setAttribute('data-theme', currentTheme);

                const icon = document.getElementById('theme-icon');
                const text = document.getElementById('theme-text');

                if (currentTheme === 'dark') {
                    icon.textContent = '☀️';
                    text.textContent = '明亮模式';
                } else {
                    icon.textContent = '🌙';
                    text.textContent = '暗黑模式';
                }
            }
        }

        // 测试API连接 - 优化版本
        async function testConnection() {
            try {
                showLoading();
                updateLoadingText('正在测试API连接...');

                safeLog('🔍 开始API连接测试', 'info');
                safeLog(`⏱️ 超时设置: ${API_CONFIG.PERFORMANCE.timeout}ms`, 'info');

                // 并行测试多个API端点，使用优化的fetch
                const testPromises = [
                    optimizedFetch(API_CONFIG.getUrl('TEST_CONNECTION')).catch(e => ({ error: e.message })),
                    optimizedFetch(API_CONFIG.getUrl('GET_COMPONENTS')).catch(e => ({ error: e.message })),
                    optimizedFetch(API_CONFIG.getUrl('GET_TEST_PROJECTS')).catch(e => ({ error: e.message }))
                ];

                const [connectionTest, componentsTest, projectsTest] = await Promise.allSettled(testPromises);

                hideLoading();

                let statusMsg = '🔗 API连接测试结果:\n\n';
                let allSuccess = true;

                // 测试基础连接
                if (connectionTest.status === 'fulfilled' && !connectionTest.value.error) {
                    try {
                        const result = await connectionTest.value.json();
                        statusMsg += `✅ SiliconCloud LLM: ${result.siliconcloud ? '连接正常' : '连接失败'}\n`;
                        statusMsg += `✅ Neo4j数据库: ${result.neo4j ? '连接正常' : '连接失败'}\n`;
                        statusMsg += `✅ 业务逻辑矩阵: ${result.matrixData ? '数据加载成功' : '数据加载失败'}\n`;
                        safeLog('✅ 基础连接测试通过', 'info');
                    } catch (e) {
                        statusMsg += '❌ 基础连接测试失败 (响应解析错误)\n';
                        allSuccess = false;
                        safeLog('❌ 基础连接测试失败', 'error');
                    }
                } else {
                    const error = connectionTest.value?.error || '连接超时';
                    statusMsg += `❌ 基础连接测试失败: ${error}\n`;
                    allSuccess = false;
                    safeLog(`❌ 基础连接测试失败: ${error}`, 'error');
                }

                // 测试组件API
                if (componentsTest.status === 'fulfilled' && !componentsTest.value.error) {
                    try {
                        const result = await componentsTest.value.json();
                        statusMsg += `✅ 配件数据API: ${result.success ? `成功 (${result.components?.length || 0}个配件)` : '失败'}\n`;
                        safeLog('✅ 配件数据API测试通过', 'info');
                    } catch (e) {
                        statusMsg += '❌ 配件数据API: 响应解析失败\n';
                        allSuccess = false;
                        safeLog('❌ 配件数据API测试失败', 'error');
                    }
                } else {
                    const error = componentsTest.value?.error || '连接超时';
                    statusMsg += `❌ 配件数据API: ${error}\n`;
                    allSuccess = false;
                    safeLog(`❌ 配件数据API失败: ${error}`, 'error');
                }

                // 测试项目API
                if (projectsTest.status === 'fulfilled' && !projectsTest.value.error) {
                    try {
                        const result = await projectsTest.value.json();
                        statusMsg += `✅ 测试项目API: ${result.success ? `成功 (${result.projects?.length || 0}个项目)` : '失败'}\n`;
                        safeLog('✅ 测试项目API测试通过', 'info');
                    } catch (e) {
                        statusMsg += '❌ 测试项目API: 响应解析失败\n';
                        allSuccess = false;
                        safeLog('❌ 测试项目API测试失败', 'error');
                    }
                } else {
                    const error = projectsTest.value?.error || '连接超时';
                    statusMsg += `❌ 测试项目API: ${error}\n`;
                    allSuccess = false;
                    safeLog(`❌ 测试项目API失败: ${error}`, 'error');
                }

                // 更新连接状态
                if (allSuccess) {
                    apiConnectionState.isConnected = true;
                    apiConnectionState.consecutiveFailures = 0;
                    apiConnectionState.fallbackMode = false;
                    statusMsg += '\n🎉 系统已就绪，可以生成真实DVP方案！';
                    safeLog('🎉 所有API测试通过，系统就绪', 'info');
                } else {
                    apiConnectionState.isConnected = false;
                    apiConnectionState.consecutiveFailures++;
                    if (apiConnectionState.consecutiveFailures >= apiConnectionState.maxFailures) {
                        apiConnectionState.fallbackMode = true;
                        statusMsg += '\n⚠️ 部分API连接失败，已启用降级模式';
                        safeLog('⚠️ 启用降级模式', 'warning');
                    } else {
                        statusMsg += '\n⚠️ 部分API连接失败，请检查网络或后端服务';
                    }
                }

                alert(statusMsg);

            } catch (error) {
                hideLoading();
                safeLog(`💥 连接测试异常: ${error.message}`, 'error');
                alert(`连接测试失败: ${error.message}\n\n请确保后端服务器正在运行。`);
            }
        }

        // 知识图谱相关功能
        async function showKnowledgeGraph() {
            document.getElementById('graphModal').style.display = 'block';

            // 如果图表还未初始化，则初始化
            if (!knowledgeGraphChart) {
                initKnowledgeGraph();
            }

            // 加载图谱数据
            await loadGraphData();
        }

        function closeKnowledgeGraph() {
            document.getElementById('graphModal').style.display = 'none';
        }

        function initKnowledgeGraph() {
            const container = document.getElementById('graphContainer');
            knowledgeGraphChart = echarts.init(container);

            // 设置图表的基本配置
            const option = {
                title: {
                    text: 'Neo4j 知识图谱',
                    left: 'center',
                    textStyle: {
                        color: '#333',
                        fontSize: 18
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        if (params.dataType === 'node') {
                            return `<strong>${params.data.name}</strong><br/>类型: ${params.data.category}<br/>属性: ${JSON.stringify(params.data.properties || {}, null, 2)}`;
                        } else if (params.dataType === 'edge') {
                            return `关系: ${params.data.name}<br/>从: ${params.data.source}<br/>到: ${params.data.target}`;
                        }
                    }
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    data: ['车辆配置', '组件', '测试项目', '关系']
                },
                series: [{
                    name: '知识图谱',
                    type: 'graph',
                    layout: 'force',
                    data: [],
                    links: [],
                    categories: [
                        { name: '车辆配置', itemStyle: { color: '#ff7f0e' } },
                        { name: '组件', itemStyle: { color: '#2ca02c' } },
                        { name: '测试项目', itemStyle: { color: '#1f77b4' } },
                        { name: '关系', itemStyle: { color: '#d62728' } }
                    ],
                    roam: true,
                    focusNodeAdjacency: true,
                    itemStyle: {
                        borderColor: '#fff',
                        borderWidth: 1,
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.3)'
                    },
                    label: {
                        show: true,
                        position: 'right',
                        formatter: '{b}'
                    },
                    lineStyle: {
                        color: 'source',
                        curveness: 0.3
                    },
                    emphasis: {
                        focus: 'adjacency',
                        lineStyle: {
                            width: 10
                        }
                    },
                    force: {
                        repulsion: 1000,
                        gravity: 0.1,
                        edgeLength: 150,
                        layoutAnimation: true
                    }
                }]
            };

            knowledgeGraphChart.setOption(option);

            // 监听窗口大小变化
            window.addEventListener('resize', function() {
                if (knowledgeGraphChart) {
                    knowledgeGraphChart.resize();
                }
            });
        }

        async function loadGraphData() {
            try {
                // 初始化图表容器
                if (!knowledgeGraphChart) {
                    initKnowledgeGraph();
                }

                // 显示加载状态 - 使用ECharts的showLoading方法
                if (knowledgeGraphChart) {
                    knowledgeGraphChart.showLoading({
                        text: '正在加载知识图谱数据...',
                        color: '#4f46e5',
                        textColor: '#666',
                        maskColor: 'rgba(255, 255, 255, 0.8)',
                        zlevel: 0
                    });
                }

                console.log('正在从Neo4j数据库获取知识图谱数据...');

                // 调用后端API获取图谱数据
                const response = await fetch(API_CONFIG.getUrl('GET_KNOWLEDGE_GRAPH'));

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (!result.success) {
                    throw new Error(result.error || '获取图谱数据失败');
                }

                graphData = result.data;
                console.log('知识图谱数据加载成功:', graphData);

                // 隐藏加载状态
                if (knowledgeGraphChart) {
                    knowledgeGraphChart.hideLoading();
                }

                // 处理并渲染图谱数据
                renderGraphData(graphData);

                // 更新元数据统计
                updateGraphStats(graphData);

            } catch (error) {
                console.error('加载图谱数据失败:', error);

                // 隐藏加载状态
                if (knowledgeGraphChart) {
                    knowledgeGraphChart.hideLoading();
                }

                // 显示错误信息在图表中
                if (knowledgeGraphChart) {
                    const errorOption = {
                        title: {
                            text: '加载失败',
                            subtext: error.message,
                            left: 'center',
                            top: 'center',
                            textStyle: {
                                color: '#ef4444',
                                fontSize: 18
                            },
                            subtextStyle: {
                                color: '#666',
                                fontSize: 14
                            }
                        },
                        series: [{
                            data: [],
                            links: []
                        }]
                    };
                    knowledgeGraphChart.setOption(errorOption, true);
                }

                alert(`知识图谱加载失败: ${error.message}\n\n请检查网络连接或重试加载。`);
            }
        }

        function renderGraphData(data) {
            if (!knowledgeGraphChart || !data) {
                console.error('图表未初始化或数据为空');
                return;
            }

            console.log('开始渲染图谱数据:', data);

            // 转换节点数据
            const nodes = data.nodes.map(node => {
                const nodeCategory = node.category || 'Unknown';
                return {
                    id: node.id,
                    name: node.name || node.id,
                    category: getCategoryIndex(nodeCategory),
                    value: node.properties,
                    properties: node.properties,
                    symbolSize: getNodeSize(nodeCategory),
                    itemStyle: {
                        color: getNodeColor(nodeCategory)
                    }
                };
            });

            // 转换关系数据
            const links = data.links.map(rel => ({
                source: rel.source,
                target: rel.target,
                name: rel.relation,
                lineStyle: {
                    color: '#999',
                    width: 2
                }
            }));

            console.log(`处理完成: ${nodes.length}个节点, ${links.length}个关系`);

            // 完整的图表配置
            const option = {
                title: {
                    text: 'Neo4j 知识图谱',
                    left: 'center',
                    textStyle: {
                        color: '#333',
                        fontSize: 18
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        if (params.dataType === 'node') {
                            const categoryNames = ['车辆配置', '组件', '测试项目', '其他'];
                            const categoryName = categoryNames[params.data.category] || '其他';
                            return `<strong>${params.data.name}</strong><br/>类型: ${categoryName}<br/>属性: ${JSON.stringify(params.data.properties || {}, null, 2)}`;
                        } else if (params.dataType === 'edge') {
                            return `关系: ${params.data.name}<br/>从: ${params.data.source}<br/>到: ${params.data.target}`;
                        }
                    }
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    data: ['车辆配置', '组件', '测试项目', '其他']
                },
                series: [{
                    name: '知识图谱',
                    type: 'graph',
                    layout: 'force',
                    data: nodes,
                    links: links,
                    categories: [
                        { name: '车辆配置', itemStyle: { color: '#ff7f0e' } },
                        { name: '组件', itemStyle: { color: '#2ca02c' } },
                        { name: '测试项目', itemStyle: { color: '#1f77b4' } },
                        { name: '其他', itemStyle: { color: '#d62728' } }
                    ],
                    roam: true,
                    focusNodeAdjacency: true,
                    itemStyle: {
                        borderColor: '#fff',
                        borderWidth: 1,
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.3)'
                    },
                    label: {
                        show: true,
                        position: 'right',
                        formatter: '{b}',
                        fontSize: 12
                    },
                    lineStyle: {
                        color: 'source',
                        curveness: 0.3,
                        opacity: 0.7
                    },
                    emphasis: {
                        focus: 'adjacency',
                        lineStyle: {
                            width: 4
                        }
                    },
                    force: {
                        repulsion: 1000,
                        gravity: 0.1,
                        edgeLength: 150,
                        layoutAnimation: true
                    }
                }]
            };

            // 设置图表选项
            knowledgeGraphChart.setOption(option, true);
            console.log('图表渲染完成');
        }

        function getCategoryIndex(label) {
            const categoryMap = {
                'VehicleConfig': 0,
                'Component': 1,
                'TestProject': 2
            };
            return categoryMap[label] || 3;
        }

        function getNodeColor(label) {
            const colorMap = {
                'VehicleConfig': '#ff7f0e',
                'Component': '#2ca02c',
                'TestProject': '#1f77b4'
            };
            return colorMap[label] || '#d62728';
        }

        function getNodeSize(label) {
            const sizeMap = {
                'VehicleConfig': 60,
                'Component': 40,
                'TestProject': 50
            };
            return sizeMap[label] || 30;
        }

        function updateGraphStats(data) {
            if (!data) return;

            const nodeCount = data.nodes.length;
            const relationCount = data.links.length;

            // 统计不同类型的节点数量
            const vehicleCount = data.nodes.filter(n => n.category === 'VehicleConfig').length;
            const componentCount = data.nodes.filter(n => n.category === 'Component').length;
            const projectCount = data.nodes.filter(n => n.category === 'TestProject').length;

            // 更新统计显示
            document.getElementById('nodeCount').textContent = nodeCount;
            document.getElementById('relationCount').textContent = relationCount;
            document.getElementById('vehicleCount').textContent = vehicleCount;
            document.getElementById('componentCount').textContent = componentCount;
            document.getElementById('projectCount').textContent = projectCount;
        }

        function refreshGraph() {
            loadGraphData();
        }

        function resetGraphView() {
            if (knowledgeGraphChart) {
                knowledgeGraphChart.dispatchAction({
                    type: 'restore'
                });
            }
        }

        function filterGraph() {
            const filterValue = document.getElementById('graphFilter').value;

            if (!graphData || !knowledgeGraphChart) return;

            let filteredNodes = graphData.nodes;
            let filteredLinks = graphData.links;

            if (filterValue !== 'all') {
                const labelMap = {
                    'vehicles': 'VehicleConfig',
                    'components': 'Component',
                    'projects': 'TestProject'
                };

                const targetLabel = labelMap[filterValue];
                filteredNodes = graphData.nodes.filter(node => node.category === targetLabel);

                // 过滤相关的关系
                const nodeIds = new Set(filteredNodes.map(n => n.id));
                filteredLinks = graphData.links.filter(rel =>
                    nodeIds.has(rel.source) && nodeIds.has(rel.target)
                );
            }

            // 重新渲染过滤后的数据
            const filteredData = {
                nodes: filteredNodes,
                links: filteredLinks
            };

            renderGraphData(filteredData);
            updateGraphStats(filteredData);
        }

        // 点击弹窗外部关闭弹窗
        window.onclick = function(event) {
            const modal = document.getElementById('graphModal');
            if (event.target === modal) {
                closeKnowledgeGraph();
            }
        }
    </script>
</body>
</html>