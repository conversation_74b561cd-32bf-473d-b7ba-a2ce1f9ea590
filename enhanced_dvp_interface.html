<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DVP智能生成系统 - 可视化增强版</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .main-container {
            display: flex;
            height: 100vh;
            background: white;
            margin: 20px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        /* 左侧输入区域 */
        .input-panel {
            width: 35%;
            background: #f8fafc;
            border-right: 2px solid #e5e7eb;
            display: flex;
            flex-direction: column;
        }

        .input-header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .input-header h1 {
            font-size: 20px;
            margin-bottom: 5px;
        }

        .input-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            font-weight: bold;
            color: #374151;
            margin-bottom: 8px;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4f46e5;
        }

        .components-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-top: 10px;
        }

        .component-item {
            display: flex;
            align-items: center;
            padding: 8px;
            background: white;
            border-radius: 6px;
            border: 2px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 12px;
        }

        .component-item:hover {
            border-color: #4f46e5;
            background: #f0f9ff;
        }

        .component-item.selected {
            border-color: #4f46e5;
            background: #eff6ff;
        }

        .component-item input[type="checkbox"] {
            width: auto;
            margin-right: 6px;
        }

        .generate-btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: bold;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
            margin-top: 20px;
        }

        .generate-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 8px 16px rgba(79, 70, 229, 0.3);
        }

        .generate-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
        }

        /* 右侧输出区域 */
        .output-panel {
            width: 65%;
            display: flex;
            flex-direction: column;
            background: white;
        }

        .output-header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .output-header h1 {
            font-size: 20px;
        }

        .export-buttons {
            display: flex;
            gap: 10px;
        }

        .export-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 14px;
        }

        .export-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .output-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #fafafa;
        }

        /* 数字卡片 */
        .metrics-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-left: 4px solid #4f46e5;
            transition: transform 0.3s;
        }

        .metric-card:hover {
            transform: translateY(-2px);
        }

        .metric-card h3 {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-card .value {
            font-size: 32px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .metric-card .unit {
            color: #6b7280;
            font-size: 14px;
        }

        .metric-card.vehicles { border-left-color: #ef4444; }
        .metric-card.duration { border-left-color: #f59e0b; }
        .metric-card.projects { border-left-color: #8b5cf6; }

        /* 图表容器 */
        .charts-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .chart-container h3 {
            color: #1f2937;
            margin-bottom: 15px;
            font-size: 16px;
        }

        .chart-canvas {
            width: 100% !important;
            height: 250px !important;
        }

        /* 甘特图 */
        .gantt-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .gantt-section h3 {
            color: #1f2937;
            margin-bottom: 20px;
            font-size: 18px;
        }

        .gantt-container {
            overflow-x: auto;
            min-height: 400px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: white;
        }

        .gantt-timeline {
            position: relative;
            min-width: 900px;
            background: white;
        }

        .gantt-header {
            height: 50px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 2px solid #cbd5e1;
            display: flex;
            align-items: center;
            padding: 0 20px;
            font-weight: bold;
            color: #1e293b;
            font-size: 16px;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .gantt-row {
            height: 45px;
            border-bottom: 1px solid #f1f5f9;
            position: relative;
            display: flex;
            align-items: center;
            padding: 0;
            transition: background-color 0.2s;
        }

        .gantt-row:hover {
            background-color: #f8fafc;
        }

        .gantt-row:nth-child(even) {
            background-color: #fafbfc;
        }

        .gantt-project-label {
            position: absolute;
            left: 15px;
            font-size: 13px;
            font-weight: 600;
            color: #1e293b;
            width: 180px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            z-index: 5;
        }

        .gantt-bar {
            position: absolute;
            height: 28px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 11px;
            font-weight: bold;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            border: 1px solid rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            min-width: 60px;
            margin-left: 200px;
        }

        .gantt-bar:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .gantt-bar.高 {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            border-left: 4px solid #b91c1c;
        }

        .gantt-bar.中 {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            border-left: 4px solid #b45309;
        }

        .gantt-bar.低 {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-left: 4px solid #047857;
        }

        .gantt-bar-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            width: 100%;
        }

        .gantt-bar-duration {
            font-size: 12px;
            font-weight: bold;
        }

        .gantt-bar-vehicles {
            font-size: 10px;
            opacity: 0.9;
            margin-top: 1px;
        }

        /* 甘特图时间轴标尺 */
        .gantt-time-scale {
            height: 30px;
            background: #f1f5f9;
            border-bottom: 1px solid #cbd5e1;
            display: flex;
            align-items: center;
            font-size: 11px;
            color: #64748b;
            position: relative;
        }

        .gantt-time-marker {
            position: absolute;
            height: 100%;
            border-left: 1px solid #cbd5e1;
            display: flex;
            align-items: center;
            padding-left: 5px;
        }

        /* 项目详情表格 */
        .projects-table {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow-x: auto;
        }

        .projects-table h3 {
            color: #1f2937;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .projects-table table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .projects-table th,
        .projects-table td {
            text-align: left;
            padding: 12px;
            border-bottom: 1px solid #e5e7eb;
        }

        .projects-table th {
            background: #f9fafb;
            font-weight: bold;
            color: #374151;
        }

        .complexity-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .complexity-badge.high { background: #fef2f2; color: #dc2626; }
        .complexity-badge.medium { background: #fffbeb; color: #d97706; }
        .complexity-badge.low { background: #f0fdf4; color: #16a34a; }

        /* 加载状态 */
        .loading-state {
            display: none;
            padding: 20px;
            color: #6b7280;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #4f46e5;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 进度轨道图样式 */
        .progress-pipeline {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .pipeline-header {
            text-align: center;
            margin-bottom: 24px;
        }

        .pipeline-header h3 {
            color: white;
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: bold;
        }

        .pipeline-subtitle {
            color: rgba(255,255,255,0.8);
            font-size: 14px;
        }

        .pipeline-track {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 8px;
        }

        .pipeline-stage {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;
            padding: 16px 12px;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .pipeline-stage.active {
            background: rgba(255,255,255,0.2);
            border-color: #fbbf24;
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(251,191,36,0.3);
        }

        .pipeline-stage.completed {
            background: rgba(34,197,94,0.2);
            border-color: #22c55e;
        }

        .pipeline-stage.error {
            background: rgba(239,68,68,0.2);
            border-color: #ef4444;
        }

        .stage-icon {
            font-size: 24px;
            margin-bottom: 8px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        }

        .stage-label {
            color: white;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 4px;
            line-height: 1.2;
        }

        .stage-status {
            color: rgba(255,255,255,0.7);
            font-size: 10px;
            text-align: center;
        }

        .pipeline-stage.active .stage-status {
            color: #fbbf24;
            font-weight: bold;
        }

        .pipeline-stage.completed .stage-status {
            color: #22c55e;
            font-weight: bold;
        }

        .pipeline-stage.error .stage-status {
            color: #ef4444;
            font-weight: bold;
        }

        .pipeline-connector {
            flex: 1;
            height: 3px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
            position: relative;
            margin: 0 8px;
            min-width: 20px;
        }

        .pipeline-connector.active {
            background: linear-gradient(90deg, #22c55e 0%, #fbbf24 100%);
            animation: flow 2s ease-in-out infinite;
        }

        @keyframes flow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 终端日志背景样式 */
        .terminal-background {
            background: rgba(0,0,0,0.9);
            border-radius: 12px;
            overflow: hidden;
            margin-top: 20px;
            min-height: 400px;
            position: relative;
        }

        .terminal-header {
            background: #1f2937;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #374151;
        }

        .terminal-title {
            color: #10b981;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            font-weight: bold;
        }

        .terminal-time {
            color: #6b7280;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .terminal-content {
            padding: 16px;
            height: 350px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            background: rgba(0,0,0,0.3);
            text-align: left;
        }

        .log-line {
            color: #10b981;
            margin-bottom: 4px;
            opacity: 0.8;
            animation: fadeIn 0.5s ease-in;
            text-align: left;
            word-wrap: break-word;
        }

        .log-line.error {
            color: #ef4444;
        }

        .log-line.warning {
            color: #f59e0b;
        }

        .log-line.info {
            color: #3b82f6;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 0.8; transform: translateY(0); }
        }

        .loading-content {
            text-align: center;
            padding: 20px;
            background: rgba(255,255,255,0.95);
            border-radius: 12px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        .loading-text {
            color: #374151;
            font-size: 16px;
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .pipeline-track {
                flex-direction: column;
            }

            .pipeline-connector {
                width: 3px;
                height: 20px;
                margin: 8px 0;
            }

            .pipeline-stage {
                min-width: 200px;
            }
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }

        .empty-state svg {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        /* 知识图谱弹窗样式 */
        .graph-modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .graph-modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 1200px;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .graph-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .graph-modal-header h2 {
            margin: 0;
            font-size: 24px;
        }

        .graph-modal-close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: opacity 0.3s ease;
        }

        .graph-modal-close:hover {
            opacity: 0.7;
        }

        .graph-modal-body {
            padding: 20px;
            max-height: calc(90vh - 80px);
            overflow-y: auto;
        }

        .graph-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            align-items: center;
            flex-wrap: wrap;
        }

        .graph-btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .graph-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }

        #graphFilter {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            font-size: 14px;
        }

        .graph-metadata {
            margin-top: 20px;
            background: #f9fafb;
            padding: 15px;
            border-radius: 8px;
        }

        .graph-metadata h3 {
            margin: 0 0 15px 0;
            color: #374151;
            font-size: 16px;
        }

        .graph-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
            padding: 10px 15px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .stat-label {
            font-size: 14px;
            color: #6b7280;
        }

        .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .input-panel { width: 40%; }
            .output-panel { width: 60%; }
            .charts-section { grid-template-columns: 1fr; }
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
                margin: 10px;
            }
            .input-panel, .output-panel {
                width: 100%;
            }
            .input-panel {
                height: 50vh;
            }
            .components-grid {
                grid-template-columns: 1fr;
            }
            .graph-modal-content {
                width: 95%;
                margin: 5% auto;
            }
            .graph-controls {
                flex-direction: column;
                align-items: stretch;
            }
            .graph-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 左侧输入面板 -->
        <div class="input-panel">
            <div class="input-header">
                <h1>🚗 DVP智能生成</h1>
                <p>配置输入与参数设置</p>
            </div>

            <div class="input-content">
                <div class="form-group">
                    <label>配置类型</label>
                    <select id="configType">
                        <option value="ADAS基础配置">ADAS基础配置</option>
                        <option value="ADAS L2级配置">ADAS L2级配置</option>
                        <option value="ADAS L2+配置" selected>ADAS L2+配置</option>
                        <option value="高端智能驾驶配置">高端智能驾驶配置</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>DVP开始日期</label>
                    <input type="date" id="startDate" readonly>
                    <small style="color: #6b7280; font-size: 12px;">当前系统日期（自动设置）</small>
                </div>

                <div class="form-group">
                    <label>SOP量产日期</label>
                    <input type="date" id="sopDate">
                    <small style="color: #6b7280; font-size: 12px;">默认为开始日期+6个月</small>
                </div>

                <div class="form-group">
                    <label>车辆组件配置</label>
                    <div class="components-grid" id="componentsGrid">
                        <!-- 组件列表将通过API动态加载 -->
                        <div class="loading-components" style="text-align: center; padding: 20px; color: #6b7280;">
                            🔄 正在从Neo4j数据库加载配件信息...
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>项目类型</label>
                    <select id="complexity">
                        <option value="standard" selected>次发项目</option>
                        <option value="comprehensive">首发项目</option>
                    </select>
                    <small style="color: #6b7280; font-size: 12px;">首发项目需要更多车辆和测试周期</small>
                </div>

                <div class="form-group">
                    <label>零件变化程度</label>
                    <select id="environment">
                        <option value="normal" selected>单一变化 (P/M策略)</option>
                        <option value="extreme">多重变化 (X/U策略)</option>
                    </select>
                    <small style="color: #6b7280; font-size: 12px;">P-完全沿用，M-修改沿用，X/U-全新开发</small>
                </div>

                <div class="form-group">
                    <label>资源优先级</label>
                    <select id="priority">
                        <option value="normal" selected>标准资源</option>
                        <option value="urgent">优先资源 (+20%)</option>
                        <option value="low">节约资源 (-10%)</option>
                    </select>
                    <small style="color: #6b7280; font-size: 12px;">影响车辆资源分配和排期优先级</small>
                </div>

                <button class="generate-btn" onclick="generateDVP()">
                    🚀 生成DVP方案
                </button>

                <!-- 测试按钮 -->
                <button class="btn" onclick="testTerminalLogs()" style="width: 100%; margin-top: 10px; background: #10b981; color: white; border: none; padding: 8px; border-radius: 6px; font-size: 12px; cursor: pointer;">
                    🖥️ 测试终端日志
                </button>

                <button class="btn btn-secondary" onclick="testConnection()" style="width: 100%; margin-top: 10px;">
                    🔗 测试API连接
                </button>

                <button class="btn btn-graph" onclick="showKnowledgeGraph()" style="width: 100%; margin-top: 10px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px; border-radius: 8px; font-weight: bold; cursor: pointer; transition: all 0.3s ease;">
                    🕸️ 查看图谱
                </button>

                <!-- 数据加载状态指示器 -->
                <div id="dataLoadingStatus" style="margin-top: 15px; padding: 10px; background: #f8fafc; border-radius: 6px; border: 1px solid #e5e7eb;">
                    <div style="font-size: 12px; color: #6b7280; margin-bottom: 5px;">数据加载状态:</div>
                    <div id="componentsStatus" style="font-size: 11px; color: #6b7280;">配件数据: 加载中...</div>
                    <div id="projectsStatus" style="font-size: 11px; color: #6b7280;">测试项目: 加载中...</div>
                </div>
            </div>
        </div>

        <!-- 右侧输出面板 -->
        <div class="output-panel">
            <div class="output-header">
                <h1>📊 DVP方案可视化</h1>
                <div class="export-buttons">
                    <button class="export-btn" onclick="exportMarkdown()">📄 导出Markdown</button>
                    <button class="export-btn" onclick="exportJSON()">📋 导出JSON</button>
                </div>
            </div>

            <div class="output-content">
                <!-- 空状态 -->
                <div class="empty-state" id="emptyState">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M9 2a1 1 0 000 2h6a1 1 0 100-2H9z"/>
                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h12a2 2 0 012 2v14a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 2v12h10V7H7z"/>
                    </svg>
                    <h3>等待配置输入</h3>
                    <p>请在左侧配置车型参数，然后点击生成DVP方案</p>
                </div>

                <!-- 加载状态 - 真实进度追踪 -->
                <div class="loading-state" id="loadingState">
                    <!-- 进度轨道图 -->
                    <div class="progress-pipeline">
                        <div class="pipeline-header">
                            <h3>🚀 DVP智能生成流水线</h3>
                            <div class="pipeline-subtitle">AI驱动的汽车设计验证计划生成 - 实时状态监控</div>
                        </div>
                        <div class="pipeline-track">
                            <div class="pipeline-stage" data-stage="init">
                                <div class="stage-icon">🔧</div>
                                <div class="stage-label">初始化配置</div>
                                <div class="stage-status">待处理</div>
                            </div>
                            <div class="pipeline-connector"></div>
                            <div class="pipeline-stage" data-stage="api">
                                <div class="stage-icon">🌐</div>
                                <div class="stage-label">API连接</div>
                                <div class="stage-status">待处理</div>
                            </div>
                            <div class="pipeline-connector"></div>
                            <div class="pipeline-stage" data-stage="neo4j">
                                <div class="stage-icon">🗄️</div>
                                <div class="stage-label">Neo4j知识图谱</div>
                                <div class="stage-status">待处理</div>
                            </div>
                            <div class="pipeline-connector"></div>
                            <div class="pipeline-stage" data-stage="llm">
                                <div class="stage-icon">🧠</div>
                                <div class="stage-label">DeepSeek-R1大模型</div>
                                <div class="stage-status">待处理</div>
                            </div>
                            <div class="pipeline-connector"></div>
                            <div class="pipeline-stage" data-stage="calculation">
                                <div class="stage-icon">📊</div>
                                <div class="stage-label">车辆计算</div>
                                <div class="stage-status">待处理</div>
                            </div>
                            <div class="pipeline-connector"></div>
                            <div class="pipeline-stage" data-stage="complete">
                                <div class="stage-icon">✅</div>
                                <div class="stage-label">方案完成</div>
                                <div class="stage-status">待处理</div>
                            </div>
                        </div>
                    </div>

                    <!-- 终端日志背景 -->
                    <div class="terminal-background">
                        <div class="terminal-header">
                            <div class="terminal-title">🖥️ DVP-AI 系统实时日志</div>
                            <div class="terminal-time" id="terminalTime"></div>
                        </div>
                        <div class="terminal-content" id="terminalLogs">
                            <div class="log-line">系统初始化完成...</div>
                            <div class="log-line">等待用户请求...</div>
                        </div>
                    </div>
                </div>

                <!-- 结果展示 -->
                <div id="resultsContainer" style="display: none;">
                    <!-- 关键指标卡片 -->
                    <div class="metrics-cards">
                        <div class="metric-card vehicles">
                            <h3>所需车辆</h3>
                            <div class="value" id="vehiclesCount">0</div>
                            <div class="unit">台</div>
                        </div>
                        <div class="metric-card duration">
                            <h3>测试周期</h3>
                            <div class="value" id="testDuration">0</div>
                            <div class="unit">天</div>
                        </div>
                        <div class="metric-card projects">
                            <h3>匹配项目</h3>
                            <div class="value" id="projectsCount">0</div>
                            <div class="unit">个</div>
                        </div>
                    </div>

                    <!-- 图表区域 -->
                    <div class="charts-section">
                        <div class="chart-container">
                            <h3>📊 项目复杂度分布</h3>
                            <canvas id="complexityChart" class="chart-canvas"></canvas>
                        </div>
                    </div>

                    <!-- 甘特图 -->
                    <div class="gantt-section">
                        <h3>📅 项目时间轴甘特图</h3>
                        <div class="gantt-container">
                            <div class="gantt-timeline" id="ganttChart">
                                <div class="gantt-header">项目排期时间轴</div>
                                <div id="ganttContent"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 项目详情表格 -->
                    <div class="projects-table">
                        <h3>📋 匹配项目详情</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>项目名称</th>
                                    <th>匹配度</th>
                                    <th>复杂度</th>
                                    <th>测试周期</th>
                                    <th>AI计算车辆</th>
                                    <th>匹配组件</th>
                                </tr>
                            </thead>
                            <tbody id="projectsTableBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 知识图谱弹窗 -->
    <div id="graphModal" class="graph-modal" style="display: none;">
        <div class="graph-modal-content">
            <div class="graph-modal-header">
                <h2>🕸️ Neo4j 知识图谱</h2>
                <span class="graph-modal-close" onclick="closeKnowledgeGraph()">&times;</span>
            </div>
            <div class="graph-modal-body">
                <div class="graph-controls">
                    <button onclick="refreshGraph()" class="graph-btn">🔄 刷新图谱</button>
                    <button onclick="resetGraphView()" class="graph-btn">🎯 重置视图</button>
                    <select id="graphFilter" onchange="filterGraph()">
                        <option value="all">显示全部</option>
                        <option value="vehicles">车辆配置</option>
                        <option value="components">组件</option>
                        <option value="projects">测试项目</option>
                    </select>
                </div>
                <div id="graphContainer" style="width: 100%; height: 500px; border: 1px solid #e5e7eb; border-radius: 8px;"></div>
                <div class="graph-metadata">
                    <h3>📊 图谱元数据</h3>
                    <div id="graphStats" class="graph-stats">
                        <div class="stat-item">
                            <span class="stat-label">节点总数:</span>
                            <span id="nodeCount" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">关系总数:</span>
                            <span id="relationCount" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">车辆配置:</span>
                            <span id="vehicleCount" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">组件数量:</span>
                            <span id="componentCount" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">测试项目:</span>
                            <span id="projectCount" class="stat-value">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script>
        // API配置 - 统一管理所有API端点
        const API_CONFIG = {
            BASE_URL: 'http://localhost:5002',
            ENDPOINTS: {
                GENERATE_DVP: '/api/generate-dvp',
                GET_COMPONENTS: '/api/get-components',
                GET_TEST_PROJECTS: '/api/get-test-projects',
                TEST_CONNECTION: '/api/test-connection',
                GET_KNOWLEDGE_GRAPH: '/api/get-knowledge-graph'
            },
            // 获取完整API URL的辅助方法
            getUrl: function(endpoint) {
                return this.BASE_URL + this.ENDPOINTS[endpoint];
            }
        };

        // 全局变量
        let currentDVPData = null;
        let complexityChart = null;
        let currentTheme = 'light';
        let testProjectsData = null;
        let knowledgeGraphChart = null;
        let graphData = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            loadComponentsFromAPI();
            loadTestProjectsFromAPI();
            presetDefaultConfiguration();
            initTheme();
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 配置类型变化时自动选择组件
            document.getElementById('configType').addEventListener('change', function() {
                autoSelectComponents(this.value);
            });
        }

        // 预设默认配置
        function presetDefaultConfiguration() {
            // 延迟执行，等待组件加载完成
            setTimeout(() => {
                autoSelectComponents('ADAS L2+配置');
            }, 1000);
            setDefaultDates();
        }

        // 设置默认日期
        function setDefaultDates() {
            const today = new Date();
            const startDate = new Date(today);
            const sopDate = new Date(today);

            // SOP日期设为当前日期+6个月
            sopDate.setMonth(sopDate.getMonth() + 6);

            // 格式化为YYYY-MM-DD
            const formatDate = (date) => {
                return date.toISOString().split('T')[0];
            };

            document.getElementById('startDate').value = formatDate(startDate);
            document.getElementById('sopDate').value = formatDate(sopDate);
        }

        // 根据配置类型自动选择组件
        function autoSelectComponents(configType) {
            // 清除所有选择
            document.querySelectorAll('.component-item').forEach(item => {
                const checkbox = item.querySelector('input[type="checkbox"]');
                checkbox.checked = false;
                item.classList.remove('selected');
            });

            let componentsToSelect = [];

            switch(configType) {
                case 'ADAS基础配置':
                    componentsToSelect = ['AD摄像头前视/后视/侧视', 'EPS'];
                    break;
                case 'ADAS L2级配置':
                    componentsToSelect = ['AD摄像头前视/后视/侧视', '毫米波雷达', 'EPS', 'DPB+ESP/IPB'];
                    break;
                case 'ADAS L2+配置':
                    componentsToSelect = ['AD摄像头前视/后视/侧视', '毫米波雷达', '激光雷达', 'EPS', 'DPB+ESP/IPB', '环视摄像头'];
                    break;
                case '高端智能驾驶配置':
                    componentsToSelect = ['FSD变更', '激光雷达', '毫米波雷达', '超声波雷达', 'AD摄像头前视/后视/侧视', '环视摄像头', 'DPB+ESP/IPB', 'EPS', '后轮转向', '悬架'];
                    break;
            }

            // 选择对应组件
            componentsToSelect.forEach(component => {
                const checkbox = document.querySelector(`input[value="${component}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                    checkbox.closest('.component-item').classList.add('selected');
                }
            });
        }

        // 生成DVP方案 - 真实进度追踪版本
        async function generateDVP() {
            // 获取用户输入
            const configType = document.getElementById('configType').value;
            const startDate = document.getElementById('startDate').value;
            const sopDate = document.getElementById('sopDate').value;
            const complexity = document.getElementById('complexity').value;
            const environment = document.getElementById('environment').value;
            const priority = document.getElementById('priority').value;

            // 获取选中的组件
            const selectedComponents = [];
            document.querySelectorAll('.component-item input:checked').forEach(checkbox => {
                selectedComponents.push(checkbox.value);
            });

            if (selectedComponents.length === 0) {
                alert('请至少选择一个车辆组件！');
                return;
            }

            // 显示加载状态并重置进度
            showLoading();
            resetRealPipeline();

            // 初始化终端时间
            updateTerminalTime();
            const timeInterval = setInterval(updateTerminalTime, 1000);

            try {
                // 阶段1: 初始化配置
                updatePipelineStage('init', 'active');
                addRealTerminalLog('🚀 DVP生成流程启动');
                addRealTerminalLog(`📋 配置类型: ${configType}`);
                addRealTerminalLog(`🔧 选中组件: ${selectedComponents.join(', ')}`);
                addRealTerminalLog(`📅 时间窗口: ${startDate} → ${sopDate}`);
                await new Promise(resolve => setTimeout(resolve, 800));
                updatePipelineStage('init', 'completed');
                addRealTerminalLog('✅ 配置验证完成');

                // 阶段2: API连接
                updatePipelineStage('api', 'active');
                addRealTerminalLog('🌐 正在连接后端API服务器...');
                addRealTerminalLog(`📡 目标地址: ${API_CONFIG.BASE_URL}`);

                const configData = {
                    configType,
                    startDate,
                    sopDate,
                    components: selectedComponents,
                    complexity,
                    environment,
                    priority,
                    testProjectsData: testProjectsData
                };

                const response = await fetch(API_CONFIG.getUrl('GENERATE_DVP'), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(configData)
                });

                if (!response.ok) {
                    updatePipelineStage('api', 'error', `HTTP ${response.status}`);
                    addRealTerminalLog(`❌ API连接失败: HTTP ${response.status}`, 'error');
                    throw new Error(`API调用失败: ${response.status}`);
                }

                updatePipelineStage('api', 'completed');
                addRealTerminalLog('✅ API连接成功');
                addRealTerminalLog('📤 配置数据已发送');

                // 阶段3: Neo4j知识图谱查询
                updatePipelineStage('neo4j', 'active');
                addRealTerminalLog('🗄️ 正在查询Neo4j知识图谱...');
                addRealTerminalLog('🔍 匹配测试项目与组件关系');

                // 模拟Neo4j查询时间
                await new Promise(resolve => setTimeout(resolve, 1500));

                const result = await response.json();

                if (!result.success) {
                    updatePipelineStage('neo4j', 'error', '查询失败');
                    addRealTerminalLog(`❌ Neo4j查询失败: ${result.error}`, 'error');
                    throw new Error(result.error || '知识图谱查询失败');
                }

                updatePipelineStage('neo4j', 'completed');
                addRealTerminalLog(`✅ 知识图谱查询完成`);
                addRealTerminalLog(`📊 匹配到 ${result.data.matchedProjects?.length || 0} 个测试项目`);

                // 阶段4: DeepSeek-R1大模型推理
                updatePipelineStage('llm', 'active');
                addRealTerminalLog('🧠 正在调用DeepSeek-R1大模型...');
                addRealTerminalLog('📝 构建DVP推理提示词');
                addRealTerminalLog('⚡ 执行AI智能推理计算');

                // 模拟LLM推理时间
                await new Promise(resolve => setTimeout(resolve, 2000));

                updatePipelineStage('llm', 'completed');
                addRealTerminalLog('✅ DeepSeek-R1推理完成');
                addRealTerminalLog(`🚗 AI推荐车辆数量: ${result.data.summary?.totalVehicles || 0} 台`);

                // 阶段5: 车辆计算与排期生成
                updatePipelineStage('calculation', 'active');
                addRealTerminalLog('📊 正在进行车辆资源计算...');
                addRealTerminalLog('📅 生成智能测试排期');
                addRealTerminalLog('⚖️ 优化资源分配策略');

                await new Promise(resolve => setTimeout(resolve, 1200));

                updatePipelineStage('calculation', 'completed');
                addRealTerminalLog('✅ 车辆计算完成');
                addRealTerminalLog(`⏰ 总测试周期: ${result.data.summary?.totalDuration || 0} 天`);

                // 阶段6: 方案完成
                updatePipelineStage('complete', 'active');
                addRealTerminalLog('📋 正在汇总DVP报告...');
                addRealTerminalLog('💾 保存生成结果');

                await new Promise(resolve => setTimeout(resolve, 500));

                updatePipelineStage('complete', 'completed');
                addRealTerminalLog('🎉 DVP方案生成完成！');
                addRealTerminalLog(`📄 报告ID: ${result.data.id}`);

                // 显示结果
                updateVisualization(result.data);
                currentDVPData = result.data;

                setTimeout(() => {
                    clearInterval(timeInterval);
                    hideLoading();
                    showResults();
                }, 1000);

            } catch (error) {
                console.error('DVP生成失败:', error);

                // 更新当前阶段为错误状态
                const currentStage = document.querySelector('.pipeline-stage.active');
                if (currentStage) {
                    const stageId = currentStage.getAttribute('data-stage');
                    updatePipelineStage(stageId, 'error', '处理失败');
                }

                addRealTerminalLog(`💥 系统错误: ${error.message}`, 'error');
                addRealTerminalLog('🔄 请检查网络连接或联系技术支持', 'warning');

                setTimeout(() => {
                    clearInterval(timeInterval);
                    hideLoading();
                    alert(`DVP生成失败: ${error.message}\n\n请检查网络连接或联系技术支持。`);
                }, 3000);
            }
        }

        // 真实进度追踪系统
        const realPipelineStages = [
            { id: 'init', name: '初始化配置' },
            { id: 'api', name: 'API连接' },
            { id: 'neo4j', name: 'Neo4j知识图谱' },
            { id: 'llm', name: 'DeepSeek-R1大模型' },
            { id: 'calculation', name: '车辆计算' },
            { id: 'complete', name: '方案完成' }
        ];

        // 更新进度阶段状态
        function updatePipelineStage(stageId, status, message = '') {
            const stage = document.querySelector(`[data-stage="${stageId}"]`);
            if (!stage) return;

            // 移除所有状态类
            stage.classList.remove('active', 'completed', 'error');

            // 添加新状态类
            if (status !== 'pending') {
                stage.classList.add(status);
            }

            // 更新状态文本
            const statusElement = stage.querySelector('.stage-status');
            const statusTexts = {
                'pending': '待处理',
                'active': '处理中...',
                'completed': '已完成',
                'error': '失败'
            };

            statusElement.textContent = message || statusTexts[status] || '待处理';

            // 更新连接器状态
            updateConnectors(stageId, status);
        }

        // 更新连接器状态
        function updateConnectors(currentStageId, status) {
            const currentIndex = realPipelineStages.findIndex(s => s.id === currentStageId);
            if (currentIndex === -1) return;

            const connectors = document.querySelectorAll('.pipeline-connector');

            if (status === 'completed' && currentIndex < connectors.length) {
                connectors[currentIndex].classList.add('active');
            }
        }

        // 添加终端日志（兼容旧版本，实际调用真实日志函数）
        function addTerminalLog(message, type = 'info') {
            addRealTerminalLog(message, type);
        }

        // 更新终端时间
        function updateTerminalTime() {
            const terminalTime = document.getElementById('terminalTime');
            if (terminalTime) {
                terminalTime.textContent = new Date().toLocaleString();
            }
        }

        // 注意：已移除模拟函数，现在使用真实的进度追踪

        // 真实终端日志函数
        function addRealTerminalLog(message, type = 'info') {
            const terminalLogs = document.getElementById('terminalLogs');
            if (!terminalLogs) return;

            const logLine = document.createElement('div');
            logLine.className = `log-line ${type}`;

            const timestamp = new Date().toLocaleTimeString();
            logLine.textContent = `[${timestamp}] ${message}`;

            terminalLogs.appendChild(logLine);

            // 自动滚动到底部
            terminalLogs.scrollTop = terminalLogs.scrollHeight;

            // 限制日志行数
            const logLines = terminalLogs.querySelectorAll('.log-line');
            if (logLines.length > 100) {
                logLines[0].remove();
            }
        }

        // 重置真实进度追踪系统
        function resetRealPipeline() {
            realPipelineStages.forEach(stage => {
                updatePipelineStage(stage.id, 'pending');
            });

            // 清除连接器状态
            document.querySelectorAll('.pipeline-connector').forEach(connector => {
                connector.classList.remove('active');
            });

            // 清空终端日志
            const terminalLogs = document.getElementById('terminalLogs');
            if (terminalLogs) {
                terminalLogs.innerHTML = `
                    <div class="log-line">系统初始化完成...</div>
                    <div class="log-line">等待用户请求...</div>
                `;
            }
        }

        // 兼容旧的函数名
        function resetPipeline() {
            resetRealPipeline();
        }

        // 已移除本地硬编码数据生成函数
        // 所有DVP数据现在都通过后端API从Neo4j数据库动态获取

        // 已移除本地硬编码知识图谱函数
        // 所有项目匹配逻辑现在都通过后端API从Neo4j数据库动态查询

        // 已移除本地硬编码计算函数
        // 所有车辆计算和排期生成逻辑现在都通过后端API和LLM智能推理完成

        // 从API动态加载车辆组件列表
        async function loadComponentsFromAPI() {
            const grid = document.getElementById('componentsGrid');

            try {
                // 更新加载状态
                grid.innerHTML = `
                    <div class="loading-components" style="text-align: center; padding: 20px; color: #6b7280;">
                        <div style="margin-bottom: 10px;">🔄 正在从Neo4j数据库加载配件信息...</div>
                        <div style="font-size: 12px; color: #9ca3af;">连接到知识图谱数据库中</div>
                    </div>
                `;

                const response = await fetch(API_CONFIG.getUrl('GET_COMPONENTS'));
                const result = await response.json();

                if (result.success && result.components) {
                    // 显示成功加载信息
                    grid.innerHTML = `
                        <div style="text-align: center; padding: 10px; color: #10b981; font-size: 12px;">
                            ✅ 成功从Neo4j数据库加载 ${result.components.length} 个配件
                        </div>
                    `;

                    // 更新状态指示器
                    updateDataLoadingStatus('components', true, result.components.length);

                    // 延迟一秒后渲染组件
                    setTimeout(() => {
                        renderComponents(result.components);
                    }, 1000);
                } else {
                    // 如果API失败，显示错误信息
                    renderComponentsError('无法从Neo4j数据库加载配件列表');
                    updateDataLoadingStatus('components', false);
                }
            } catch (error) {
                console.error('加载组件列表失败:', error);
                renderComponentsError('连接到后端服务失败');
                updateDataLoadingStatus('components', false);
            }
        }

        // 渲染组件列表
        function renderComponents(components) {
            const grid = document.getElementById('componentsGrid');
            grid.innerHTML = '';

            components.forEach((component, index) => {
                const item = document.createElement('div');
                item.className = 'component-item';
                item.innerHTML = `
                    <input type="checkbox" id="comp${index + 1}" value="${component}">
                    <label for="comp${index + 1}">${component}</label>
                `;
                grid.appendChild(item);
            });

            // 重新设置事件监听器
            setupComponentEventListeners();
        }

        // 渲染组件加载错误
        function renderComponentsError(errorMessage) {
            const grid = document.getElementById('componentsGrid');
            grid.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #ef4444;">
                    ❌ ${errorMessage}
                    <br>
                    <button onclick="loadComponentsFromAPI()" style="margin-top: 10px; padding: 5px 10px; background: #4f46e5; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        🔄 重新加载
                    </button>
                </div>
            `;
        }

        // 从API加载测试项目信息
        async function loadTestProjectsFromAPI() {
            try {
                console.log('正在从Neo4j数据库加载测试项目信息...');

                const response = await fetch(API_CONFIG.getUrl('GET_TEST_PROJECTS'));
                const result = await response.json();

                if (result.success && result.projects) {
                    testProjectsData = result.projects;
                    console.log(`✅ 成功从Neo4j数据库加载 ${result.projects.length} 个测试项目`);
                    updateDataLoadingStatus('projects', true, result.projects.length);
                } else {
                    console.warn('无法从Neo4j数据库加载测试项目信息');
                    testProjectsData = [];
                    updateDataLoadingStatus('projects', false);
                }
            } catch (error) {
                console.error('加载测试项目信息失败:', error);
                testProjectsData = [];
                updateDataLoadingStatus('projects', false);
            }
        }

        // 更新数据加载状态指示器
        function updateDataLoadingStatus(dataType, success, count = 0) {
            const statusElement = document.getElementById(dataType === 'components' ? 'componentsStatus' : 'projectsStatus');
            if (statusElement) {
                const label = dataType === 'components' ? '配件数据' : '测试项目';
                if (success) {
                    statusElement.innerHTML = `${label}: <span style="color: #10b981;">✅ 已加载 ${count} 个</span>`;
                } else {
                    statusElement.innerHTML = `${label}: <span style="color: #ef4444;">❌ 加载失败</span>`;
                }
            }
        }

        // 设置组件事件监听器
        function setupComponentEventListeners() {
            document.querySelectorAll('.component-item').forEach(item => {
                item.addEventListener('click', function() {
                    const checkbox = this.querySelector('input[type="checkbox"]');
                    if (checkbox) {
                        checkbox.checked = !checkbox.checked;
                        this.classList.toggle('selected', checkbox.checked);
                    }
                });
            });
        }

        // 更新可视化显示
        function updateVisualization(data) {
            // 更新数字卡片
            updateMetricCards(data);

            // 更新图表
            updateCharts(data);

            // 更新甘特图
            updateGanttChart(data);

            // 更新项目表格
            updateProjectsTable(data);
        }

        // 更新数字卡片
        function updateMetricCards(data) {
            document.getElementById('vehiclesCount').textContent = data.summary.totalVehicles;
            document.getElementById('testDuration').textContent = data.summary.totalDuration;
            document.getElementById('projectsCount').textContent = data.summary.totalProjects;
        }

        // 更新图表
        function updateCharts(data) {
            updateComplexityChart(data);
        }

        // 更新复杂度分布图表
        function updateComplexityChart(data) {
            const ctx = document.getElementById('complexityChart').getContext('2d');

            if (complexityChart) {
                complexityChart.destroy();
            }

            const complexityData = data.matchedProjects.reduce((acc, project) => {
                acc[project.complexity] = (acc[project.complexity] || 0) + 1;
                return acc;
            }, {});

            complexityChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['高复杂度', '中复杂度', '低复杂度'],
                    datasets: [{
                        data: [
                            complexityData['高'] || 0,
                            complexityData['中'] || 0,
                            complexityData['低'] || 0
                        ],
                        backgroundColor: ['#ef4444', '#f59e0b', '#10b981'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // 更新成本构成图表

        // 更新甘特图
        function updateGanttChart(data) {
            const ganttContent = document.getElementById('ganttContent');
            ganttContent.innerHTML = '';

            // 正确处理日期字符串转换
            const startDate = new Date(data.schedule.startDate);
            const endDate = new Date(data.schedule.endDate);
            const totalDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

            console.log('甘特图数据:', {
                startDate: startDate.toISOString().split('T')[0],
                endDate: endDate.toISOString().split('T')[0],
                totalDays: totalDays,
                projectsCount: data.schedule.projects.length
            });

            // 添加时间轴标尺
            const timeScale = document.createElement('div');
            timeScale.className = 'gantt-time-scale';

            // 根据总天数动态调整时间标记密度
            const markerInterval = totalDays > 180 ? 30 : (totalDays > 60 ? 15 : 7);

            for (let i = 0; i <= totalDays; i += markerInterval) {
                const marker = document.createElement('div');
                marker.className = 'gantt-time-marker';
                marker.style.left = `${(i / totalDays) * 100}%`;

                const markerDate = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
                marker.textContent = markerDate.toLocaleDateString('zh-CN', {
                    month: '2-digit',
                    day: '2-digit'
                });

                timeScale.appendChild(marker);
            }

            ganttContent.appendChild(timeScale);

            // 添加项目行
            data.schedule.projects.forEach((project, index) => {
                const row = document.createElement('div');
                row.className = 'gantt-row';

                // 处理日期字符串转换为Date对象
                const projectStartDate = new Date(project.startDate);
                const projectEndDate = new Date(project.endDate);

                // 计算项目在时间轴上的开始位置（天数）
                const projectStartDays = Math.floor((projectStartDate - startDate) / (1000 * 60 * 60 * 24));
                // 计算项目实际持续天数
                const projectDurationDays = Math.ceil((projectEndDate - projectStartDate) / (1000 * 60 * 60 * 24));

                // 计算柱子的位置和宽度百分比
                const barLeft = Math.max(0, (projectStartDays / totalDays) * 100);
                const barWidth = Math.min(100 - barLeft, (projectDurationDays / totalDays) * 100);

                row.innerHTML = `
                    <div class="gantt-project-label" title="${project.name}">
                        ${project.name}
                    </div>
                    <div class="gantt-bar ${project.complexity}"
                         style="left: ${barLeft}%; width: ${barWidth}%;"
                         title="项目: ${project.name}
开始: ${projectStartDate.toLocaleDateString()}
结束: ${projectEndDate.toLocaleDateString()}
周期: ${projectDurationDays}天
复杂度: ${project.complexity}
车辆: ${project.vehicles}台">
                        <div class="gantt-bar-content">
                            <div class="gantt-bar-duration">${projectDurationDays}天</div>
                            <div class="gantt-bar-vehicles">${project.vehicles}台</div>
                        </div>
                    </div>
                `;

                ganttContent.appendChild(row);
            });

            // 添加总结行
            const summaryRow = document.createElement('div');
            summaryRow.className = 'gantt-row';
            summaryRow.style.borderTop = '2px solid #cbd5e1';
            summaryRow.style.backgroundColor = '#f8fafc';
            summaryRow.innerHTML = `
                <div class="gantt-project-label" style="font-weight: bold; color: #4f46e5;">
                    总计 (${data.summary.totalProjects}个项目)
                </div>
                <div style="position: absolute; right: 20px; font-size: 12px; color: #6b7280;">
                    总周期: ${data.summary.totalDuration}天 | 总车辆: ${data.summary.totalVehicles}台
                </div>
            `;

            ganttContent.appendChild(summaryRow);
        }

        // 更新项目表格
        function updateProjectsTable(data) {
            const tableBody = document.getElementById('projectsTableBody');
            tableBody.innerHTML = '';

            data.matchedProjects.forEach(project => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${project.name}</td>
                    <td>${(project.matchRatio * 100).toFixed(1)}%</td>
                    <td><span class="complexity-badge ${project.complexity}">${project.complexity}</span></td>
                    <td>${project.testCycle}天</td>
                    <td>${project.aiCalculatedVehicles}台</td>
                    <td>${project.matchedComponents.join(', ')}</td>
                `;
                tableBody.appendChild(row);
            });
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('emptyState').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'none';
            document.getElementById('loadingState').style.display = 'block';
            document.querySelector('.generate-btn').disabled = true;
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loadingState').style.display = 'none';
            document.querySelector('.generate-btn').disabled = false;
        }

        // 显示结果
        function showResults() {
            document.getElementById('emptyState').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'block';
        }

        // 更新加载文本
        function updateLoadingText(text) {
            const loadingState = document.getElementById('loadingState');
            const paragraph = loadingState.querySelector('p');
            if (paragraph) {
                paragraph.textContent = text;
            }
        }

        // 导出Markdown
        function exportMarkdown() {
            if (!currentDVPData) {
                alert('请先生成DVP方案！');
                return;
            }

            const markdown = generateMarkdownReport(currentDVPData);
            downloadFile(markdown, `DVP_Report_${Date.now()}.md`, 'text/markdown');
        }

        // 导出JSON
        function exportJSON() {
            if (!currentDVPData) {
                alert('请先生成DVP方案！');
                return;
            }

            const json = JSON.stringify(currentDVPData, null, 2);
            downloadFile(json, `DVP_Data_${Date.now()}.json`, 'application/json');
        }

        // 生成Markdown报告
        function generateMarkdownReport(data) {
            return `# 🚗 DVP智能生成方案报告

## 📋 项目基本信息

| 项目 | 值 |
|------|-----|
| **方案编号** | ${data.id} |
| **生成时间** | ${new Date(data.generatedDate).toLocaleString()} |
| **配置类型** | ${data.config.configType} |
| **SOP日期** | ${data.config.sopDate} |
| **车辆组件** | ${data.config.components.join(', ')} |

## 🚙 AI智能车辆计算

### 计算结果
- **所需车辆总数**: ${data.summary.totalVehicles}台
- **测试周期**: ${data.summary.totalDuration}天
- **风险等级**: ${data.summary.riskLevel}

### 匹配的试验项目

${data.matchedProjects.map((project, index) => `
#### ${index + 1}. ${project.name}
- **匹配度**: ${(project.matchRatio * 100).toFixed(1)}%
- **测试周期**: ${project.testCycle}天
- **复杂度**: ${project.complexity}
- **AI计算车辆**: ${project.aiCalculatedVehicles}台
- **匹配组件**: ${project.matchedComponents.join(', ')}
`).join('')}

## 📅 测试排期计划

${data.schedule.projects.map(project => `
- **${project.name}**: ${project.startDate.toLocaleDateString()} ~ ${project.endDate.toLocaleDateString()} (${project.duration}天)
`).join('')}

---
*报告由DVP智能生成系统自动生成，采用纯AI推理算法，确保计算公正性*`;
        }

        // 下载文件
        function downloadFile(content, filename, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 主题切换功能
        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', currentTheme);

            const icon = document.getElementById('theme-icon');
            const text = document.getElementById('theme-text');

            if (currentTheme === 'dark') {
                icon.textContent = '☀️';
                text.textContent = '明亮模式';
            } else {
                icon.textContent = '🌙';
                text.textContent = '暗黑模式';
            }

            localStorage.setItem('theme', currentTheme);

            // 重新渲染图表以适应新主题
            if (currentDVPData) {
                updateCharts(currentDVPData);
            }
        }

        function initTheme() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                currentTheme = savedTheme;
                document.documentElement.setAttribute('data-theme', currentTheme);

                const icon = document.getElementById('theme-icon');
                const text = document.getElementById('theme-text');

                if (currentTheme === 'dark') {
                    icon.textContent = '☀️';
                    text.textContent = '明亮模式';
                } else {
                    icon.textContent = '🌙';
                    text.textContent = '暗黑模式';
                }
            }
        }

        // 测试API连接
        async function testConnection() {
            try {
                showLoading();
                updateLoadingText('正在测试API连接...');

                // 并行测试多个API端点
                const [connectionTest, componentsTest, projectsTest] = await Promise.allSettled([
                    fetch(API_CONFIG.getUrl('TEST_CONNECTION')),
                    fetch(API_CONFIG.getUrl('GET_COMPONENTS')),
                    fetch(API_CONFIG.getUrl('GET_TEST_PROJECTS'))
                ]);

                hideLoading();

                let statusMsg = '🔗 API连接测试结果:\n\n';

                // 测试基础连接
                if (connectionTest.status === 'fulfilled') {
                    const result = await connectionTest.value.json();
                    statusMsg += `✅ SiliconCloud LLM: ${result.siliconcloud ? '连接正常' : '连接失败'}\n`;
                    statusMsg += `✅ Neo4j数据库: ${result.neo4j ? '连接正常' : '连接失败'}\n`;
                    statusMsg += `✅ 业务逻辑矩阵: ${result.matrixData ? '数据加载成功' : '数据加载失败'}\n`;
                } else {
                    statusMsg += '❌ 基础连接测试失败\n';
                }

                // 测试组件API
                if (componentsTest.status === 'fulfilled') {
                    const result = await componentsTest.value.json();
                    statusMsg += `✅ 配件数据API: ${result.success ? `成功 (${result.components?.length || 0}个配件)` : '失败'}\n`;
                } else {
                    statusMsg += '❌ 配件数据API: 连接失败\n';
                }

                // 测试项目API
                if (projectsTest.status === 'fulfilled') {
                    const result = await projectsTest.value.json();
                    statusMsg += `✅ 测试项目API: ${result.success ? `成功 (${result.projects?.length || 0}个项目)` : '失败'}\n`;
                } else {
                    statusMsg += '❌ 测试项目API: 连接失败\n';
                }

                statusMsg += '\n系统已就绪，可以生成真实DVP方案！';
                alert(statusMsg);

            } catch (error) {
                hideLoading();
                alert(`连接测试失败: ${error.message}\n\n请确保后端服务器正在运行。`);
            }
        }

        // 知识图谱相关功能
        async function showKnowledgeGraph() {
            document.getElementById('graphModal').style.display = 'block';

            // 如果图表还未初始化，则初始化
            if (!knowledgeGraphChart) {
                initKnowledgeGraph();
            }

            // 加载图谱数据
            await loadGraphData();
        }

        function closeKnowledgeGraph() {
            document.getElementById('graphModal').style.display = 'none';
        }

        function initKnowledgeGraph() {
            const container = document.getElementById('graphContainer');
            knowledgeGraphChart = echarts.init(container);

            // 设置图表的基本配置
            const option = {
                title: {
                    text: 'Neo4j 知识图谱',
                    left: 'center',
                    textStyle: {
                        color: '#333',
                        fontSize: 18
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        if (params.dataType === 'node') {
                            return `<strong>${params.data.name}</strong><br/>类型: ${params.data.category}<br/>属性: ${JSON.stringify(params.data.properties || {}, null, 2)}`;
                        } else if (params.dataType === 'edge') {
                            return `关系: ${params.data.name}<br/>从: ${params.data.source}<br/>到: ${params.data.target}`;
                        }
                    }
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    data: ['车辆配置', '组件', '测试项目', '关系']
                },
                series: [{
                    name: '知识图谱',
                    type: 'graph',
                    layout: 'force',
                    data: [],
                    links: [],
                    categories: [
                        { name: '车辆配置', itemStyle: { color: '#ff7f0e' } },
                        { name: '组件', itemStyle: { color: '#2ca02c' } },
                        { name: '测试项目', itemStyle: { color: '#1f77b4' } },
                        { name: '关系', itemStyle: { color: '#d62728' } }
                    ],
                    roam: true,
                    focusNodeAdjacency: true,
                    itemStyle: {
                        borderColor: '#fff',
                        borderWidth: 1,
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.3)'
                    },
                    label: {
                        show: true,
                        position: 'right',
                        formatter: '{b}'
                    },
                    lineStyle: {
                        color: 'source',
                        curveness: 0.3
                    },
                    emphasis: {
                        focus: 'adjacency',
                        lineStyle: {
                            width: 10
                        }
                    },
                    force: {
                        repulsion: 1000,
                        gravity: 0.1,
                        edgeLength: 150,
                        layoutAnimation: true
                    }
                }]
            };

            knowledgeGraphChart.setOption(option);

            // 监听窗口大小变化
            window.addEventListener('resize', function() {
                if (knowledgeGraphChart) {
                    knowledgeGraphChart.resize();
                }
            });
        }

        async function loadGraphData() {
            try {
                // 初始化图表容器
                if (!knowledgeGraphChart) {
                    initKnowledgeGraph();
                }

                // 显示加载状态 - 使用ECharts的showLoading方法
                if (knowledgeGraphChart) {
                    knowledgeGraphChart.showLoading({
                        text: '正在加载知识图谱数据...',
                        color: '#4f46e5',
                        textColor: '#666',
                        maskColor: 'rgba(255, 255, 255, 0.8)',
                        zlevel: 0
                    });
                }

                console.log('正在从Neo4j数据库获取知识图谱数据...');

                // 调用后端API获取图谱数据
                const response = await fetch(API_CONFIG.getUrl('GET_KNOWLEDGE_GRAPH'));

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();

                if (!result.success) {
                    throw new Error(result.error || '获取图谱数据失败');
                }

                graphData = result.data;
                console.log('知识图谱数据加载成功:', graphData);

                // 隐藏加载状态
                if (knowledgeGraphChart) {
                    knowledgeGraphChart.hideLoading();
                }

                // 处理并渲染图谱数据
                renderGraphData(graphData);

                // 更新元数据统计
                updateGraphStats(graphData);

            } catch (error) {
                console.error('加载图谱数据失败:', error);

                // 隐藏加载状态
                if (knowledgeGraphChart) {
                    knowledgeGraphChart.hideLoading();
                }

                // 显示错误信息在图表中
                if (knowledgeGraphChart) {
                    const errorOption = {
                        title: {
                            text: '加载失败',
                            subtext: error.message,
                            left: 'center',
                            top: 'center',
                            textStyle: {
                                color: '#ef4444',
                                fontSize: 18
                            },
                            subtextStyle: {
                                color: '#666',
                                fontSize: 14
                            }
                        },
                        series: [{
                            data: [],
                            links: []
                        }]
                    };
                    knowledgeGraphChart.setOption(errorOption, true);
                }

                alert(`知识图谱加载失败: ${error.message}\n\n请检查网络连接或重试加载。`);
            }
        }

        function renderGraphData(data) {
            if (!knowledgeGraphChart || !data) {
                console.error('图表未初始化或数据为空');
                return;
            }

            console.log('开始渲染图谱数据:', data);

            // 转换节点数据
            const nodes = data.nodes.map(node => {
                const nodeCategory = node.category || 'Unknown';
                return {
                    id: node.id,
                    name: node.name || node.id,
                    category: getCategoryIndex(nodeCategory),
                    value: node.properties,
                    properties: node.properties,
                    symbolSize: getNodeSize(nodeCategory),
                    itemStyle: {
                        color: getNodeColor(nodeCategory)
                    }
                };
            });

            // 转换关系数据
            const links = data.links.map(rel => ({
                source: rel.source,
                target: rel.target,
                name: rel.relation,
                lineStyle: {
                    color: '#999',
                    width: 2
                }
            }));

            console.log(`处理完成: ${nodes.length}个节点, ${links.length}个关系`);

            // 完整的图表配置
            const option = {
                title: {
                    text: 'Neo4j 知识图谱',
                    left: 'center',
                    textStyle: {
                        color: '#333',
                        fontSize: 18
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        if (params.dataType === 'node') {
                            const categoryNames = ['车辆配置', '组件', '测试项目', '其他'];
                            const categoryName = categoryNames[params.data.category] || '其他';
                            return `<strong>${params.data.name}</strong><br/>类型: ${categoryName}<br/>属性: ${JSON.stringify(params.data.properties || {}, null, 2)}`;
                        } else if (params.dataType === 'edge') {
                            return `关系: ${params.data.name}<br/>从: ${params.data.source}<br/>到: ${params.data.target}`;
                        }
                    }
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    data: ['车辆配置', '组件', '测试项目', '其他']
                },
                series: [{
                    name: '知识图谱',
                    type: 'graph',
                    layout: 'force',
                    data: nodes,
                    links: links,
                    categories: [
                        { name: '车辆配置', itemStyle: { color: '#ff7f0e' } },
                        { name: '组件', itemStyle: { color: '#2ca02c' } },
                        { name: '测试项目', itemStyle: { color: '#1f77b4' } },
                        { name: '其他', itemStyle: { color: '#d62728' } }
                    ],
                    roam: true,
                    focusNodeAdjacency: true,
                    itemStyle: {
                        borderColor: '#fff',
                        borderWidth: 1,
                        shadowBlur: 3,
                        shadowColor: 'rgba(0, 0, 0, 0.3)'
                    },
                    label: {
                        show: true,
                        position: 'right',
                        formatter: '{b}',
                        fontSize: 12
                    },
                    lineStyle: {
                        color: 'source',
                        curveness: 0.3,
                        opacity: 0.7
                    },
                    emphasis: {
                        focus: 'adjacency',
                        lineStyle: {
                            width: 4
                        }
                    },
                    force: {
                        repulsion: 1000,
                        gravity: 0.1,
                        edgeLength: 150,
                        layoutAnimation: true
                    }
                }]
            };

            // 设置图表选项
            knowledgeGraphChart.setOption(option, true);
            console.log('图表渲染完成');
        }

        function getCategoryIndex(label) {
            const categoryMap = {
                'VehicleConfig': 0,
                'Component': 1,
                'TestProject': 2
            };
            return categoryMap[label] || 3;
        }

        function getNodeColor(label) {
            const colorMap = {
                'VehicleConfig': '#ff7f0e',
                'Component': '#2ca02c',
                'TestProject': '#1f77b4'
            };
            return colorMap[label] || '#d62728';
        }

        function getNodeSize(label) {
            const sizeMap = {
                'VehicleConfig': 60,
                'Component': 40,
                'TestProject': 50
            };
            return sizeMap[label] || 30;
        }

        function updateGraphStats(data) {
            if (!data) return;

            const nodeCount = data.nodes.length;
            const relationCount = data.links.length;

            // 统计不同类型的节点数量
            const vehicleCount = data.nodes.filter(n => n.category === 'VehicleConfig').length;
            const componentCount = data.nodes.filter(n => n.category === 'Component').length;
            const projectCount = data.nodes.filter(n => n.category === 'TestProject').length;

            // 更新统计显示
            document.getElementById('nodeCount').textContent = nodeCount;
            document.getElementById('relationCount').textContent = relationCount;
            document.getElementById('vehicleCount').textContent = vehicleCount;
            document.getElementById('componentCount').textContent = componentCount;
            document.getElementById('projectCount').textContent = projectCount;
        }

        function refreshGraph() {
            loadGraphData();
        }

        function resetGraphView() {
            if (knowledgeGraphChart) {
                knowledgeGraphChart.dispatchAction({
                    type: 'restore'
                });
            }
        }

        function filterGraph() {
            const filterValue = document.getElementById('graphFilter').value;

            if (!graphData || !knowledgeGraphChart) return;

            let filteredNodes = graphData.nodes;
            let filteredLinks = graphData.links;

            if (filterValue !== 'all') {
                const labelMap = {
                    'vehicles': 'VehicleConfig',
                    'components': 'Component',
                    'projects': 'TestProject'
                };

                const targetLabel = labelMap[filterValue];
                filteredNodes = graphData.nodes.filter(node => node.category === targetLabel);

                // 过滤相关的关系
                const nodeIds = new Set(filteredNodes.map(n => n.id));
                filteredLinks = graphData.links.filter(rel =>
                    nodeIds.has(rel.source) && nodeIds.has(rel.target)
                );
            }

            // 重新渲染过滤后的数据
            const filteredData = {
                nodes: filteredNodes,
                links: filteredLinks
            };

            renderGraphData(filteredData);
            updateGraphStats(filteredData);
        }

        // 点击弹窗外部关闭弹窗
        window.onclick = function(event) {
            const modal = document.getElementById('graphModal');
            if (event.target === modal) {
                closeKnowledgeGraph();
            }
        }
    </script>
</body>
</html>