# -*- coding: utf-8 -*-
"""
改进的 LLM 连接测试脚本
增加重试机制和更好的错误处理
"""

import json
import urllib.request
import urllib.parse
import urllib.error
import time
import ssl
import socket
from datetime import datetime

def load_env_config():
    """从.env文件加载配置"""
    config = {}
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()
        return config
    except FileNotFoundError:
        print("❌ 未找到 .env 文件")
        return {}

def create_ssl_context():
    """创建更宽松的SSL上下文"""
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

def robust_api_call(url, data, headers, timeout=30, max_retries=3):
    """带重试机制的API调用"""
    ssl_context = create_ssl_context()
    
    for attempt in range(max_retries):
        try:
            print(f"  🔄 尝试 {attempt + 1}/{max_retries}")
            
            data_bytes = json.dumps(data).encode('utf-8')
            req = urllib.request.Request(url, data_bytes, headers)
            
            # 使用自定义SSL上下文
            with urllib.request.urlopen(req, timeout=timeout, context=ssl_context) as response:
                result = json.loads(response.read().decode('utf-8'))
                return True, result, response.status
                
        except urllib.error.HTTPError as e:
            error_msg = f"HTTP错误 {e.code}: {e.reason}"
            if attempt == max_retries - 1:
                return False, error_msg, e.code
            print(f"    ⚠️ {error_msg}, 重试中...")
            time.sleep(2 ** attempt)  # 指数退避
            
        except urllib.error.URLError as e:
            error_msg = f"网络错误: {e.reason}"
            if attempt == max_retries - 1:
                return False, error_msg, None
            print(f"    ⚠️ {error_msg}, 重试中...")
            time.sleep(2 ** attempt)
            
        except socket.timeout:
            error_msg = "请求超时"
            if attempt == max_retries - 1:
                return False, error_msg, None
            print(f"    ⚠️ {error_msg}, 重试中...")
            time.sleep(2 ** attempt)
            
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            if attempt == max_retries - 1:
                return False, error_msg, None
            print(f"    ⚠️ {error_msg}, 重试中...")
            time.sleep(2 ** attempt)
    
    return False, "所有重试均失败", None

def test_basic_connection_robust():
    """改进的基本连接测试"""
    print("🔍 改进的基本连接测试")
    print("=" * 50)
    
    config = load_env_config()
    api_key = config.get('SILICONCLOUD_API_KEY', 'sk-nauixigqyamklriyoqzepwtxjtenumbehtucnjdtxtvloxbz')
    base_url = config.get('SILICONCLOUD_BASE_URL', 'https://api.siliconflow.cn/v1')
    
    print(f"📡 API Base URL: {base_url}")
    print(f"🔑 API Key: {api_key[:20]}...{api_key[-10:]}")
    
    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "User-Agent": "DVP-Test-Client/1.0"
    }
    
    data = {
        "model": "deepseek-ai/DeepSeek-R1",
        "messages": [
            {"role": "user", "content": "请简单说'测试成功'"}
        ],
        "max_tokens": 50,
        "temperature": 0.1
    }
    
    start_time = time.time()
    success, result, status_code = robust_api_call(url, data, headers, timeout=45, max_retries=3)
    response_time = time.time() - start_time
    
    if success:
        print(f"✅ 连接成功!")
        print(f"⏱️ 总响应时间: {response_time:.2f}秒")
        print(f"📊 HTTP状态码: {status_code}")
        
        if "choices" in result and len(result["choices"]) > 0:
            content = result["choices"][0]["message"]["content"]
            print(f"🤖 LLM响应: {content}")
            return True, content
        else:
            print("⚠️ 响应格式异常")
            return False, "响应格式异常"
    else:
        print(f"❌ 连接失败: {result}")
        return False, result

def test_simple_dvp_calculation():
    """简化的DVP计算测试"""
    print("\n🔍 简化DVP计算测试")
    print("=" * 50)
    
    config = load_env_config()
    api_key = config.get('SILICONCLOUD_API_KEY')
    base_url = config.get('SILICONCLOUD_BASE_URL', 'https://api.siliconflow.cn/v1')
    
    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "User-Agent": "DVP-Test-Client/1.0"
    }
    
    # 简化的DVP提示
    prompt = """
作为汽车测试专家，请计算以下测试项目需要的车辆数量：
1. ACC测试：15天
2. AEB测试：10天
3. LKA测试：12天

请返回JSON格式：{"totalVehicles": 数字, "reasoning": "简短说明"}
"""
    
    data = {
        "model": "deepseek-ai/DeepSeek-R1",
        "messages": [
            {"role": "user", "content": prompt}
        ],
        "max_tokens": 300,
        "temperature": 0.3
    }
    
    print("🤖 发送DVP计算请求...")
    start_time = time.time()
    success, result, status_code = robust_api_call(url, data, headers, timeout=60, max_retries=2)
    response_time = time.time() - start_time
    
    if success:
        print(f"✅ DVP计算成功!")
        print(f"⏱️ 响应时间: {response_time:.2f}秒")
        
        if "choices" in result and len(result["choices"]) > 0:
            content = result["choices"][0]["message"]["content"]
            print(f"🤖 LLM分析结果:")
            print("-" * 30)
            print(content)
            print("-" * 30)
            
            # 尝试提取JSON
            try:
                if "{" in content and "}" in content:
                    json_start = content.find("{")
                    json_end = content.rfind("}") + 1
                    json_str = content[json_start:json_end]
                    parsed_result = json.loads(json_str)
                    print(f"📊 解析结果: {parsed_result}")
                    return True, parsed_result
            except:
                print("⚠️ JSON解析失败，但响应正常")
            
            return True, content
        else:
            print("⚠️ 响应格式异常")
            return False, "响应格式异常"
    else:
        print(f"❌ DVP计算失败: {result}")
        return False, result

def test_connection_stability():
    """测试连接稳定性"""
    print("\n🔍 连接稳定性测试")
    print("=" * 50)
    
    config = load_env_config()
    api_key = config.get('SILICONCLOUD_API_KEY')
    base_url = config.get('SILICONCLOUD_BASE_URL', 'https://api.siliconflow.cn/v1')
    
    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "User-Agent": "DVP-Test-Client/1.0"
    }
    
    # 进行5次快速测试
    success_count = 0
    total_tests = 5
    response_times = []
    
    for i in range(total_tests):
        print(f"\n📍 稳定性测试 {i+1}/{total_tests}")
        
        data = {
            "model": "deepseek-ai/DeepSeek-R1",
            "messages": [
                {"role": "user", "content": f"请说'测试{i+1}'"}
            ],
            "max_tokens": 20,
            "temperature": 0.1
        }
        
        start_time = time.time()
        success, result, status_code = robust_api_call(url, data, headers, timeout=30, max_retries=2)
        response_time = time.time() - start_time
        
        if success:
            success_count += 1
            response_times.append(response_time)
            print(f"  ✅ 成功 ({response_time:.2f}s)")
        else:
            print(f"  ❌ 失败: {result}")
        
        # 间隔1秒
        time.sleep(1)
    
    print(f"\n📊 稳定性测试结果:")
    print(f"  成功率: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    if response_times:
        avg_time = sum(response_times) / len(response_times)
        print(f"  平均响应时间: {avg_time:.2f}秒")
        print(f"  最快响应: {min(response_times):.2f}秒")
        print(f"  最慢响应: {max(response_times):.2f}秒")
    
    return success_count, total_tests, response_times

def main():
    """主测试函数"""
    print("🚀 改进的 SiliconCloud LLM 连接测试")
    print("=" * 60)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查.env文件
    if not os.path.exists('.env'):
        print("❌ 未找到.env文件，请确保配置文件存在")
        return
    
    test_results = {}
    
    # 测试1: 改进的基本连接测试
    basic_success, basic_response = test_basic_connection_robust()
    test_results['basic_connection'] = {'success': basic_success, 'response': basic_response}
    
    if basic_success:
        # 测试2: 简化DVP计算测试
        dvp_success, dvp_response = test_simple_dvp_calculation()
        test_results['dvp_calculation'] = {'success': dvp_success, 'response': dvp_response}
        
        # 测试3: 连接稳定性测试
        success_count, total_tests, response_times = test_connection_stability()
        test_results['stability'] = {
            'success_count': success_count,
            'total_tests': total_tests,
            'success_rate': success_count/total_tests,
            'response_times': response_times
        }
    
    # 生成改进的测试报告
    print("\n📊 改进测试总结报告")
    print("=" * 60)
    
    if test_results['basic_connection']['success']:
        print("✅ 基本连接: 成功")
        
        if 'dvp_calculation' in test_results:
            dvp_status = "成功" if test_results['dvp_calculation']['success'] else "失败"
            print(f"🚗 DVP计算: {dvp_status}")
        
        if 'stability' in test_results:
            stability = test_results['stability']
            print(f"📈 连接稳定性: {stability['success_count']}/{stability['total_tests']} ({stability['success_rate']*100:.1f}%)")
        
        print("\n🎉 LLM连接测试完成！")
        
        # 给出使用建议
        if 'stability' in test_results and test_results['stability']['success_rate'] < 0.8:
            print("\n⚠️ 建议:")
            print("  - 网络连接不够稳定，建议在实际使用中增加重试机制")
            print("  - 考虑增加超时时间和错误处理")
        else:
            print("\n✅ 系统连接稳定，可以正常使用！")
    else:
        print("❌ 基本连接: 失败")
        print(f"   错误信息: {test_results['basic_connection']['response']}")
        print("\n⚠️ 请检查网络连接和API配置")
    
    # 保存测试结果
    try:
        with open('improved_llm_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
        print(f"\n📄 详细测试结果已保存到: improved_llm_test_results.json")
    except Exception as e:
        print(f"\n⚠️ 保存测试结果失败: {e}")

if __name__ == "__main__":
    import os
    main()
