#!/bin/bash

# DVP智能生成系统依赖安装脚本

echo "🚀 DVP智能生成系统依赖安装脚本"
echo "=================================="

# 检查Python 3
echo "📝 检查Python 3版本..."
python3 --version

# 选项1: 使用虚拟环境（推荐）
echo ""
echo "选项1: 使用虚拟环境（推荐）"
echo "python3 -m venv dvp_env"
echo "source dvp_env/bin/activate"
echo "pip install neo4j flask openai requests"

# 选项2: 系统级安装（需要权限）
echo ""
echo "选项2: 系统级安装"
echo "python3 -m pip install --user neo4j flask openai requests"

# 选项3: 使用break-system-packages（不推荐）
echo ""
echo "选项3: 强制安装（不推荐）"
echo "python3 -m pip install --break-system-packages neo4j flask openai requests"

echo ""
echo "📁 创建必要目录..."
mkdir -p reports
echo "✅ reports目录已创建"

echo ""
echo "🔧 安装完成后运行："
echo "python3 dvp_api_server.py"

echo ""
echo "📋 如果遇到Neo4j连接问题，系统将自动使用模拟模式"
echo "📋 如果遇到LLM API超时，系统将使用fallback计算"
echo ""
echo "完整使用指南请查看: QUICK_START_GUIDE.md"