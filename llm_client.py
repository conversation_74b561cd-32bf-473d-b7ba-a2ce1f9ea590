# -*- coding: utf-8 -*-
"""
SiliconCloud LLM API客户端
用于车辆数量计算和测试排期推理
"""

import json
import urllib.request
import urllib.parse
import urllib.error

class SiliconCloudClient:
    """SiliconCloud LLM API客户端"""
    
    def __init__(self, api_key="sk-nauixigqyamklriyoqzepwtxjtenumbehtucnjdtxtvloxbz"):
        self.api_key = api_key
        self.base_url = "https://api.siliconflow.cn/v1"
        self.headers = {
            "Authorization": "Bearer " + api_key,
            "Content-Type": "application/json"
        }
    
    def call_llm(self, prompt, model="deepseek-ai/DeepSeek-R1"):
        """调用LLM API"""
        url = self.base_url + "/chat/completions"
        
        data = {
            "model": model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 1000,
            "temperature": 0.7
        }
        
        try:
            # 使用urllib发送POST请求，增加超时时间到60秒
            data_bytes = json.dumps(data).encode('utf-8')
            req = urllib.request.Request(url, data_bytes, self.headers)
            with urllib.request.urlopen(req, timeout=60) as response:
                result = json.loads(response.read().decode('utf-8'))
            
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                return "API调用失败"
                
        except Exception as e:
            print("LLM API调用错误: {}".format(str(e)))
            return "API调用失败: {}".format(str(e))
    
    def call_llm_with_retry(self, prompt, model="deepseek-ai/DeepSeek-R1", max_retries=3, retry_delay=2):
        """带重试机制的LLM API调用"""
        for attempt in range(max_retries):
            try:
                result = self.call_llm(prompt, model)
                if not result.startswith("API调用失败"):
                    return result
                
                print(f"尝试 {attempt+1}/{max_retries} 失败，{retry_delay}秒后重试...")
                import time
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
                
            except Exception as e:
                print(f"重试过程中出错: {str(e)}")
        
        return "API调用失败: 已达到最大重试次数"
    
    def calculate_vehicle_count(self, test_projects, logic_rules):
        """使用LLM计算车辆数量"""
        prompt = """
作为汽车DVP测试专家，请根据以下信息计算所需的试验车辆数量：

试验项目：
{}

计算规则：
{}

请分析每个试验项目的车辆需求，考虑：
1. 测试项目的复杂度
2. 测试周期的重叠情况
3. 车辆的复用可能性
4. 风险缓解需要的备用车辆

请给出详细的计算过程和最终的车辆数量建议。
""".format(
            "\n".join([p.get("project_name", "") + ": " + str(p.get("test_duration", 0)) + "天" for p in test_projects]),
            "\n".join(logic_rules)
        )
        
        print("正在调用LLM计算车辆数量...")
        result = self.call_llm(prompt)
        print("LLM计算结果:")
        print(result)
        
        # 从LLM结果中提取数字（简化处理）
        try:
            # 查找结果中的数字
            import re
            numbers = re.findall(r'\d+', result)
            if numbers:
                return int(numbers[-1])  # 返回最后一个数字作为车辆数量
            else:
                return 3  # 默认值
        except:
            return 3  # 默认值
    
    def generate_test_schedule(self, test_projects, sop_date, total_vehicles):
        """使用LLM生成测试排期"""
        prompt = """
作为汽车DVP测试计划专家，请根据以下信息制定测试排期：

试验项目：
{}

SOP量产日期：{}
可用车辆数量：{}台

请制定详细的测试排期计划，包括：
1. 各个测试项目的开始和结束时间
2. 车辆分配方案
3. 关键里程碑节点
4. 风险缓解措施

请以JSON格式返回排期计划。
""".format(
            "\n".join([
                "{}: {}天, 需要组件: {}".format(
                    p.get("project_name", ""),
                    p.get("test_duration", 0),
                    ", ".join(p.get("required_components", []))
                ) for p in test_projects
            ]),
            sop_date,
            total_vehicles
        )
        
        print("正在调用LLM生成测试排期...")
        result = self.call_llm(prompt)
        print("LLM排期结果:")
        print(result)
        
        # 简化处理，返回基本排期结构
        schedule = {
            "total_duration": sum([p.get("test_duration", 0) for p in test_projects]),
            "vehicle_allocation": total_vehicles,
            "projects": test_projects,
            "sop_date": sop_date,
            "llm_analysis": result
        }
        
        return schedule

def test_llm_client():
    """测试LLM客户端"""
    client = SiliconCloudClient()
    
    # 测试基本调用
    test_prompt = "请用中文回答：什么是汽车DVP测试？"
    result = client.call_llm(test_prompt)
    print("测试结果:")
    print(result)
    
    # 测试车辆数量计算
    test_projects = [
        {
            "project_name": "ACC自适应巡航测试",
            "test_duration": 15,
            "required_components": ["前摄像头", "毫米波雷达"]
        },
        {
            "project_name": "AEB自动紧急制动测试",
            "test_duration": 10,
            "required_components": ["前摄像头", "毫米波雷达"]
        }
    ]
    
    logic_rules = [
        "单一功能测试需要1台车",
        "组合功能测试需要2台车",
        "长期测试需要3台车"
    ]
    
    vehicle_count = client.calculate_vehicle_count(test_projects, logic_rules)
    print("\n计算得出的车辆数量: {}台".format(vehicle_count))

if __name__ == "__main__":
    test_llm_client()
