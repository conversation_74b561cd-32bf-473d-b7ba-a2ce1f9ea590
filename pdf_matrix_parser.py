# -*- coding: utf-8 -*-
"""
PDF矩阵解析器
解析DVP生成—ADAS检测项目逻辑关系.pdf文件
"""

import json
import os
from collections import defaultdict

class PDFMatrixParser:
    """PDF矩阵解析器"""
    
    def __init__(self, pdf_path):
        self.pdf_path = pdf_path
        self.matrix_data = {
            "test_projects": [],           # A列：试验项目
            "components": [],              # 关联零件配置
            "relationships": [],           # 矩阵关系（✅和X）
            "test_cycles": {},             # D列：试验周期
            # 注意：已移除G列车辆基线数据，确保纯AI智能计算
            "logic_descriptions": {},      # B列：逻辑说明
            "variation_factors": []        # R-AA列：变化因素
        }
    
    def parse_matrix_structure(self):
        """解析矩阵结构"""
        print("正在解析PDF矩阵结构...")
        
        # 由于无法直接读取PDF，我们基于您的描述创建标准结构
        # 在实际应用中，这里会使用PDF解析库
        
        # 示例试验项目（A列）
        test_projects = [
            "ACC自适应巡航控制",
            "AEB自动紧急制动",
            "BSD盲点监测",
            "LDW车道偏离警告",
            "LKA车道保持辅助",
            "FCW前方碰撞警告",
            "APA自动泊车辅助",
            "TSR交通标志识别",
            "HMA远光灯辅助",
            "RCW后方交叉警告"
        ]
        
        # 示例关联零件配置
        components = [
            "前摄像头",
            "后摄像头",
            "左侧摄像头",
            "右侧摄像头",
            "前毫米波雷达",
            "后毫米波雷达",
            "左前毫米波雷达",
            "右前毫米波雷达",
            "激光雷达",
            "超声波传感器",
            "ADAS控制器ECU",
            "显示屏HMI"
        ]
        
        # 示例试验周期（D列）
        test_cycles = {
            "ACC自适应巡航控制": 20,
            "AEB自动紧急制动": 15,
            "BSD盲点监测": 10,
            "LDW车道偏离警告": 8,
            "LKA车道保持辅助": 12,
            "FCW前方碰撞警告": 10,
            "APA自动泊车辅助": 25,
            "TSR交通标志识别": 8,
            "HMA远光灯辅助": 5,
            "RCW后方交叉警告": 12
        }
        
        # 注意：已移除G列车辆基线数据
        # 车辆数量将完全由AI智能计算，不依赖预设基线
        # 这确保了系统的公正性和智能化水平
        
        # 示例逻辑说明（B列）
        logic_descriptions = {
            "ACC自适应巡航控制": "需要长距离测试和多场景验证，考虑不同天气条件",
            "AEB自动紧急制动": "需要高风险测试，需要备用车辆",
            "BSD盲点监测": "需要多角度测试，考虑不同车型",
            "LDW车道偏离警告": "相对简单功能，基础测试即可",
            "LKA车道保持辅助": "需要精确控制测试，需要备用车辆",
            "FCW前方碰撞警告": "需要多场景测试，考虑不同速度",
            "APA自动泊车辅助": "复杂功能，需要多种停车场景测试",
            "TSR交通标志识别": "基础视觉功能，标准测试",
            "HMA远光灯辅助": "简单功能，基础测试",
            "RCW后方交叉警告": "需要多角度测试，考虑盲区"
        }
        
        # 示例变化因素（R-AA列）
        variation_factors = [
            "天气条件",
            "路面状况",
            "光照条件",
            "车速范围",
            "目标车型",
            "传感器精度",
            "算法版本",
            "硬件配置"
        ]
        
        self.matrix_data.update({
            "test_projects": test_projects,
            "components": components,
            "test_cycles": test_cycles,
            # 注意：不再包含vehicle_baselines，确保纯AI计算
            "logic_descriptions": logic_descriptions,
            "variation_factors": variation_factors
        })
        
        print("矩阵结构解析完成")
        return True
    
    def parse_relationships(self):
        """解析关系矩阵（✅和X）"""
        print("正在解析关系矩阵...")
        
        # 示例关系矩阵
        relationships = []
        
        # 为每个试验项目定义相关组件
        project_component_map = {
            "ACC自适应巡航控制": ["前摄像头", "前毫米波雷达", "ADAS控制器ECU"],
            "AEB自动紧急制动": ["前摄像头", "前毫米波雷达", "ADAS控制器ECU"],
            "BSD盲点监测": ["后摄像头", "左前毫米波雷达", "右前毫米波雷达", "ADAS控制器ECU"],
            "LDW车道偏离警告": ["前摄像头", "ADAS控制器ECU"],
            "LKA车道保持辅助": ["前摄像头", "ADAS控制器ECU"],
            "FCW前方碰撞警告": ["前摄像头", "前毫米波雷达", "ADAS控制器ECU"],
            "APA自动泊车辅助": ["前摄像头", "后摄像头", "左侧摄像头", "右侧摄像头", "超声波传感器", "ADAS控制器ECU"],
            "TSR交通标志识别": ["前摄像头", "ADAS控制器ECU"],
            "HMA远光灯辅助": ["前摄像头", "ADAS控制器ECU"],
            "RCW后方交叉警告": ["后摄像头", "后毫米波雷达", "ADAS控制器ECU"]
        }
        
        # 生成关系矩阵
        for project in self.matrix_data["test_projects"]:
            for component in self.matrix_data["components"]:
                if project in project_component_map and component in project_component_map[project]:
                    relationship = {
                        "test_project": project,
                        "component": component,
                        "relationship": "✅",
                        "required": True
                    }
                else:
                    relationship = {
                        "test_project": project,
                        "component": component,
                        "relationship": "X",
                        "required": False
                    }
                relationships.append(relationship)
        
        self.matrix_data["relationships"] = relationships
        print("关系矩阵解析完成，共{}条关系".format(len(relationships)))
        return True
    
    def generate_knowledge_graph_data(self):
        """生成知识图谱数据"""
        print("正在生成知识图谱数据...")
        
        kg_data = {
            "nodes": [],
            "edges": []
        }
        
        # 添加试验项目节点
        for project in self.matrix_data["test_projects"]:
            node = {
                "id": "project_" + project.replace(" ", "_"),
                "label": project,
                "type": "TestProject",
                "properties": {
                    "name": project,
                    "test_cycle": self.matrix_data["test_cycles"].get(project, 0),
                    "vehicle_baseline": self.matrix_data["vehicle_baselines"].get(project, 1),
                    "logic_description": self.matrix_data["logic_descriptions"].get(project, "")
                }
            }
            kg_data["nodes"].append(node)
        
        # 添加组件节点
        for component in self.matrix_data["components"]:
            node = {
                "id": "component_" + component.replace(" ", "_"),
                "label": component,
                "type": "Component",
                "properties": {
                    "name": component,
                    "category": self._classify_component(component)
                }
            }
            kg_data["nodes"].append(node)
        
        # 添加关系边（只添加✅关系）
        for rel in self.matrix_data["relationships"]:
            if rel["relationship"] == "✅":
                edge = {
                    "from": "project_" + rel["test_project"].replace(" ", "_"),
                    "to": "component_" + rel["component"].replace(" ", "_"),
                    "type": "REQUIRES",
                    "properties": {
                        "required": True
                    }
                }
                kg_data["edges"].append(edge)
        
        print("知识图谱数据生成完成")
        print("节点数：{}".format(len(kg_data["nodes"])))
        print("边数：{}".format(len(kg_data["edges"])))
        
        return kg_data
    
    def _classify_component(self, component):
        """分类组件"""
        if "摄像头" in component:
            return "Camera"
        elif "雷达" in component:
            return "Radar"
        elif "传感器" in component:
            return "Sensor"
        elif "控制器" in component or "ECU" in component:
            return "Controller"
        elif "显示" in component or "HMI" in component:
            return "Display"
        else:
            return "Other"
    
    def save_parsed_data(self, output_file="parsed_matrix_data.json"):
        """保存解析数据"""
        print("正在保存解析数据...")
        
        with open(output_file, "w") as f:
            json.dump(self.matrix_data, f, ensure_ascii=False, indent=2)
        
        print("解析数据已保存至：{}".format(output_file))
        return output_file
    
    def parse_all(self):
        """解析所有内容"""
        print("开始解析PDF矩阵文件...")
        
        # 1. 解析矩阵结构
        self.parse_matrix_structure()
        
        # 2. 解析关系矩阵
        self.parse_relationships()
        
        # 3. 生成知识图谱数据
        kg_data = self.generate_knowledge_graph_data()
        
        # 4. 保存解析结果
        self.save_parsed_data()
        
        # 5. 保存知识图谱数据
        with open("knowledge_graph_data.json", "w") as f:
            json.dump(kg_data, f, ensure_ascii=False, indent=2)
        
        print("PDF矩阵解析完成！")
        return self.matrix_data, kg_data

def main():
    """主函数"""
    pdf_path = "/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/DVP生成—ADAS检测项目逻辑关系.pdf"
    
    parser = PDFMatrixParser(pdf_path)
    matrix_data, kg_data = parser.parse_all()
    
    print("\n=== 解析结果摘要 ===")
    print("试验项目数量：{}".format(len(matrix_data["test_projects"])))
    print("关联零件数量：{}".format(len(matrix_data["components"])))
    print("关系数量：{}".format(len(matrix_data["relationships"])))
    print("知识图谱节点：{}".format(len(kg_data["nodes"])))
    print("知识图谱边：{}".format(len(kg_data["edges"])))
    
    return matrix_data, kg_data

if __name__ == "__main__":
    main()