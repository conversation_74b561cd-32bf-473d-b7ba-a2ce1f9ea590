{"id": "DVP_AI_20250708_184426", "generatedDate": "2025-07-08T18:44:26.349082", "config": {"configType": "ADAS基础配置", "startDate": "2025-07-08", "sopDate": "2026-01-08", "components": ["AD摄像头前视/后视/侧视", "EPS"], "complexity": "standard", "environment": "normal", "priority": "normal", "testProjectsData": [{"name": "超声波雷达标定匹配", "optionalComponents": [], "requiredComponents": ["超声波雷达"], "testCycle": 3, "totalComponents": 1}, {"name": "毫米波雷达标定匹配", "optionalComponents": [], "requiredComponents": ["毫米波雷达"], "testCycle": 5, "totalComponents": 1}, {"name": "激光雷达标定匹配", "optionalComponents": [], "requiredComponents": ["激光雷达"], "testCycle": 5, "totalComponents": 1}, {"name": "定位IMU模块标定匹配", "optionalComponents": [], "requiredComponents": ["FSD变更"], "testCycle": 7, "totalComponents": 1}, {"name": "感知数采", "optionalComponents": [], "requiredComponents": ["FSD变更", "激光雷达", "AD摄像头前视/后视/侧视", "环视摄像头"], "testCycle": 10, "totalComponents": 4}, {"name": "感知开发", "optionalComponents": [], "requiredComponents": ["FSD变更", "激光雷达", "毫米波雷达", "超声波雷达", "AD摄像头前视/后视/侧视", "环视摄像头"], "testCycle": 15, "totalComponents": 6}, {"name": "产线标定", "optionalComponents": [], "requiredComponents": ["FSD变更", "激光雷达", "毫米波雷达", "超声波雷达", "AD摄像头前视/后视/侧视", "环视摄像头"], "testCycle": 5, "totalComponents": 6}, {"name": "底软开发", "optionalComponents": [], "requiredComponents": ["FSD变更"], "testCycle": 20, "totalComponents": 1}, {"name": "数据回传", "optionalComponents": [], "requiredComponents": ["FSD变更", "AD摄像头前视/后视/侧视", "环视摄像头"], "testCycle": 8, "totalComponents": 3}, {"name": "地图定位", "optionalComponents": [], "requiredComponents": ["FSD变更"], "testCycle": 12, "totalComponents": 1}, {"name": "行泊主功能开发", "optionalComponents": [], "requiredComponents": ["FSD变更", "激光雷达", "毫米波雷达", "超声波雷达", "AD摄像头前视/后视/侧视", "环视摄像头", "DPB+ESP/IPB", "EPS", "后轮转向", "悬架"], "testCycle": 25, "totalComponents": 10}, {"name": "功能集成测试", "optionalComponents": [], "requiredComponents": ["FSD变更", "激光雷达", "毫米波雷达", "超声波雷达", "AD摄像头前视/后视/侧视", "环视摄像头", "DPB+ESP/IPB", "EPS", "后轮转向", "悬架"], "testCycle": 20, "totalComponents": 10}, {"name": "泛化路试", "optionalComponents": [], "requiredComponents": ["FSD变更", "激光雷达", "毫米波雷达", "超声波雷达", "AD摄像头前视/后视/侧视", "环视摄像头", "DPB+ESP/IPB", "EPS", "后轮转向", "悬架"], "testCycle": 30, "totalComponents": 10}]}, "matchedProjects": [{"name": "数据回传", "matchedComponents": ["AD摄像头前视/后视/侧视"], "testCycle": 8, "source": "备用数据源", "matchRatio": 0.3333333333333333, "matchCount": 1, "totalComponents": 3, "complexity": "低", "recommendedVehicles": 2, "aiCalculatedVehicles": 2}], "vehicleCalculation": {"totalVehicles": 1, "reasoning": "备用计算：基础1台 × 优先级1.0 = 1台", "projectDetails": [{"name": "数据回传", "recommendedVehicles": 1, "reasoning": "基于8天测试周期的标准配置"}], "riskAssessment": "使用备用计算方法，建议人工复核", "scheduleRecommendations": "建议按测试周期从短到长排序执行", "aiPowered": false, "dataSource": "备用计算算法"}, "schedule": {"startDate": "2025-07-08T00:00:00", "endDate": "2026-01-08T00:00:00", "totalDuration": 6, "projects": [{"name": "数据回传", "startDate": "2025-07-08T00:00:00", "endDate": "2025-07-16T00:00:00", "duration": 8, "complexity": "低", "vehicles": 2}], "aiOptimized": false, "scheduling": "智能算法排期(184天窗口)"}, "summary": {"totalProjects": 1, "totalVehicles": 1, "totalDuration": 6, "riskLevel": "低", "aiPowered": true, "dataSource": "PDF业务逻辑矩阵 + Neo4j知识图谱"}}