 * Tip: There are .env files present. Install python-dotenv to use them.
矩阵数据加载成功: data/parsed_matrix_data.json
知识图谱数据加载成功: data/knowledge_graph_data.json
✅ 矩阵数据加载成功: data/parsed_matrix_data.json
🚀 启动DVP API服务器...
📊 已集成真实AI推理和业务逻辑矩阵
🌐 访问地址: http://localhost:5001
 * Serving Flask app 'dvp_api_server'
 * Debug mode: on
[31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5001
 * Running on http://**********:5001
[33mPress CTRL+C to quit[0m
 * Restarting with stat
 * Tip: There are .env files present. Install python-dotenv to use them.
 * Debugger is active!
 * Debugger PIN: 281-256-029
Fatal Python error: init_sys_streams: can't initialize sys standard streams
Python runtime state: core initialized
OSError: [Errno 9] Bad file descriptor

Current thread 0x00007ff84835ca00 (most recent call first):
  <no Python frame>
