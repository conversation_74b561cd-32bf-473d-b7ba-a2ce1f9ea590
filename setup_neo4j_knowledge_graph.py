# -*- coding: utf-8 -*-
"""
Neo4j知识图谱初始化脚本
根据PDF业务输入创建车辆配件和试验项目的知识图谱
"""

import json
import os
from neo4j import GraphDatabase
from neo4j_connector import Neo4jConnector

class Neo4jKnowledgeGraphSetup:
    """Neo4j知识图谱设置"""
    
    def __init__(self):
        self.neo4j_client = Neo4jConnector()
        self.driver = None
        
    def connect(self):
        """连接到Neo4j数据库"""
        try:
            self.driver = GraphDatabase.driver(
                self.neo4j_client.uri,
                auth=(self.neo4j_client.username, self.neo4j_client.password)
            )
            print("✅ 成功连接到Neo4j数据库")
            return True
        except Exception as e:
            print(f"❌ 连接Neo4j失败: {e}")
            return False
    
    def clear_database(self):
        """清空Neo4j数据库"""
        print("🧹 清空Neo4j数据库...")
        
        with self.driver.session() as session:
            # 删除所有关系
            session.run("MATCH ()-[r]-() DELETE r")
            # 删除所有节点
            session.run("MATCH (n) DELETE n")
            print("✅ Neo4j数据库已清空")
    
    def create_constraints_and_indexes(self):
        """创建约束和索引"""
        print("📋 创建Neo4j约束和索引...")
        
        with self.driver.session() as session:
            try:
                # 先删除可能存在的索引
                try:
                    session.run("DROP INDEX component_name_index IF EXISTS")
                    session.run("DROP INDEX test_project_name_index IF EXISTS")
                except:
                    pass
                
                # 为配件节点创建唯一约束
                session.run("CREATE CONSTRAINT component_name IF NOT EXISTS FOR (c:Component) REQUIRE c.name IS UNIQUE")
                
                # 为试验项目节点创建唯一约束
                session.run("CREATE CONSTRAINT test_project_name IF NOT EXISTS FOR (t:TestProject) REQUIRE t.name IS UNIQUE")
                
                print("✅ 约束和索引创建完成")
            except Exception as e:
                print(f"⚠️ 约束创建警告: {e}")
                print("继续执行...")
    
    def load_business_data(self):
        """加载PDF业务数据"""
        print("📖 加载PDF业务数据...")
        
        try:
            # 尝试不同路径
            data_paths = [
                "data/parsed_matrix_data.json",
                "parsed_matrix_data.json",
                os.path.join(os.path.dirname(__file__), "data", "parsed_matrix_data.json")
            ]
            
            for path in data_paths:
                try:
                    with open(path, "r", encoding='utf-8') as f:
                        data = json.load(f)
                        print(f"✅ 成功加载业务数据: {path}")
                        return data
                except FileNotFoundError:
                    continue
            
            raise FileNotFoundError("未找到业务数据文件")
            
        except Exception as e:
            print(f"❌ 加载业务数据失败: {e}")
            return None
    
    def create_knowledge_graph(self, matrix_data):
        """创建知识图谱三元组"""
        print("🕸️ 创建知识图谱三元组...")
        
        with self.driver.session() as session:
            # 1. 创建配件节点
            print("📦 创建配件节点...")
            components = matrix_data.get("components", [])
            for component in components:
                session.run(
                    "MERGE (c:Component {name: $name})",
                    name=component
                )
            print(f"✅ 创建了 {len(components)} 个配件节点")
            
            # 2. 创建试验项目节点（包含测试周期属性）
            print("🧪 创建试验项目节点...")
            test_projects = matrix_data.get("test_projects", [])
            test_cycles = matrix_data.get("test_cycles", {})
            
            for project in test_projects:
                test_cycle = test_cycles.get(project, 10)  # 默认10天
                session.run(
                    "MERGE (t:TestProject {name: $name, testCycle: $testCycle})",
                    name=project,
                    testCycle=test_cycle
                )
            print(f"✅ 创建了 {len(test_projects)} 个试验项目节点")
            
            # 3. 创建关系（只存储"✅"的关联）
            print("🔗 创建配件-试验项目关系...")
            relationships = matrix_data.get("relationships", [])
            created_relations = 0
            
            for rel in relationships:
                if rel["relationship"] == "✅":
                    component_name = rel["component"]
                    project_name = rel["test_project"]
                    is_required = rel.get("required", False)
                    
                    # 创建REQUIRES关系
                    relationship_type = "REQUIRES" if is_required else "USES"
                    session.run(f"""
                        MATCH (t:TestProject {{name: $project_name}})
                        MATCH (c:Component {{name: $component_name}})
                        MERGE (t)-[:{relationship_type}]->(c)
                    """, project_name=project_name, component_name=component_name)
                    
                    created_relations += 1
            
            print(f"✅ 创建了 {created_relations} 个关系")
    
    def verify_knowledge_graph(self):
        """验证知识图谱创建结果"""
        print("🔍 验证知识图谱...")
        
        with self.driver.session() as session:
            # 统计节点数量
            component_count = session.run("MATCH (c:Component) RETURN count(c) as count").single()["count"]
            project_count = session.run("MATCH (t:TestProject) RETURN count(t) as count").single()["count"]
            relation_count = session.run("MATCH ()-[r]-() RETURN count(r) as count").single()["count"]
            
            print(f"📊 知识图谱统计:")
            print(f"  - 配件节点: {component_count} 个")
            print(f"  - 试验项目节点: {project_count} 个")
            print(f"  - 关系: {relation_count} 个")
            
            # 展示一些示例查询结果
            print(f"\n🔍 示例查询结果:")
            
            # 查询所有配件
            result = session.run("MATCH (c:Component) RETURN c.name as name LIMIT 5")
            components = [record["name"] for record in result]
            print(f"  - 前5个配件: {components}")
            
            # 查询所有试验项目及其测试周期
            result = session.run("MATCH (t:TestProject) RETURN t.name as name, t.testCycle as cycle LIMIT 5")
            projects = [(record["name"], record["cycle"]) for record in result]
            print(f"  - 前5个试验项目及周期: {projects}")
            
            # 查询某个项目需要的配件
            result = session.run("""
                MATCH (t:TestProject {name: '感知开发'})-[r]->(c:Component)
                RETURN c.name as component, type(r) as relation_type
            """)
            dependencies = [(record["component"], record["relation_type"]) for record in result]
            print(f"  - '感知开发'项目依赖的配件: {dependencies}")
    
    def close(self):
        """关闭数据库连接"""
        if self.driver:
            self.driver.close()
            print("🔌 数据库连接已关闭")

def main():
    """主函数"""
    print("🚀 开始初始化Neo4j知识图谱...")
    
    # 初始化设置类
    setup = Neo4jKnowledgeGraphSetup()
    
    try:
        # 1. 连接数据库
        if not setup.connect():
            return False
        
        # 2. 清空数据库
        setup.clear_database()
        
        # 3. 创建约束和索引
        setup.create_constraints_and_indexes()
        
        # 4. 加载业务数据
        matrix_data = setup.load_business_data()
        if not matrix_data:
            return False
        
        # 5. 创建知识图谱
        setup.create_knowledge_graph(matrix_data)
        
        # 6. 验证结果
        setup.verify_knowledge_graph()
        
        print("🎉 Neo4j知识图谱初始化完成！")
        return True
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return False
    
    finally:
        setup.close()

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 知识图谱已准备就绪，可以开始使用DVP系统")
    else:
        print("\n❌ 知识图谱初始化失败，请检查配置和数据")