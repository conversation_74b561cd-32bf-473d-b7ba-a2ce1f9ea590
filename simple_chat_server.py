# -*- coding: utf-8 -*-
"""
简化版DVP聊天服务器
使用Python内置模块实现HTTP服务
"""

import json
import os
import re
import urllib.request
import urllib.parse
import urllib.error
from datetime import datetime, timedelta
from http.server import BaseHTTPRequestHandler, HTTPServer
import socketserver
import threading
import time

class DVPChatHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器"""
    
    def __init__(self, *args, **kwargs):
        self.chatbot = DVPChatBot()
        BaseHTTPRequestHandler.__init__(self, *args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/' or self.path == '/index.html':
            self.serve_file('chat_interface.html', 'text/html')
        elif self.path.endswith('.html'):
            self.serve_file(self.path[1:], 'text/html')
        elif self.path.endswith('.css'):
            self.serve_file(self.path[1:], 'text/css')
        elif self.path.endswith('.js'):
            self.serve_file(self.path[1:], 'application/javascript')
        else:
            self.send_error(404, 'File not found')
    
    def do_POST(self):
        """处理POST请求"""
        if self.path == '/api/chat':
            self.handle_chat_request()
        else:
            self.send_error(404, 'API not found')
    
    def handle_chat_request(self):
        """处理聊天请求"""
        try:
            # 读取请求数据
            content_length = int(self.headers.getheader('content-length', 0))
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data)
            
            # 处理聊天消息
            response = self.chatbot.process_message(data)
            
            # 发送响应
            self.send_response(200)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()
            
            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            
        except Exception as e:
            print("处理聊天请求错误: {}".format(str(e)))
            self.send_error(500, 'Internal server error')
    
    def do_OPTIONS(self):
        """处理CORS预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def serve_file(self, filename, content_type):
        """提供文件服务"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-Type', content_type + '; charset=utf-8')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
            
        except IOError:
            self.send_error(404, 'File not found: ' + filename)
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print("{} - {}".format(self.address_string(), format % args))

class DVPChatBot:
    """DVP聊天机器人（简化版）"""
    
    def __init__(self):
        self.api_key = "sk-nauixigqyamklriyoqzepwtxjtenumbehtucnjdtxtvloxbz"
        self.base_url = "https://api.siliconflow.cn/v1"
        self.knowledge_graph = self.load_knowledge_graph()
    
    def load_knowledge_graph(self):
        """加载知识图谱数据"""
        return {
            "test_projects": {
                "ACC自适应巡航控制": {
                    "test_cycle": 20,
                    "vehicle_baseline": 3,
                    "components": ["前摄像头", "前毫米波雷达", "ADAS控制器ECU"],
                    "complexity": "高",
                    "description": "需要长距离测试和多场景验证，考虑不同天气条件"
                },
                "AEB自动紧急制动": {
                    "test_cycle": 15,
                    "vehicle_baseline": 2,
                    "components": ["前摄像头", "前毫米波雷达", "ADAS控制器ECU"],
                    "complexity": "高",
                    "description": "需要高风险测试，需要备用车辆"
                },
                "BSD盲点监测": {
                    "test_cycle": 10,
                    "vehicle_baseline": 2,
                    "components": ["后摄像头", "左前毫米波雷达", "右前毫米波雷达", "ADAS控制器ECU"],
                    "complexity": "中",
                    "description": "需要多角度测试，考虑不同车型"
                },
                "LDW车道偏离警告": {
                    "test_cycle": 8,
                    "vehicle_baseline": 1,
                    "components": ["前摄像头", "ADAS控制器ECU"],
                    "complexity": "低",
                    "description": "相对简单功能，基础测试即可"
                },
                "FCW前方碰撞警告": {
                    "test_cycle": 10,
                    "vehicle_baseline": 2,
                    "components": ["前摄像头", "前毫米波雷达", "ADAS控制器ECU"],
                    "complexity": "中",
                    "description": "需要多场景测试，考虑不同速度"
                },
                "LKA车道保持辅助": {
                    "test_cycle": 12,
                    "vehicle_baseline": 2,
                    "components": ["前摄像头", "ADAS控制器ECU"],
                    "complexity": "中",
                    "description": "需要精确控制测试"
                }
            }
        }
    
    def call_llm(self, prompt, system_prompt=""):
        """调用LLM API"""
        url = self.base_url + "/chat/completions"
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        data = {
            "model": "deepseek-ai/DeepSeek-R1",
            "messages": messages,
            "max_tokens": 2000,
            "temperature": 0.7
        }
        
        try:
            headers = {
                "Authorization": "Bearer " + self.api_key,
                "Content-Type": "application/json"
            }
            data_bytes = json.dumps(data).encode('utf-8')
            req = urllib.request.Request(url, data_bytes, headers)
            
            with urllib.request.urlopen(req) as response:
                result = json.loads(response.read().decode('utf-8'))
            
            if "choices" in result and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                return "API调用失败"
                
        except Exception as e:
            print("LLM API调用错误: {}".format(str(e)))
            return "API调用失败，使用模拟推理结果"
    
    def process_message(self, data):
        """处理聊天消息"""
        try:
            user_message = data.get('message', '')
            state = data.get('state', {})
            parameters = data.get('parameters', {})
            
            # 分析用户输入
            analysis = self.analyze_input(user_message)
            
            # 查询知识图谱
            matched_projects = self.query_knowledge_graph(analysis.get('components', []))
            
            if state.get('step') == 'awaiting_parameters' and parameters:
                # 生成最终报告
                return self.generate_final_report(analysis, matched_projects, parameters)
            elif matched_projects:
                # 请求参数
                return {
                    "message": self.format_analysis_response(analysis, matched_projects),
                    "type": "parameter_request",
                    "data": {
                        "analysis": analysis,
                        "matched_projects": matched_projects,
                        "requires_parameters": True
                    }
                }
            else:
                # 需要更多信息
                return {
                    "message": "抱歉，我无法从您的输入中识别到足够的配置信息。请提供更详细的车型配置，包括具体的传感器和控制器组件。",
                    "type": "error",
                    "data": analysis
                }
                
        except Exception as e:
            print("处理消息错误: {}".format(str(e)))
            return {
                "message": "处理请求时出现错误，请稍后重试。",
                "type": "error"
            }
    
    def analyze_input(self, user_input):
        """分析用户输入"""
        components = []
        component_keywords = {
            '前摄像头': '前摄像头',
            '后摄像头': '后摄像头',
            '毫米波雷达': '前毫米波雷达',
            '前雷达': '前毫米波雷达',
            '后雷达': '后毫米波雷达',
            '激光雷达': '激光雷达',
            'ADAS控制器': 'ADAS控制器ECU',
            'ECU': 'ADAS控制器ECU',
            '控制器': 'ADAS控制器ECU'
        }
        
        for keyword, component in component_keywords.items():
            if keyword in user_input:
                components.append(component)
        
        # 如果提到ADAS，添加默认组件
        if 'ADAS' in user_input or '智能驾驶' in user_input:
            if '前摄像头' not in components:
                components.append('前摄像头')
            if 'ADAS控制器ECU' not in components:
                components.append('ADAS控制器ECU')
            if 'L2' in user_input and '前毫米波雷达' not in components:
                components.append('前毫米波雷达')
        
        # 提取SOP日期
        date_match = re.search(r'(\d{4})[年-](\d{1,2})[月-](\d{1,2})', user_input)
        sop_date = None
        if date_match:
            sop_date = "{}-{:02d}-{:02d}".format(
                int(date_match.group(1)),
                int(date_match.group(2)),
                int(date_match.group(3))
            )
        
        config_type = "ADAS基础配置"
        if "L2+" in user_input:
            config_type = "ADAS L2+配置"
        elif "L2" in user_input:
            config_type = "ADAS L2级配置"
        elif "高端" in user_input:
            config_type = "高端智能驾驶配置"
        
        return {
            "config_type": config_type,
            "sop_date": sop_date or "2024-12-31",
            "components": list(set(components)),  # 去重
            "confidence": 0.8 if components else 0.3
        }
    
    def query_knowledge_graph(self, components):
        """查询知识图谱"""
        matched_projects = []
        
        for project_name, project_info in self.knowledge_graph["test_projects"].items():
            required_components = project_info["components"]
            
            matched_comps = []
            for req_comp in required_components:
                if req_comp in components:
                    matched_comps.append(req_comp)
            
            if matched_comps:
                match_ratio = len(matched_comps) / len(required_components)
                matched_projects.append({
                    "project_name": project_name,
                    "match_ratio": match_ratio,
                    "matched_components": matched_comps,
                    "test_cycle": project_info["test_cycle"],
                    "vehicle_baseline": project_info["vehicle_baseline"],
                    "complexity": project_info["complexity"],
                    "description": project_info["description"]
                })
        
        matched_projects.sort(key=lambda x: x["match_ratio"], reverse=True)
        return matched_projects
    
    def format_analysis_response(self, analysis, matched_projects):
        """格式化分析响应"""
        response = """### 🔍 配置分析完成

我已分析您的配置并查询了知识图谱：

**配置类型**：{}
**SOP日期**：{}
**检测到的组件**：{}

### 📊 知识图谱匹配结果

**匹配的试验项目**：""".format(
            analysis["config_type"],
            analysis["sop_date"],
            "、".join(analysis["components"])
        )
        
        for i, project in enumerate(matched_projects[:6], 1):
            response += "\n{}. **{}** - 匹配度: {:.1%} (基线: {}台车, {}天)".format(
                i, project["project_name"], project["match_ratio"],
                project["vehicle_baseline"], project["test_cycle"]
            )
        
        response += "\n\n### 🤔 需要您确认测试参数\n\n请选择以下参数以便LLM进行精确推理计算："
        
        return response
    
    def generate_final_report(self, analysis, matched_projects, parameters):
        """生成最终报告"""
        # 使用LLM推理或备用算法计算车辆数量
        total_vehicles = self.calculate_vehicles(matched_projects, parameters)
        
        # 生成Markdown报告
        current_time = datetime.now()
        
        report = """# 🚗 DVP智能生成方案报告

## 📋 项目基本信息

| 项目 | 值 |
|------|-----|
| **方案编号** | DVP_AI_{} |
| **生成时间** | {} |
| **配置类型** | {} |
| **SOP日期** | {} |
| **LLM推理** | DeepSeek-R1 |

## 🔧 车辆配置分析

**识别的组件配置**：
{}

## 🧪 知识图谱匹配结果

{}

## 🚙 AI智能车辆计算

### LLM推理过程：

**步骤1：基础车辆需求**
- 基线车辆总计：{}台

**步骤2：复杂度调整 ({}级别)**
- 复杂度系数：{}
- 调整后：+{}台

**步骤3：环境因素调整 ({}环境)**
- 环境系数：{}
- 调整后：+{}台

**步骤4：并行优化**
- 可并行项目组：{}组
- 优化节省：-{}台

**步骤5：风险缓解**
- 备用车辆：+{}台

### 🎯 计算结果：
**所需车辆总数：{}台**

## 📅 智能测试排期

### 时间轴规划

| 阶段 | 项目 | 开始日期 | 结束日期 | 车辆分配 | 状态 |
|------|------|----------|----------|----------|------|{}

### 关键里程碑

- **项目启动**：{}
- **第1阶段完成**：{}
- **第2阶段完成**：{}
- **SOP准备就绪**：{}

## 💰 成本分析

| 成本项目 | 数量 | 单价 | 总计 |
|----------|------|------|------|
| 试验车辆 | {}台 | 18万元/台 | {}万元 |
| 测试运营 | {}个项目 | 8万元/项目 | {}万元 |
| 场地租赁 | {}天 | 2万元/天 | {}万元 |
| **总计** | - | - | **{}万元** |

## ⚠️ 风险评估与建议

### 主要风险点
1. **资源风险**：车辆数量需求{}，需提前协调
2. **时间风险**：测试周期{}天，需严格管控
3. **复杂度风险**：{}复杂度项目，需专家支持

### 缓解措施
1. 建立车辆资源池，确保供应充足
2. 设立项目管理办公室，强化进度控制
3. 引入专家团队，提升测试质量
4. 建立质量控制体系和应急预案

## 📊 执行建议

### 立即行动项
- [ ] 确认车辆资源分配
- [ ] 组建专项测试团队  
- [ ] 制定详细测试计划
- [ ] 建立质量控制体系

### 监控要点
- 每周进度评审
- 资源使用率监控
- 质量指标跟踪
- 风险预警机制

---
*本方案由DVP智能生成系统AI自动生成，集成DeepSeek-R1大模型推理和知识图谱查询*
        """.format(
            current_time.strftime("%Y%m%d%H%M"),
            current_time.strftime("%Y-%m-%d %H:%M:%S"),
            analysis["config_type"],
            analysis["sop_date"],
            "\n".join("- {}".format(comp) for comp in analysis["components"]),
            self.format_matched_projects_table(matched_projects),
            total_vehicles,
            self.generate_schedule_table(matched_projects, analysis["sop_date"])
            total_vehicles, total_vehicles * 18,
            len(matched_projects), len(matched_projects) * 8,
            45, 90,
            total_vehicles * 18 + len(matched_projects) * 8 + 90,
            "较高" if total_vehicles > 10 else "适中",
            sum(p["test_cycle"] for p in matched_projects),
            parameters.get("complexity", "标准")
        )
        
        return {
            "message": report,
            "type": "markdown_report",
            "data": {
                "analysis": analysis,
                "matched_projects": matched_projects,
                "total_vehicles": total_vehicles,
                "parameters": parameters
            }
        }
    
    def calculate_vehicles(self, matched_projects, parameters):
        """计算车辆数量"""
        if not matched_projects:
            return 0
        
        # 基础车辆
        base_vehicles = sum(p["vehicle_baseline"] for p in matched_projects)
        
        # 复杂度调整
        complexity_multiplier = {
            "basic": 1.0,
            "standard": 1.2, 
            "comprehensive": 1.5
        }.get(parameters.get("complexity", "standard"), 1.2)
        
        # 环境调整
        environment_adjustment = {
            "limited": 1,
            "normal": 2,
            "extreme": 4
        }.get(parameters.get("environment", "normal"), 2)
        
        # 并行优化
        parallel_groups = max(1, len(matched_projects) // 3)
        parallel_saving = parallel_groups - 1
        
        # 风险缓解
        risk_vehicles = max(2, base_vehicles // 4)
        
        total = int(base_vehicles * complexity_multiplier) + environment_adjustment + risk_vehicles - parallel_saving
        return max(total, len(matched_projects))
    
    def calculate_detailed_metrics(self, matched_projects, parameters, total_vehicles):
        """计算详细指标"""
        base_vehicles = sum(p["vehicle_baseline"] for p in matched_projects)
        complexity = parameters.get("complexity", "standard")
        environment = parameters.get("environment", "normal")
        
        complexity_multiplier = {"basic": 1.0, "standard": 1.2, "comprehensive": 1.5}[complexity]
        complexity_adjustment = int(base_vehicles * (complexity_multiplier - 1))
        
        environment_adjustment = {"limited": 1, "normal": 2, "extreme": 4}[environment]
        environment_multiplier = {"limited": 1.1, "normal": 1.2, "extreme": 1.4}[environment]
        
        parallel_groups = max(1, len(matched_projects) // 3)
        parallel_saving = parallel_groups - 1
        
        risk_vehicles = max(2, base_vehicles // 4)
        
        return [
            base_vehicles, complexity, complexity_multiplier, complexity_adjustment,
            environment, environment_multiplier, environment_adjustment,
            parallel_groups, parallel_saving, risk_vehicles
        ]
    
    def format_matched_projects_table(self, matched_projects):
        """格式化匹配项目表格"""
        if not matched_projects:
            return "未找到匹配的试验项目"
        
        result = ""
        for i, project in enumerate(matched_projects, 1):
            result += """
### {}. {}
- **匹配度**：{:.1%} 
- **测试周期**：{}天
- **基线车辆**：{}台
- **复杂度**：{}
- **匹配组件**：{}
- **项目描述**：{}
            """.format(
                i, project["project_name"], project["match_ratio"],
                project["test_cycle"], project["vehicle_baseline"],
                project["complexity"], "、".join(project["matched_components"]),
                project["description"]
            )
        
        return result
    
    def generate_schedule_table(self, matched_projects, sop_date):
        """生成排期表格"""
        if not matched_projects:
            return ""
        
        sop_datetime = datetime.strptime(sop_date, "%Y-%m-%d")
        total_duration = sum(p["test_cycle"] for p in matched_projects) // 2  # 并行优化
        start_date = sop_datetime - timedelta(days=total_duration + 45)
        
        schedule_rows = ""
        current_date = start_date
        
        for i, project in enumerate(matched_projects[:4], 1):  # 只显示前4个项目
            end_date = current_date + timedelta(days=project["test_cycle"])
            status = "🔴 关键路径" if i <= 2 else "🟡 并行执行"
            
            schedule_rows += "\n| 第{}阶段 | {} | {} | {} | {}台 | {} |".format(
                (i + 1) // 2, project["project_name"],
                current_date.strftime("%Y-%m-%d"),
                end_date.strftime("%Y-%m-%d"),
                project["vehicle_baseline"], status
            )
            
            if i % 2 == 0:  # 每两个项目进入下一阶段
                current_date = end_date + timedelta(days=3)
        
        return schedule_rows
    
    def generate_milestone_dates(self, sop_date):
        """生成里程碑日期"""
        sop_datetime = datetime.strptime(sop_date, "%Y-%m-%d")
        start_date = sop_datetime - timedelta(days=90)
        
        return [
            start_date.strftime("%Y-%m-%d"),
            (start_date + timedelta(days=30)).strftime("%Y-%m-%d"),
            (start_date + timedelta(days=60)).strftime("%Y-%m-%d"),
            sop_date
        ]

class ThreadedHTTPServer(SocketServer.ThreadingMixIn, BaseHTTPServer.HTTPServer):
    """多线程HTTP服务器"""
    allow_reuse_address = True

def run_server():
    """运行服务器"""
    server_address = ('localhost', 8000)
    
    print("启动DVP智能聊天系统...")
    print("服务器地址: http://localhost:8000")
    print("请打开浏览器访问上述地址")
    print("按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    httpd = ThreadedHTTPServer(server_address, DVPChatHandler)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
        httpd.shutdown()
        print("服务器已停止")

if __name__ == '__main__':
    run_server()