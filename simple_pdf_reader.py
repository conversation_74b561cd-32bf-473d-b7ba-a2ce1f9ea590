# -*- coding: utf-8 -*-
import os
import json

def read_pdf_info():
    """读取PDF文件信息"""
    pdf_path = "/Users/<USER>/Desktop/山创项目/01.理想DVP项目/02.DVP-AI 算法生成 POC/DVP生成—ADAS检测项目逻辑关系.pdf"
    
    if os.path.exists(pdf_path):
        file_size = os.path.getsize(pdf_path)
        print("PDF文件存在")
        print("文件大小: {} 字节".format(file_size))
        
        # 创建示例数据结构，等待PDF解析
        sample_data = {
            "adas_components": [
                "前摄像头",
                "后摄像头",
                "毫米波雷达",
                "激光雷达",
                "超声波传感器",
                "控制器ECU"
            ],
            "test_projects": [
                {
                    "project_name": "ACC自适应巡航",
                    "required_components": ["前摄像头", "毫米波雷达"],
                    "test_duration": 15,
                    "vehicle_count": 2
                },
                {
                    "project_name": "AEB自动紧急制动",
                    "required_components": ["前摄像头", "毫米波雷达"],
                    "test_duration": 10,
                    "vehicle_count": 1
                },
                {
                    "project_name": "BSD盲点监测",
                    "required_components": ["后摄像头", "毫米波雷达"],
                    "test_duration": 8,
                    "vehicle_count": 1
                }
            ],
            "vehicle_count_rules": [
                "单一功能测试：1台车",
                "组合功能测试：2台车",
                "长期耐久测试：3台车"
            ]
        }
        
        # 保存示例数据
        with open("adas_sample_data.json", "w") as f:
            json.dump(sample_data, f, ensure_ascii=False, indent=2)
        
        print("示例数据已保存到 adas_sample_data.json")
        return sample_data
    else:
        print("PDF文件不存在: {}".format(pdf_path))
        return None

if __name__ == "__main__":
    read_pdf_info()