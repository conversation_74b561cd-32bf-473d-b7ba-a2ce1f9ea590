# -*- coding: utf-8 -*-
"""
测试SiliconCloud API连接
"""

import json
import urllib.request
import urllib.parse
import urllib.error
import time
import os
from datetime import datetime

def load_env_config():
    """从.env文件加载配置"""
    config = {}
    try:
        with open('.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    config[key.strip()] = value.strip()
        return config
    except FileNotFoundError:
        print("❌ 未找到 .env 文件")
        return {}

def test_basic_connection():
    """测试基本API连接"""
    print("🔍 测试1: 基本API连接测试")
    print("=" * 50)

    # 从.env文件读取配置
    config = load_env_config()
    api_key = config.get('SILICONCLOUD_API_KEY', 'sk-nauixigqyamklriyoqzepwtxjtenumbehtucnjdtxtvloxbz')
    base_url = config.get('SILICONCLOUD_BASE_URL', 'https://api.siliconflow.cn/v1')

    print(f"📡 API Base URL: {base_url}")
    print(f"🔑 API Key: {api_key[:20]}...{api_key[-10:]}")

    url = f"{base_url}/chat/completions"

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    data = {
        "model": "deepseek-ai/DeepSeek-R1",
        "messages": [
            {"role": "user", "content": "请用中文简单介绍一下你自己，不超过50字。"}
        ],
        "max_tokens": 100,
        "temperature": 0.7
    }

    try:
        print("⏳ 正在测试API连接...")
        print(f"📍 请求URL: {url}")

        start_time = time.time()
        data_bytes = json.dumps(data).encode('utf-8')
        req = urllib.request.Request(url, data_bytes, headers)

        with urllib.request.urlopen(req, timeout=30) as response:
            response_time = time.time() - start_time
            result = json.loads(response.read().decode('utf-8'))

        print(f"✅ API连接成功!")
        print(f"⏱️ 响应时间: {response_time:.2f}秒")
        print(f"📊 HTTP状态码: {response.status}")

        if "choices" in result and len(result["choices"]) > 0:
            content = result["choices"][0]["message"]["content"]
            print(f"🤖 LLM响应: {content}")
            return True, content
        else:
            print("⚠️ API响应格式异常")
            print(f"📄 原始响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
            return False, "响应格式异常"

    except urllib.error.HTTPError as e:
        print(f"❌ HTTP错误: {e.code} - {e.reason}")
        try:
            error_body = e.read().decode('utf-8')
            print(f"📄 错误详情: {error_body}")
        except:
            pass
        return False, f"HTTP错误: {e.code}"

    except urllib.error.URLError as e:
        print(f"❌ 网络连接错误: {e.reason}")
        return False, f"网络错误: {e.reason}"

    except Exception as e:
        print(f"❌ 未知错误: {str(e)}")
        return False, f"未知错误: {str(e)}"

def test_different_models():
    """测试不同的模型"""
    print("\n🔍 测试2: 不同模型测试")
    print("=" * 50)

    config = load_env_config()
    api_key = config.get('SILICONCLOUD_API_KEY')
    base_url = config.get('SILICONCLOUD_BASE_URL', 'https://api.siliconflow.cn/v1')

    models_to_test = [
        "deepseek-ai/DeepSeek-R1",
        "deepseek-ai/deepseek-chat",
        "Qwen/Qwen2.5-72B-Instruct",
        "meta-llama/Llama-3.1-8B-Instruct"
    ]

    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    results = {}

    for model in models_to_test:
        print(f"\n🧪 测试模型: {model}")

        data = {
            "model": model,
            "messages": [
                {"role": "user", "content": "请说'你好'"}
            ],
            "max_tokens": 50,
            "temperature": 0.1
        }

        try:
            start_time = time.time()
            data_bytes = json.dumps(data).encode('utf-8')
            req = urllib.request.Request(url, data_bytes, headers)

            with urllib.request.urlopen(req, timeout=15) as response:
                response_time = time.time() - start_time
                result = json.loads(response.read().decode('utf-8'))

            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                print(f"✅ {model}: 成功 ({response_time:.2f}s)")
                print(f"   响应: {content[:50]}...")
                results[model] = {"status": "success", "time": response_time, "response": content}
            else:
                print(f"⚠️ {model}: 响应格式异常")
                results[model] = {"status": "format_error", "time": response_time}

        except Exception as e:
            print(f"❌ {model}: 失败 - {str(e)}")
            results[model] = {"status": "error", "error": str(e)}

    return results

def test_performance():
    """测试API性能"""
    print("\n🔍 测试3: API性能测试")
    print("=" * 50)

    config = load_env_config()
    api_key = config.get('SILICONCLOUD_API_KEY')
    base_url = config.get('SILICONCLOUD_BASE_URL', 'https://api.siliconflow.cn/v1')

    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # 测试不同长度的请求
    test_cases = [
        {"name": "短文本", "content": "你好", "max_tokens": 50},
        {"name": "中等文本", "content": "请详细介绍一下汽车DVP测试的基本流程和重要性", "max_tokens": 200},
        {"name": "长文本", "content": "请详细分析汽车ADAS系统的DVP测试策略，包括测试项目规划、车辆资源分配、测试排期安排等各个方面，并提供具体的实施建议", "max_tokens": 500}
    ]

    performance_results = {}

    for test_case in test_cases:
        print(f"\n🧪 测试场景: {test_case['name']}")

        data = {
            "model": "deepseek-ai/DeepSeek-R1",
            "messages": [
                {"role": "user", "content": test_case["content"]}
            ],
            "max_tokens": test_case["max_tokens"],
            "temperature": 0.7
        }

        try:
            start_time = time.time()
            data_bytes = json.dumps(data).encode('utf-8')
            req = urllib.request.Request(url, data_bytes, headers)

            with urllib.request.urlopen(req, timeout=60) as response:
                response_time = time.time() - start_time
                result = json.loads(response.read().decode('utf-8'))

            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                tokens_used = result.get("usage", {}).get("total_tokens", 0)

                print(f"✅ 响应时间: {response_time:.2f}秒")
                print(f"📊 Token使用: {tokens_used}")
                print(f"📝 响应长度: {len(content)}字符")
                print(f"🔤 响应预览: {content[:100]}...")

                performance_results[test_case['name']] = {
                    "response_time": response_time,
                    "tokens_used": tokens_used,
                    "response_length": len(content),
                    "status": "success"
                }
            else:
                print(f"⚠️ 响应格式异常")
                performance_results[test_case['name']] = {"status": "format_error"}

        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            performance_results[test_case['name']] = {"status": "error", "error": str(e)}

    return performance_results

def test_dvp_specific_prompts():
    """测试DVP相关的特定提示"""
    print("\n🔍 测试4: DVP专业场景测试")
    print("=" * 50)

    config = load_env_config()
    api_key = config.get('SILICONCLOUD_API_KEY')
    base_url = config.get('SILICONCLOUD_BASE_URL', 'https://api.siliconflow.cn/v1')

    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    dvp_prompts = [
        {
            "name": "车辆数量计算",
            "prompt": """
作为汽车DVP测试专家，请根据以下测试项目计算所需车辆数量：
- ACC自适应巡航测试：15天，需要前摄像头、毫米波雷达
- AEB自动紧急制动测试：10天，需要前摄像头、毫米波雷达
- LKA车道保持辅助测试：12天，需要前摄像头

请返回JSON格式：{"totalVehicles": 数字, "reasoning": "推理过程"}
"""
        },
        {
            "name": "测试排期规划",
            "prompt": """
请为以下ADAS测试项目制定排期计划：
项目1：FCW前方碰撞警告测试，10天
项目2：BSD盲点监测测试，8天
项目3：APA自动泊车测试，15天
总车辆数：5台，SOP日期：2025-12-31

请返回详细的排期建议。
"""
        }
    ]

    dvp_results = {}

    for test_prompt in dvp_prompts:
        print(f"\n🧪 测试场景: {test_prompt['name']}")

        data = {
            "model": "deepseek-ai/DeepSeek-R1",
            "messages": [
                {"role": "user", "content": test_prompt["prompt"]}
            ],
            "max_tokens": 800,
            "temperature": 0.3
        }

        try:
            start_time = time.time()
            data_bytes = json.dumps(data).encode('utf-8')
            req = urllib.request.Request(url, data_bytes, headers)

            with urllib.request.urlopen(req, timeout=45) as response:
                response_time = time.time() - start_time
                result = json.loads(response.read().decode('utf-8'))

            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                print(f"✅ 响应时间: {response_time:.2f}秒")
                print(f"🤖 LLM分析结果:")
                print("-" * 40)
                print(content)
                print("-" * 40)

                dvp_results[test_prompt['name']] = {
                    "response_time": response_time,
                    "response": content,
                    "status": "success"
                }
            else:
                print(f"⚠️ 响应格式异常")
                dvp_results[test_prompt['name']] = {"status": "format_error"}

        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            dvp_results[test_prompt['name']] = {"status": "error", "error": str(e)}

    return dvp_results

def main():
    """主测试函数"""
    print("🚀 SiliconCloud LLM API 连接测试")
    print("=" * 60)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 检查.env文件
    if not os.path.exists('.env'):
        print("❌ 未找到.env文件，请确保配置文件存在")
        return

    # 执行各项测试
    test_results = {}

    # 测试1: 基本连接
    success, response = test_basic_connection()
    test_results['basic_connection'] = {'success': success, 'response': response}

    if success:
        # 测试2: 不同模型
        test_results['model_tests'] = test_different_models()

        # 测试3: 性能测试
        test_results['performance_tests'] = test_performance()

        # 测试4: DVP专业测试
        test_results['dvp_tests'] = test_dvp_specific_prompts()

    # 生成测试报告
    print("\n📊 测试总结报告")
    print("=" * 60)

    if test_results['basic_connection']['success']:
        print("✅ 基本连接: 成功")

        if 'model_tests' in test_results:
            successful_models = sum(1 for r in test_results['model_tests'].values() if r.get('status') == 'success')
            total_models = len(test_results['model_tests'])
            print(f"🧪 模型测试: {successful_models}/{total_models} 成功")

        if 'performance_tests' in test_results:
            successful_perf = sum(1 for r in test_results['performance_tests'].values() if r.get('status') == 'success')
            total_perf = len(test_results['performance_tests'])
            print(f"⚡ 性能测试: {successful_perf}/{total_perf} 成功")

        if 'dvp_tests' in test_results:
            successful_dvp = sum(1 for r in test_results['dvp_tests'].values() if r.get('status') == 'success')
            total_dvp = len(test_results['dvp_tests'])
            print(f"🚗 DVP专业测试: {successful_dvp}/{total_dvp} 成功")

        print("\n🎉 LLM API连接测试完成！系统可以正常使用。")
    else:
        print("❌ 基本连接: 失败")
        print(f"   错误信息: {test_results['basic_connection']['response']}")
        print("\n⚠️ 请检查网络连接和API配置")

    # 保存测试结果
    try:
        with open('llm_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
        print(f"\n📄 详细测试结果已保存到: llm_test_results.json")
    except Exception as e:
        print(f"\n⚠️ 保存测试结果失败: {e}")

if __name__ == "__main__":
    main()