# -*- coding: utf-8 -*-
"""
测试SiliconCloud API连接
"""

import json
import urllib.request
import urllib.parse

def test_api_connection():
    """测试API连接"""
    api_key = "sk-nauixigqyamklriyoqzepwtxjtenumbehtucnjdtxtvloxbz"
    url = "https://api.siliconflow.cn/v1/chat/completions"
    
    headers = {
        "Authorization": "Bearer " + api_key,
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "deepseek-ai/DeepSeek-R1",
        "messages": [
            {"role": "user", "content": "Hello, please respond in Chinese."}
        ],
        "max_tokens": 100
    }
    
    try:
        print("正在测试API连接...")
        print("URL: {}".format(url))
        print("API Key: {}...".format(api_key[:20]))
        
        # 创建请求
        data_bytes = json.dumps(data).encode('utf-8')
        req = urllib.request.Request(url, data_bytes, headers)
        
        # 发送请求
        with urllib.request.urlopen(req) as response:
            result = json.loads(response.read().decode('utf-8'))
        
        print("API调用成功!")
        print("Response: {}".format(result))
        
    except urllib.error.HTTPError as e:
        print("HTTP错误: {}".format(e.code))
        print("错误详情: {}".format(e.read()))
    except Exception as e:
        print("其他错误: {}".format(str(e)))

if __name__ == "__main__":
    test_api_connection()