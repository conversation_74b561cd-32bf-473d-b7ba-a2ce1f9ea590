# -*- coding: utf-8 -*-
"""
测试项目复杂度分布饼图移除效果
"""

import json
import urllib.request
import urllib.parse
import urllib.error
import time

def test_dvp_generation_without_chart():
    """测试DVP生成，验证复杂度图表已移除"""
    print("🧪 测试项目复杂度分布饼图移除效果")
    print("=" * 50)
    
    # 测试配置
    test_config = {
        "configType": "ADAS L2级配置",
        "startDate": "2025-01-08",
        "sopDate": "2025-07-08",
        "components": ["AD摄像头前视/后视/侧视", "毫米波雷达", "EPS"],
        "complexity": "standard",
        "environment": "normal", 
        "priority": "normal"
    }
    
    api_url = "http://localhost:5002/api/generate-dvp"
    headers = {"Content-Type": "application/json"}
    
    try:
        print(f"📋 测试配置: {test_config['configType']}")
        print(f"🔧 组件: {', '.join(test_config['components'])}")
        print("⏳ 发送DVP生成请求...")
        
        data_bytes = json.dumps(test_config).encode('utf-8')
        req = urllib.request.Request(api_url, data_bytes, headers)
        
        start_time = time.time()
        
        with urllib.request.urlopen(req, timeout=90) as response:
            response_time = time.time() - start_time
            result = json.loads(response.read().decode('utf-8'))
        
        if result.get('success'):
            data = result['data']
            print(f"✅ DVP生成成功!")
            print(f"⏱️ 响应时间: {response_time:.2f}秒")
            print(f"📋 方案ID: {data['id']}")
            print(f"🚗 车辆数量: {data['summary']['totalVehicles']}台")
            print(f"📊 匹配项目: {data['summary']['totalProjects']}个")
            print(f"⏰ 测试周期: {data['summary']['totalDuration']}天")
            
            print(f"\n📋 匹配的测试项目:")
            for i, project in enumerate(data['matchedProjects'][:3], 1):
                print(f"  {i}. {project['name']}")
                print(f"     - 匹配度: {project['matchRatio']*100:.1f}%")
                print(f"     - 复杂度: {project.get('complexity', '未定义')}")
                print(f"     - 测试周期: {project['testCycle']}天")
            
            print(f"\n✅ 测试完成！")
            print(f"📊 现在界面中应该只显示:")
            print(f"   - ✅ 关键指标卡片（车辆数量、测试周期、匹配项目）")
            print(f"   - ✅ 甘特图时间轴")
            print(f"   - ✅ 项目详情表格")
            print(f"   - ❌ 项目复杂度分布饼图（已移除）")
            return True
        else:
            print(f"❌ DVP生成失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 项目复杂度分布饼图移除验证")
    print("=" * 60)
    print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    print("🔧 移除内容:")
    print("  1. HTML中的复杂度图表容器")
    print("  2. CSS中的图表相关样式")
    print("  3. JavaScript中的complexityChart变量")
    print("  4. updateComplexityChart函数")
    print("  5. updateCharts函数调用")
    print()
    
    print("📝 移除原因:")
    print("  - 项目复杂度分布饼图没有真实的数据源支撑")
    print("  - 复杂度数据是模拟生成的，不反映真实业务逻辑")
    print("  - 用户更关心具体的项目信息和时间安排")
    print("  - 简化界面，突出核心功能")
    print()
    
    success = test_dvp_generation_without_chart()
    
    print("\n📊 移除验证结果:")
    if success:
        print("✅ DVP生成功能正常工作")
        print("✅ 界面中不再显示复杂度分布饼图")
        print("✅ 其他可视化组件正常显示")
        print("🎯 建议在浏览器中验证界面效果")
    else:
        print("❌ 测试过程中遇到问题")
    
    print("\n💡 界面优化效果:")
    print("  - 🎯 更聚焦于核心数据展示")
    print("  - 📊 保留有真实数据支撑的可视化")
    print("  - 🚀 提升界面加载性能")
    print("  - 💼 更符合实际业务需求")

if __name__ == "__main__":
    main()
