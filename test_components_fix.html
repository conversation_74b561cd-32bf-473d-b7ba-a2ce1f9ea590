<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>组件收集测试</title>
</head>
<body>
    <h1>组件收集逻辑测试</h1>
    
    <div id="componentsGrid">
        <div class="component-item">
            <input type="checkbox" id="comp1" value="FSD变更">
            <label for="comp1">FSD变更</label>
        </div>
        <div class="component-item">
            <input type="checkbox" id="comp2" value="激光雷达">
            <label for="comp2">激光雷达</label>
        </div>
        <div class="component-item">
            <input type="checkbox" id="comp3" value="AD摄像头前视/后视/侧视">
            <label for="comp3">AD摄像头前视/后视/侧视</label>
        </div>
        <div class="component-item">
            <input type="checkbox" id="comp4" value="环视摄像头">
            <label for="comp4">环视摄像头</label>
        </div>
    </div>
    
    <button onclick="testComponentCollection()">测试组件收集</button>
    <div id="result"></div>
    
    <script>
        function testComponentCollection() {
            // 模拟用户选择
            document.getElementById('comp1').checked = true;
            document.getElementById('comp3').checked = true;
            
            // 收集选中的组件
            const selectedComponents = [];
            document.querySelectorAll('.component-item input:checked').forEach(checkbox => {
                selectedComponents.push(checkbox.value);
            });
            
            document.getElementById('result').innerHTML = `
                <h3>收集到的组件：</h3>
                <pre>${JSON.stringify(selectedComponents, null, 2)}</pre>
                <p>预期结果：应该显示 ["FSD变更", "AD摄像头前视/后视/侧视"]</p>
            `;
            
            console.log('收集到的组件:', selectedComponents);
        }
    </script>
</body>
</html>