# -*- coding: utf-8 -*-
"""
测试 DVP API 的修复效果
"""

import json
import urllib.request
import urllib.parse
import urllib.error
import time

def test_dvp_generation():
    """测试 DVP 生成 API"""
    print("🧪 测试 DVP 生成 API")
    print("=" * 50)
    
    # API 配置
    api_url = "http://localhost:5002/api/generate-dvp"
    
    # 测试数据
    test_config = {
        "configType": "ADAS L2级配置",
        "startDate": "2025-01-08",
        "sopDate": "2025-07-08",
        "components": ["AD摄像头前视/后视/侧视", "毫米波雷达"],
        "complexity": "standard",
        "environment": "normal", 
        "priority": "normal"
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        print(f"📡 发送请求到: {api_url}")
        print(f"📋 测试配置: {test_config['configType']}")
        print(f"📅 时间窗口: {test_config['startDate']} → {test_config['sopDate']}")
        print(f"🔧 组件: {', '.join(test_config['components'])}")
        
        # 发送请求
        data_bytes = json.dumps(test_config).encode('utf-8')
        req = urllib.request.Request(api_url, data_bytes, headers)
        
        start_time = time.time()
        
        with urllib.request.urlopen(req, timeout=120) as response:
            response_time = time.time() - start_time
            result = json.loads(response.read().decode('utf-8'))
        
        print(f"✅ API 调用成功!")
        print(f"⏱️ 响应时间: {response_time:.2f}秒")
        print(f"📊 HTTP状态码: {response.status}")
        
        if result.get('success'):
            data = result['data']
            print(f"\n🎉 DVP 生成成功!")
            print(f"📋 方案ID: {data['id']}")
            print(f"🚗 车辆数量: {data['summary']['totalVehicles']}台")
            print(f"📊 匹配项目: {data['summary']['totalProjects']}个")
            print(f"⏰ 测试周期: {data['summary']['totalDuration']}天")
            print(f"⚠️ 风险等级: {data['summary']['riskLevel']}")
            
            # 显示匹配的项目
            print(f"\n📋 匹配的测试项目:")
            for i, project in enumerate(data['matchedProjects'][:5], 1):  # 只显示前5个
                print(f"  {i}. {project['name']}")
                print(f"     - 匹配度: {project['matchRatio']*100:.1f}%")
                print(f"     - 测试周期: {project['testCycle']}天")
                print(f"     - 复杂度: {project['complexity']}")
            
            if len(data['matchedProjects']) > 5:
                print(f"     ... 还有 {len(data['matchedProjects']) - 5} 个项目")
            
            return True, data
        else:
            print(f"❌ DVP 生成失败: {result.get('error', '未知错误')}")
            return False, result.get('error')
            
    except urllib.error.HTTPError as e:
        print(f"❌ HTTP错误: {e.code} - {e.reason}")
        try:
            error_body = e.read().decode('utf-8')
            error_data = json.loads(error_body)
            print(f"📄 错误详情: {error_data.get('error', error_body)}")
        except:
            print(f"📄 错误详情: 无法解析错误响应")
        return False, f"HTTP {e.code}"
        
    except urllib.error.URLError as e:
        print(f"❌ 网络连接错误: {e.reason}")
        return False, f"网络错误: {e.reason}"
        
    except Exception as e:
        print(f"❌ 未知错误: {str(e)}")
        return False, f"未知错误: {str(e)}"

def test_api_endpoints():
    """测试各个 API 端点"""
    print("\n🔍 测试各个 API 端点")
    print("=" * 50)
    
    endpoints = [
        ("测试连接", "http://localhost:5002/api/test-connection"),
        ("获取配件", "http://localhost:5002/api/get-components"),
        ("获取测试项目", "http://localhost:5002/api/get-test-projects"),
    ]
    
    results = {}
    
    for name, url in endpoints:
        try:
            print(f"\n🧪 测试 {name}: {url}")
            
            start_time = time.time()
            with urllib.request.urlopen(url, timeout=30) as response:
                response_time = time.time() - start_time
                result = json.loads(response.read().decode('utf-8'))
            
            if result.get('success'):
                print(f"✅ {name}: 成功 ({response_time:.2f}s)")
                if 'components' in result:
                    print(f"   📦 配件数量: {len(result['components'])}")
                elif 'projects' in result:
                    print(f"   📋 项目数量: {len(result['projects'])}")
                elif 'siliconcloud' in result:
                    print(f"   🤖 LLM连接: {'正常' if result['siliconcloud'] else '异常'}")
                    print(f"   🗄️ Neo4j连接: {'正常' if result['neo4j'] else '异常'}")
                    print(f"   📊 矩阵数据: {'已加载' if result['matrixData'] else '未加载'}")
                
                results[name] = True
            else:
                print(f"❌ {name}: 失败 - {result.get('error', '未知错误')}")
                results[name] = False
                
        except Exception as e:
            print(f"❌ {name}: 异常 - {str(e)}")
            results[name] = False
    
    return results

def main():
    """主测试函数"""
    print("🚀 DVP API 修复验证测试")
    print("=" * 60)
    print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试各个端点
    endpoint_results = test_api_endpoints()
    
    # 测试 DVP 生成
    dvp_success, dvp_result = test_dvp_generation()
    
    # 生成测试报告
    print("\n📊 测试总结报告")
    print("=" * 60)
    
    print("🔗 API 端点测试:")
    for endpoint, success in endpoint_results.items():
        status = "✅ 正常" if success else "❌ 异常"
        print(f"  - {endpoint}: {status}")
    
    print(f"\n🚗 DVP 生成测试:")
    if dvp_success:
        print("  ✅ DVP 生成: 成功")
        print("  🎉 500 错误已修复!")
    else:
        print(f"  ❌ DVP 生成: 失败 - {dvp_result}")
    
    # 总体评估
    total_tests = len(endpoint_results) + 1
    passed_tests = sum(endpoint_results.values()) + (1 if dvp_success else 0)
    
    print(f"\n📈 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！系统已修复并可正常使用。")
    elif dvp_success:
        print("✅ 主要功能正常！DVP 生成已修复。")
    else:
        print("⚠️ 仍有问题需要解决。")

if __name__ == "__main__":
    main()
