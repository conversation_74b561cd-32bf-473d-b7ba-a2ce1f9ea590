# -*- coding: utf-8 -*-
"""
简单的 DVP 生成测试
"""

import json
import urllib.request
import urllib.parse
import urllib.error

def test_dvp_generation():
    """测试 DVP 生成"""
    print("🧪 测试 DVP 生成功能")
    print("=" * 50)
    
    # 简单的测试配置
    test_config = {
        "configType": "ADAS基础配置",
        "startDate": "2025-01-08",
        "sopDate": "2025-07-08",
        "components": ["前摄像头"],
        "complexity": "standard",
        "environment": "normal", 
        "priority": "normal"
    }
    
    api_url = "http://localhost:5002/api/generate-dvp"
    headers = {"Content-Type": "application/json"}
    
    try:
        print(f"📋 测试配置: {test_config['configType']}")
        print(f"🔧 组件: {', '.join(test_config['components'])}")
        
        data_bytes = json.dumps(test_config).encode('utf-8')
        req = urllib.request.Request(api_url, data_bytes, headers)
        
        print("⏳ 发送请求...")
        
        with urllib.request.urlopen(req, timeout=60) as response:
            result = json.loads(response.read().decode('utf-8'))
        
        if result.get('success'):
            data = result['data']
            print(f"✅ DVP 生成成功!")
            print(f"📋 方案ID: {data['id']}")
            print(f"🚗 车辆数量: {data['summary']['totalVehicles']}台")
            print(f"📊 匹配项目: {data['summary']['totalProjects']}个")
            print(f"⏰ 测试周期: {data['summary']['totalDuration']}天")
            return True
        else:
            print(f"❌ DVP 生成失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_dvp_generation()
