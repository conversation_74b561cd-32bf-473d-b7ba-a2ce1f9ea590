<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进度追踪UI测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        
        .test-controls {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-btn {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 14px;
        }
        
        .test-btn:hover {
            background: #3730a3;
        }
        
        /* 复制进度追踪样式 */
        .progress-pipeline {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 24px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .pipeline-header {
            text-align: center;
            margin-bottom: 24px;
        }

        .pipeline-header h3 {
            color: white;
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: bold;
        }

        .pipeline-subtitle {
            color: rgba(255,255,255,0.8);
            font-size: 14px;
        }

        .pipeline-track {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 8px;
        }

        .pipeline-stage {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 120px;
            padding: 16px 12px;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
        }

        .pipeline-stage.active {
            background: rgba(255,255,255,0.2);
            border-color: #fbbf24;
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(251,191,36,0.3);
        }

        .pipeline-stage.completed {
            background: rgba(34,197,94,0.2);
            border-color: #22c55e;
        }

        .pipeline-stage.error {
            background: rgba(239,68,68,0.2);
            border-color: #ef4444;
        }

        .stage-icon {
            font-size: 24px;
            margin-bottom: 8px;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        }

        .stage-label {
            color: white;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 4px;
            line-height: 1.2;
        }

        .stage-status {
            color: rgba(255,255,255,0.7);
            font-size: 10px;
            text-align: center;
        }

        .pipeline-stage.active .stage-status {
            color: #fbbf24;
            font-weight: bold;
        }

        .pipeline-stage.completed .stage-status {
            color: #22c55e;
            font-weight: bold;
        }

        .pipeline-stage.error .stage-status {
            color: #ef4444;
            font-weight: bold;
        }

        .pipeline-connector {
            flex: 1;
            height: 3px;
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
            position: relative;
            margin: 0 8px;
            min-width: 20px;
        }

        .pipeline-connector.active {
            background: linear-gradient(90deg, #22c55e 0%, #fbbf24 100%);
            animation: flow 2s ease-in-out infinite;
        }

        @keyframes flow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 终端样式 */
        .terminal-background {
            background: rgba(0,0,0,0.9);
            border-radius: 12px;
            overflow: hidden;
            margin-top: 20px;
        }

        .terminal-header {
            background: #1f2937;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #374151;
        }

        .terminal-title {
            color: #10b981;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            font-weight: bold;
        }

        .terminal-time {
            color: #6b7280;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .terminal-content {
            padding: 16px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }

        .log-line {
            color: #10b981;
            margin-bottom: 4px;
            opacity: 0.8;
            animation: fadeIn 0.5s ease-in;
        }

        .log-line.error {
            color: #ef4444;
        }

        .log-line.warning {
            color: #f59e0b;
        }

        .log-line.info {
            color: #3b82f6;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 0.8; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 DVP进度追踪UI测试</h1>
        
        <div class="test-controls">
            <button class="test-btn" onclick="startDemo()">开始演示</button>
            <button class="test-btn" onclick="resetDemo()">重置</button>
            <button class="test-btn" onclick="simulateError()">模拟错误</button>
        </div>
        
        <!-- 进度轨道图 -->
        <div class="progress-pipeline">
            <div class="pipeline-header">
                <h3>🚀 DVP智能生成流水线</h3>
                <div class="pipeline-subtitle">AI驱动的汽车设计验证计划生成</div>
            </div>
            <div class="pipeline-track">
                <div class="pipeline-stage" data-stage="init">
                    <div class="stage-icon">🔧</div>
                    <div class="stage-label">初始化配置</div>
                    <div class="stage-status">待处理</div>
                </div>
                <div class="pipeline-connector"></div>
                <div class="pipeline-stage" data-stage="neo4j">
                    <div class="stage-icon">🗄️</div>
                    <div class="stage-label">Neo4j知识图谱</div>
                    <div class="stage-status">待处理</div>
                </div>
                <div class="pipeline-connector"></div>
                <div class="pipeline-stage" data-stage="llm">
                    <div class="stage-icon">🧠</div>
                    <div class="stage-label">DeepSeek-R1大模型</div>
                    <div class="stage-status">待处理</div>
                </div>
                <div class="pipeline-connector"></div>
                <div class="pipeline-stage" data-stage="calculation">
                    <div class="stage-icon">📊</div>
                    <div class="stage-label">车辆计算</div>
                    <div class="stage-status">待处理</div>
                </div>
                <div class="pipeline-connector"></div>
                <div class="pipeline-stage" data-stage="schedule">
                    <div class="stage-icon">📅</div>
                    <div class="stage-label">排期生成</div>
                    <div class="stage-status">待处理</div>
                </div>
                <div class="pipeline-connector"></div>
                <div class="pipeline-stage" data-stage="complete">
                    <div class="stage-icon">✅</div>
                    <div class="stage-label">方案完成</div>
                    <div class="stage-status">待处理</div>
                </div>
            </div>
        </div>
        
        <!-- 终端日志 -->
        <div class="terminal-background">
            <div class="terminal-header">
                <div class="terminal-title">🖥️ DVP-AI 系统日志</div>
                <div class="terminal-time" id="terminalTime"></div>
            </div>
            <div class="terminal-content" id="terminalLogs">
                <div class="log-line">系统初始化完成...</div>
                <div class="log-line">等待用户请求...</div>
            </div>
        </div>
    </div>

    <script>
        // 进度阶段配置
        const pipelineStages = [
            { id: 'init', name: '初始化配置', duration: 800 },
            { id: 'neo4j', name: 'Neo4j知识图谱', duration: 1500 },
            { id: 'llm', name: 'DeepSeek-R1大模型', duration: 2500 },
            { id: 'calculation', name: '车辆计算', duration: 1000 },
            { id: 'schedule', name: '排期生成', duration: 1200 },
            { id: 'complete', name: '方案完成', duration: 500 }
        ];

        // 更新进度阶段状态
        function updatePipelineStage(stageId, status, message = '') {
            const stage = document.querySelector(`[data-stage="${stageId}"]`);
            if (!stage) return;

            stage.classList.remove('active', 'completed', 'error');
            
            if (status !== 'pending') {
                stage.classList.add(status);
            }

            const statusElement = stage.querySelector('.stage-status');
            const statusTexts = {
                'pending': '待处理',
                'active': '处理中...',
                'completed': '已完成',
                'error': '失败'
            };
            
            statusElement.textContent = message || statusTexts[status] || '待处理';
            updateConnectors(stageId, status);
        }

        // 更新连接器状态
        function updateConnectors(currentStageId, status) {
            const currentIndex = pipelineStages.findIndex(s => s.id === currentStageId);
            if (currentIndex === -1) return;

            const connectors = document.querySelectorAll('.pipeline-connector');
            
            if (status === 'completed' && currentIndex < connectors.length) {
                connectors[currentIndex].classList.add('active');
            }
        }

        // 添加终端日志
        function addTerminalLog(message, type = 'info') {
            const terminalLogs = document.getElementById('terminalLogs');
            if (!terminalLogs) return;

            const logLine = document.createElement('div');
            logLine.className = `log-line ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            logLine.textContent = `[${timestamp}] ${message}`;
            
            terminalLogs.appendChild(logLine);
            terminalLogs.scrollTop = terminalLogs.scrollHeight;
            
            const logLines = terminalLogs.querySelectorAll('.log-line');
            if (logLines.length > 50) {
                logLines[0].remove();
            }
        }

        // 更新终端时间
        function updateTerminalTime() {
            const terminalTime = document.getElementById('terminalTime');
            if (terminalTime) {
                terminalTime.textContent = new Date().toLocaleString();
            }
        }

        // 开始演示
        async function startDemo() {
            updateTerminalTime();
            setInterval(updateTerminalTime, 1000);

            addTerminalLog('🚀 DVP生成流程启动', 'info');
            addTerminalLog('📋 用户配置验证通过', 'info');

            for (let i = 0; i < pipelineStages.length; i++) {
                const stage = pipelineStages[i];
                
                updatePipelineStage(stage.id, 'active');
                addTerminalLog(`▶️ 开始执行: ${stage.name}`, 'info');
                
                await new Promise(resolve => setTimeout(resolve, stage.duration));
                
                updatePipelineStage(stage.id, 'completed');
                addTerminalLog(`✅ 完成: ${stage.name}`, 'info');
            }

            addTerminalLog('🎉 DVP方案生成完成', 'info');
        }

        // 重置演示
        function resetDemo() {
            pipelineStages.forEach(stage => {
                updatePipelineStage(stage.id, 'pending');
            });
            
            document.querySelectorAll('.pipeline-connector').forEach(connector => {
                connector.classList.remove('active');
            });
            
            const terminalLogs = document.getElementById('terminalLogs');
            if (terminalLogs) {
                terminalLogs.innerHTML = `
                    <div class="log-line">系统初始化完成...</div>
                    <div class="log-line">等待用户请求...</div>
                `;
            }
        }

        // 模拟错误
        async function simulateError() {
            resetDemo();
            updateTerminalTime();
            
            addTerminalLog('🚀 DVP生成流程启动', 'info');
            addTerminalLog('📋 用户配置验证通过', 'info');

            for (let i = 0; i < 3; i++) {
                const stage = pipelineStages[i];
                
                updatePipelineStage(stage.id, 'active');
                addTerminalLog(`▶️ 开始执行: ${stage.name}`, 'info');
                
                await new Promise(resolve => setTimeout(resolve, stage.duration));
                
                if (i === 2) { // 在LLM阶段模拟错误
                    updatePipelineStage(stage.id, 'error', '连接超时');
                    addTerminalLog(`❌ 错误: ${stage.name} - 连接超时`, 'error');
                    addTerminalLog('💥 系统错误: DeepSeek-R1模型连接失败', 'error');
                    break;
                } else {
                    updatePipelineStage(stage.id, 'completed');
                    addTerminalLog(`✅ 完成: ${stage.name}`, 'info');
                }
            }
        }

        // 初始化
        updateTerminalTime();
        setInterval(updateTerminalTime, 1000);
    </script>
</body>
</html>
