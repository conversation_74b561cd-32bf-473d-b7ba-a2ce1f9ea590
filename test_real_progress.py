# -*- coding: utf-8 -*-
"""
测试真实进度追踪的效果
"""

import json
import urllib.request
import urllib.parse
import urllib.error
import time

def test_real_dvp_generation():
    """测试真实的 DVP 生成进度追踪"""
    print("🧪 测试真实 DVP 生成进度追踪")
    print("=" * 50)
    
    # 真实的测试配置
    test_config = {
        "configType": "ADAS L2级配置",
        "startDate": "2025-01-08",
        "sopDate": "2025-07-08",
        "components": ["AD摄像头前视/后视/侧视", "毫米波雷达"],
        "complexity": "standard",
        "environment": "normal", 
        "priority": "normal"
    }
    
    api_url = "http://localhost:5002/api/generate-dvp"
    headers = {"Content-Type": "application/json"}
    
    try:
        print(f"📋 测试配置: {test_config['configType']}")
        print(f"🔧 组件: {', '.join(test_config['components'])}")
        print(f"📅 时间窗口: {test_config['startDate']} → {test_config['sopDate']}")
        
        print("\n🚀 开始真实 DVP 生成流程...")
        print("⏳ 这将触发真实的进度追踪...")
        
        data_bytes = json.dumps(test_config).encode('utf-8')
        req = urllib.request.Request(api_url, data_bytes, headers)
        
        start_time = time.time()
        
        with urllib.request.urlopen(req, timeout=120) as response:
            response_time = time.time() - start_time
            result = json.loads(response.read().decode('utf-8'))
        
        if result.get('success'):
            data = result['data']
            print(f"\n✅ DVP 生成成功!")
            print(f"⏱️ 总响应时间: {response_time:.2f}秒")
            print(f"📋 方案ID: {data['id']}")
            print(f"🚗 车辆数量: {data['summary']['totalVehicles']}台")
            print(f"📊 匹配项目: {data['summary']['totalProjects']}个")
            print(f"⏰ 测试周期: {data['summary']['totalDuration']}天")
            
            print(f"\n📋 匹配的测试项目:")
            for i, project in enumerate(data['matchedProjects'][:3], 1):
                print(f"  {i}. {project['name']}")
                print(f"     - 匹配度: {project['matchRatio']*100:.1f}%")
                print(f"     - 测试周期: {project['testCycle']}天")
            
            print(f"\n🎉 真实进度追踪测试完成！")
            print(f"💡 现在可以在浏览器中查看完整的进度追踪效果")
            return True
        else:
            print(f"❌ DVP 生成失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 真实进度追踪测试")
    print("=" * 60)
    print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    print("📝 测试说明:")
    print("  1. 这将发送真实的 DVP 生成请求")
    print("  2. 后端将执行真实的处理流程")
    print("  3. 前端界面将显示真实的进度追踪")
    print("  4. 日志将反映真实的系统交互")
    print()
    
    success = test_real_dvp_generation()
    
    print("\n📊 测试结果:")
    if success:
        print("✅ 真实进度追踪系统工作正常")
        print("🎯 建议在浏览器中测试完整的用户体验")
    else:
        print("❌ 测试失败，请检查服务器状态")
    
    print("\n💡 使用提示:")
    print("  - 打开浏览器访问主界面")
    print("  - 选择配置后点击'生成DVP方案'")
    print("  - 观察进度轨道图的实时状态变化")
    print("  - 查看终端日志的真实信息流")

if __name__ == "__main__":
    main()
