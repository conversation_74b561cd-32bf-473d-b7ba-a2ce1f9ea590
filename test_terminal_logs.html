<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>终端日志测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        
        .test-controls {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-btn {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 14px;
        }
        
        .test-btn:hover {
            background: #3730a3;
        }
        
        /* 终端日志样式 */
        .terminal-background {
            background: rgba(0,0,0,0.9);
            border-radius: 12px;
            overflow: hidden;
            margin-top: 20px;
            min-height: 400px;
            position: relative;
        }

        .terminal-header {
            background: #1f2937;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #374151;
        }

        .terminal-title {
            color: #10b981;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            font-weight: bold;
        }

        .terminal-time {
            color: #6b7280;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .terminal-content {
            padding: 16px;
            height: 350px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            background: rgba(0,0,0,0.3);
        }

        .log-line {
            color: #10b981;
            margin-bottom: 4px;
            opacity: 0.8;
            animation: fadeIn 0.5s ease-in;
        }

        .log-line.error {
            color: #ef4444;
        }

        .log-line.warning {
            color: #f59e0b;
        }

        .log-line.info {
            color: #3b82f6;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 0.8; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖥️ 终端日志显示测试</h1>
        
        <div class="test-controls">
            <button class="test-btn" onclick="addTestLog()">添加测试日志</button>
            <button class="test-btn" onclick="addErrorLog()">添加错误日志</button>
            <button class="test-btn" onclick="addWarningLog()">添加警告日志</button>
            <button class="test-btn" onclick="clearLogs()">清空日志</button>
            <button class="test-btn" onclick="startAutoTest()">自动测试</button>
        </div>
        
        <!-- 终端日志 -->
        <div class="terminal-background">
            <div class="terminal-header">
                <div class="terminal-title">🖥️ DVP-AI 系统实时日志</div>
                <div class="terminal-time" id="terminalTime"></div>
            </div>
            <div class="terminal-content" id="terminalLogs">
                <div class="log-line">系统初始化完成...</div>
                <div class="log-line">等待用户请求...</div>
            </div>
        </div>
    </div>

    <script>
        // 添加终端日志
        function addRealTerminalLog(message, type = 'info') {
            const terminalLogs = document.getElementById('terminalLogs');
            if (!terminalLogs) {
                console.error('terminalLogs element not found!');
                return;
            }

            const logLine = document.createElement('div');
            logLine.className = `log-line ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            logLine.textContent = `[${timestamp}] ${message}`;
            
            terminalLogs.appendChild(logLine);
            
            // 自动滚动到底部
            terminalLogs.scrollTop = terminalLogs.scrollHeight;
            
            // 限制日志行数
            const logLines = terminalLogs.querySelectorAll('.log-line');
            if (logLines.length > 50) {
                logLines[0].remove();
            }
        }

        // 更新终端时间
        function updateTerminalTime() {
            const terminalTime = document.getElementById('terminalTime');
            if (terminalTime) {
                terminalTime.textContent = new Date().toLocaleString();
            }
        }

        // 测试函数
        function addTestLog() {
            addRealTerminalLog('这是一条测试日志信息', 'info');
        }

        function addErrorLog() {
            addRealTerminalLog('这是一条错误日志信息', 'error');
        }

        function addWarningLog() {
            addRealTerminalLog('这是一条警告日志信息', 'warning');
        }

        function clearLogs() {
            const terminalLogs = document.getElementById('terminalLogs');
            if (terminalLogs) {
                terminalLogs.innerHTML = `
                    <div class="log-line">系统初始化完成...</div>
                    <div class="log-line">等待用户请求...</div>
                `;
            }
        }

        function startAutoTest() {
            const messages = [
                { msg: '🚀 DVP生成流程启动', type: 'info' },
                { msg: '📋 配置类型: ADAS L2级配置', type: 'info' },
                { msg: '🔧 选中组件: 前摄像头, 毫米波雷达', type: 'info' },
                { msg: '🌐 正在连接后端API服务器...', type: 'info' },
                { msg: '✅ API连接成功', type: 'info' },
                { msg: '🗄️ 正在查询Neo4j知识图谱...', type: 'info' },
                { msg: '✅ 知识图谱查询完成', type: 'info' },
                { msg: '🧠 正在调用DeepSeek-R1大模型...', type: 'info' },
                { msg: '⚠️ LLM响应超时，使用备用计算', type: 'warning' },
                { msg: '📊 正在进行车辆资源计算...', type: 'info' },
                { msg: '✅ 车辆计算完成', type: 'info' },
                { msg: '🎉 DVP方案生成完成！', type: 'info' }
            ];

            let index = 0;
            const interval = setInterval(() => {
                if (index < messages.length) {
                    addRealTerminalLog(messages[index].msg, messages[index].type);
                    index++;
                } else {
                    clearInterval(interval);
                }
            }, 800);
        }

        // 初始化
        updateTerminalTime();
        setInterval(updateTerminalTime, 1000);
        
        // 添加初始测试日志
        setTimeout(() => {
            addRealTerminalLog('终端日志系统已启动', 'info');
        }, 1000);
    </script>
</body>
</html>
