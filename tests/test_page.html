
    <!DOCTYPE html>
    <html>
    <head>
        <title>DVP界面测试</title>
        <style>
            body { font-family: Arial, sans-serif; padding: 20px; }
            .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
            .success { color: green; }
            .error { color: red; }
        </style>
    </head>
    <body>
        <h1>DVP增强界面测试</h1>
        
        <div class="test-section">
            <h2>测试数据</h2>
            <pre>{
  "id": "DVP_TEST_20250707_005915",
  "generatedDate": "2025-07-07T00:59:15.273460",
  "config": {
    "configType": "高端ADAS测试配置",
    "sopDate": "2024-12-31",
    "components": [
      "前摄像头",
      "后摄像头",
      "左侧摄像头",
      "右侧摄像头",
      "前毫米波雷达",
      "后毫米波雷达",
      "左前毫米波雷达",
      "右前毫米波雷达",
      "激光雷达",
      "超声波传感器",
      "ADAS控制器ECU",
      "显示屏HMI"
    ],
    "complexity": "comprehensive",
    "environment": "extreme",
    "priority": "urgent"
  },
  "matchedProjects": [
    {
      "name": "ACC自适应巡航控制",
      "complexity": "高",
      "testCycle": 20,
      "matchRatio": 1.0,
      "aiCalculatedVehicles": 8,
      "matchedComponents": [
        "前摄像头",
        "前毫米波雷达",
        "ADAS控制器ECU"
      ]
    },
    {
      "name": "AEB自动紧急制动",
      "complexity": "高",
      "testCycle": 15,
      "matchRatio": 1.0,
      "aiCalculatedVehicles": 6,
      "matchedComponents": [
        "前摄像头",
        "前毫米波雷达",
        "ADAS控制器ECU"
      ]
    },
    {
      "name": "LDW车道偏离警告",
      "complexity": "低",
      "testCycle": 8,
      "matchRatio": 1.0,
      "aiCalculatedVehicles": 3,
      "matchedComponents": [
        "前摄像头",
        "ADAS控制器ECU"
      ]
    }
  ],
  "summary": {
    "totalVehicles": 58,
    "totalDuration": 150,
    "totalCost": 1124,
    "totalProjects": 10,
    "riskLevel": "高"
  },
  "costAnalysis": {
    "vehicleCost": 1044,
    "operationCost": 80,
    "facilityCost": 90,
    "totalCost": 1124,
    "breakdown": {
      "vehicle": "85.2",
      "operation": "7.1",
      "facility": "7.7"
    }
  }
}</pre>
        </div>
        
        <div class="test-section">
            <h2>功能测试</h2>
            <p><a href="../enhanced_dvp_interface.html" target="_blank">打开增强版DVP界面</a></p>
            <p>测试步骤：</p>
            <ol>
                <li>选择车辆组件配置</li>
                <li>设置测试参数</li>
                <li>点击生成DVP方案</li>
                <li>查看可视化结果</li>
                <li>测试导出功能</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2>预期结果</h2>
            <ul>
                <li>✓ 左右面板布局正确显示</li>
                <li>✓ 数字卡片显示关键指标</li>
                <li>✓ 图表正确渲染</li>
                <li>✓ 甘特图显示项目进度</li>
                <li>✓ 导出功能正常工作</li>
            </ul>
        </div>
        
        <script>
            // 测试JavaScript函数
            console.log('DVP界面测试页面已加载');
            console.log('测试数据:', {
  "id": "DVP_TEST_20250707_005915",
  "generatedDate": "2025-07-07T00:59:15.273460",
  "config": {
    "configType": "高端ADAS测试配置",
    "sopDate": "2024-12-31",
    "components": [
      "前摄像头",
      "后摄像头",
      "左侧摄像头",
      "右侧摄像头",
      "前毫米波雷达",
      "后毫米波雷达",
      "左前毫米波雷达",
      "右前毫米波雷达",
      "激光雷达",
      "超声波传感器",
      "ADAS控制器ECU",
      "显示屏HMI"
    ],
    "complexity": "comprehensive",
    "environment": "extreme",
    "priority": "urgent"
  },
  "matchedProjects": [
    {
      "name": "ACC自适应巡航控制",
      "complexity": "高",
      "testCycle": 20,
      "matchRatio": 1.0,
      "aiCalculatedVehicles": 8,
      "matchedComponents": [
        "前摄像头",
        "前毫米波雷达",
        "ADAS控制器ECU"
      ]
    },
    {
      "name": "AEB自动紧急制动",
      "complexity": "高",
      "testCycle": 15,
      "matchRatio": 1.0,
      "aiCalculatedVehicles": 6,
      "matchedComponents": [
        "前摄像头",
        "前毫米波雷达",
        "ADAS控制器ECU"
      ]
    },
    {
      "name": "LDW车道偏离警告",
      "complexity": "低",
      "testCycle": 8,
      "matchRatio": 1.0,
      "aiCalculatedVehicles": 3,
      "matchedComponents": [
        "前摄像头",
        "ADAS控制器ECU"
      ]
    }
  ],
  "summary": {
    "totalVehicles": 58,
    "totalDuration": 150,
    "totalCost": 1124,
    "totalProjects": 10,
    "riskLevel": "高"
  },
  "costAnalysis": {
    "vehicleCost": 1044,
    "operationCost": 80,
    "facilityCost": 90,
    "totalCost": 1124,
    "breakdown": {
      "vehicle": "85.2",
      "operation": "7.1",
      "facility": "7.7"
    }
  }
});
        </script>
    </body>
    </html>
    